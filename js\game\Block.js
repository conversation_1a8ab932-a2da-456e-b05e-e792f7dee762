const logger = require('../utils/AsyncLogger')

/**
 * 方块类
 * 表示游戏中的每个可点击的方块
 */
class Block {
  /**
   * 构造函数
   * @param {number} type - 方块类型 (0-5)
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} layer - 层级 (0最底层)
   */
  constructor(type, x, y, layer = 0) {
    this.type = type                  // 方块类型 (0-5)
    this.x = x                        // X坐标
    this.y = y                        // Y坐标
    this.layer = layer || 0           // 层级 (0最底层)
    this.width = 55                   // 方块宽度 - 默认大小，会被槽位动态调整
    this.height = 55                  // 方块高度 - 默认大小，会被槽位动态调整
    this.initialWidth = 55            // 方块原始宽度
    this.initialHeight = 55           // 方块原始高度
    this.isVisible = true         // 是否可见
    this.isClickable = true       // 是否可点击
    this.isMoving = false         // 是否正在移动
    this.targetX = x              // 目标X坐标（用于动画）
    this.targetY = y              // 目标Y坐标（用于动画）
    this.moveSpeed = 0.3          // 移动速度
    this.scale = 1                // 缩放比例
    this.alpha = 1                // 透明度
    this.rotation = 0             // 旋转角度
    this.shadowOffset = 3         // 阴影偏移
  }

  /**
   * 更新方块状态
   * @param {number} deltaTime - 时间差
   */
  update(deltaTime) {
    // 更新移动动画
    if (this.isMoving) {
      const dx = this.targetX - this.x
      const dy = this.targetY - this.y
      const distance = Math.sqrt(dx * dx + dy * dy)
      
      if (distance < 2) {
        // 到达目标位置
        this.x = this.targetX
        this.y = this.targetY
        this.isMoving = false
      } else {
        // 继续移动
        this.x += dx * this.moveSpeed
        this.y += dy * this.moveSpeed
      }
    }
  }

  /**
   * 渲染方块
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {ResourceLoader} resources - 资源管理器
   */
  render(ctx, resources) {
    if (!this.isVisible || this.alpha <= 0) return

    ctx.save()
    
    // 设置透明度
    ctx.globalAlpha = this.alpha
    
    // 移动到方块中心点
    const centerX = this.x + this.width / 2
    const centerY = this.y + this.height / 2
    ctx.translate(centerX, centerY)
    
    // 应用缩放和旋转
    ctx.scale(this.scale, this.scale)
    ctx.rotate(this.rotation)
    
    // 绘制投射阴影（多层）
    this.renderDropShadow(ctx)
    
    // 绘制方块背景（立体效果）
    this.render3DBackground(ctx)
    
    // 获取方块图片
    const imageName = `block_${this.type}`
    const image = resources.getImage(imageName)
    
    if (image) {
      // 绘制方块图片
      ctx.drawImage(
        image,
        -this.width / 2,
        -this.height / 2,
        this.width,
        this.height
      )
      
      // 调试模式：在第20关输出成功加载的图片信息
      if (typeof window !== 'undefined' && window.currentLevel === 20 && Math.random() < 0.01) {
      }
    } else {
      // 如果没有图片，绘制3D占位符
      this.render3DPlaceholder(ctx)
      
      // 调试模式：输出缺失图片的信息
      if (typeof window !== 'undefined' && window.currentLevel === 20) {
        logger.warn(`❌ 第20关方块图片缺失: type${this.type} -> ${imageName}，将显示占位符`)
      }
    }
    
    // 绘制3D边框和高光
    this.render3DBorder(ctx)
    
    // 羊了个羊模式：根据方块状态应用不同的视觉效果
    if (!this.isClickable) {
      // 被遮挡的方块：稍微变暗，表示不可点击
      ctx.fillStyle = 'rgba(0, 0, 0, 0.4)'
      ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height)
      
      // 添加不可点击状态的边框
      ctx.strokeStyle = 'rgba(100, 100, 100, 0.8)'
      ctx.lineWidth = 1
      ctx.strokeRect(-this.width / 2 + 1, -this.height / 2 + 1, this.width - 2, this.height - 2)
    } else {
      // 可点击状态：添加轻微的高亮效果
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)'
      ctx.lineWidth = 2
      ctx.strokeRect(-this.width / 2, -this.height / 2, this.width, this.height)
    }
    
    ctx.restore()
  }



  /**
   * 绘制投射阴影
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderDropShadow(ctx) {
    if (this.shadowOffset <= 0) return

    const shadowLayers = [
      { offset: this.shadowOffset + 2, alpha: 0.1, blur: 6 },
      { offset: this.shadowOffset + 1, alpha: 0.15, blur: 3 },
      { offset: this.shadowOffset, alpha: 0.2, blur: 0 }
    ]

    shadowLayers.forEach(layer => {
      ctx.save()
      if (layer.blur > 0) {
        ctx.shadowColor = `rgba(0, 0, 0, ${layer.alpha})`
        ctx.shadowBlur = layer.blur
        ctx.shadowOffsetX = layer.offset
        ctx.shadowOffsetY = layer.offset
        ctx.fillStyle = 'rgba(0, 0, 0, 0.01)' // 透明色，只要阴影
      } else {
        ctx.fillStyle = `rgba(0, 0, 0, ${layer.alpha})`
      }
      
      ctx.fillRect(
        -this.width / 2 + layer.offset,
        -this.height / 2 + layer.offset,
        this.width,
        this.height
      )
      ctx.restore()
    })
  }

  /**
   * 绘制3D背景效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render3DBackground(ctx) {
    const halfWidth = this.width / 2
    const halfHeight = this.height / 2
    
    // 主背景渐变
    const gradient = ctx.createLinearGradient(
      -halfWidth, -halfHeight,
      halfWidth, halfHeight
    )
    
    // 根据方块类型选择渐变色
    const colors = this.getTypeColors()
    gradient.addColorStop(0, colors.light)
    gradient.addColorStop(0.5, colors.base)
    gradient.addColorStop(1, colors.dark)
    
    ctx.fillStyle = gradient
    ctx.fillRect(-halfWidth, -halfHeight, this.width, this.height)
  }

  /**
   * 绘制3D边框效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render3DBorder(ctx) {
    const halfWidth = this.width / 2
    const halfHeight = this.height / 2
    const bevelSize = 3

    // 顶部和左侧高光
    ctx.fillStyle = 'rgba(255, 255, 255, 0.6)'
    // 顶部高光
    ctx.fillRect(-halfWidth, -halfHeight, this.width, bevelSize)
    // 左侧高光
    ctx.fillRect(-halfWidth, -halfHeight, bevelSize, this.height)

    // 底部和右侧阴影
    ctx.fillStyle = 'rgba(0, 0, 0, 0.4)'
    // 底部阴影
    ctx.fillRect(-halfWidth, halfHeight - bevelSize, this.width, bevelSize)
    // 右侧阴影
    ctx.fillRect(halfWidth - bevelSize, -halfHeight, bevelSize, this.height)

    // 外边框
    ctx.strokeStyle = this.isClickable ? '#C0C0C0' : '#808080'
    ctx.lineWidth = 2
    ctx.strokeRect(-halfWidth, -halfHeight, this.width, this.height)

    // 内边框
    ctx.strokeStyle = this.isClickable ? 'rgba(255, 255, 255, 0.8)' : 'rgba(255, 255, 255, 0.4)'
    ctx.lineWidth = 1
    ctx.strokeRect(-halfWidth + 1, -halfHeight + 1, this.width - 2, this.height - 2)

    // 如果可点击，添加金属光泽
    if (this.isClickable) {
      this.renderMetallicGloss(ctx)
    }
  }

  /**
   * 绘制金属光泽效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderMetallicGloss(ctx) {
    const halfWidth = this.width / 2
    const halfHeight = this.height / 2
    
    // 对角线光泽
    const glossGradient = ctx.createLinearGradient(
      -halfWidth, -halfHeight,
      -halfWidth + this.width * 0.6, -halfHeight + this.height * 0.6
    )
    glossGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)')
    glossGradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.1)')
    glossGradient.addColorStop(1, 'rgba(255, 255, 255, 0)')
    
    ctx.fillStyle = glossGradient
    ctx.fillRect(-halfWidth + 2, -halfHeight + 2, this.width - 4, this.height - 4)
  }

  /**
   * 获取方块类型对应的颜色
   * @returns {Object} 包含light, base, dark三种颜色
   */
  getTypeColors() {
    const colorSets = [
      { light: '#FF9999', base: '#FF6B6B', dark: '#E55555' }, // 红色
      { light: '#66E6E6', base: '#4ECDC4', dark: '#44B8B5' }, // 青色
      { light: '#5AC8ED', base: '#45B7D1', dark: '#3A9BC1' }, // 蓝色
      { light: '#A8D8A8', base: '#96CEB4', dark: '#82B8A0' }, // 绿色
      { light: '#FFE4A3', base: '#FFEAA7', dark: '#E6D397' }, // 黄色
      { light: '#E8C4E8', base: '#DDA0DD', dark: '#C78FC7' }, // 紫色
      { light: '#AAE4D4', base: '#98D8C8', dark: '#86C2B4' }, // 薄荷绿
      { light: '#F9E79F', base: '#F7DC6F', dark: '#E6CC5F' }, // 金色
      { light: '#C7A2C7', base: '#BB8FCE', dark: '#A87FBE' }, // 淡紫色
      { light: '#9DD3F5', base: '#85C1E9', dark: '#75B1D9' }  // 天蓝色
    ]
    
    return colorSets[this.type % colorSets.length]
  }

  /**
   * 绘制3D占位符（当图片未加载时）
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render3DPlaceholder(ctx) {
    const halfWidth = this.width / 2
    const halfHeight = this.height / 2
    
    // 绘制图标或图案
    this.renderPlaceholderIcon(ctx)
    
    // 绘制类型数字（半透明背景）
    this.renderTypeNumber(ctx)
  }

  /**
   * 绘制占位符图标
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderPlaceholderIcon(ctx) {
    const halfWidth = this.width / 2
    const halfHeight = this.height / 2
    const colors = this.getTypeColors()
    
    // 根据类型绘制不同的图案
    switch (this.type % 5) {
      case 0: // 圆形
        this.drawCircleIcon(ctx, colors)
        break
      case 1: // 星形
        this.drawStarIcon(ctx, colors)
        break
      case 2: // 方块
        this.drawSquareIcon(ctx, colors)
        break
      case 3: // 三角形
        this.drawTriangleIcon(ctx, colors)
        break
      case 4: // 菱形
        this.drawDiamondIcon(ctx, colors)
        break
    }
  }

  /**
   * 绘制圆形图标
   */
  drawCircleIcon(ctx, colors) {
    const radius = Math.min(this.width, this.height) / 3
    
    // 渐变圆形
    const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, radius)
    gradient.addColorStop(0, colors.light)
    gradient.addColorStop(1, colors.dark)
    
    ctx.fillStyle = gradient
    ctx.beginPath()
    ctx.arc(0, 0, radius, 0, Math.PI * 2)
    ctx.fill()
    
    // 高光
    ctx.fillStyle = 'rgba(255, 255, 255, 0.4)'
    ctx.beginPath()
    ctx.arc(-radius * 0.3, -radius * 0.3, radius * 0.4, 0, Math.PI * 2)
    ctx.fill()
  }

  /**
   * 绘制星形图标
   */
  drawStarIcon(ctx, colors) {
    const radius = Math.min(this.width, this.height) / 3
    const points = 5
    
    ctx.fillStyle = colors.base
    ctx.beginPath()
    
    for (let i = 0; i < points * 2; i++) {
      const angle = (i * Math.PI) / points
      const r = i % 2 === 0 ? radius : radius * 0.5
      const x = Math.cos(angle) * r
      const y = Math.sin(angle) * r
      
      if (i === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    }
    
    ctx.closePath()
    ctx.fill()
    
    // 星形高光
    ctx.fillStyle = colors.light
    ctx.beginPath()
    ctx.moveTo(0, -radius)
    ctx.lineTo(-radius * 0.2, -radius * 0.3)
    ctx.lineTo(-radius * 0.6, -radius * 0.3)
    ctx.closePath()
    ctx.fill()
  }

  /**
   * 绘制方块图标
   */
  drawSquareIcon(ctx, colors) {
    const size = Math.min(this.width, this.height) / 2.5
    
    ctx.fillStyle = colors.base
    ctx.fillRect(-size / 2, -size / 2, size, size)
    
    // 立体效果
    ctx.fillStyle = colors.light
    ctx.fillRect(-size / 2, -size / 2, size, size / 4)
    ctx.fillRect(-size / 2, -size / 2, size / 4, size)
    
    ctx.fillStyle = colors.dark
    ctx.fillRect(-size / 2, size / 2 - size / 4, size, size / 4)
    ctx.fillRect(size / 2 - size / 4, -size / 2, size / 4, size)
  }

  /**
   * 绘制三角形图标
   */
  drawTriangleIcon(ctx, colors) {
    const size = Math.min(this.width, this.height) / 2.5
    
    ctx.fillStyle = colors.base
    ctx.beginPath()
    ctx.moveTo(0, -size / 2)
    ctx.lineTo(-size / 2, size / 2)
    ctx.lineTo(size / 2, size / 2)
    ctx.closePath()
    ctx.fill()
    
    // 高光
    ctx.fillStyle = colors.light
    ctx.beginPath()
    ctx.moveTo(0, -size / 2)
    ctx.lineTo(-size / 4, 0)
    ctx.lineTo(size / 4, 0)
    ctx.closePath()
    ctx.fill()
  }

  /**
   * 绘制菱形图标
   */
  drawDiamondIcon(ctx, colors) {
    const size = Math.min(this.width, this.height) / 3
    
    ctx.fillStyle = colors.base
    ctx.beginPath()
    ctx.moveTo(0, -size)
    ctx.lineTo(size, 0)
    ctx.lineTo(0, size)
    ctx.lineTo(-size, 0)
    ctx.closePath()
    ctx.fill()
    
    // 菱形切面效果
    ctx.fillStyle = colors.light
    ctx.beginPath()
    ctx.moveTo(0, -size)
    ctx.lineTo(size / 2, -size / 2)
    ctx.lineTo(0, 0)
    ctx.lineTo(-size / 2, -size / 2)
    ctx.closePath()
    ctx.fill()
  }

  /**
   * 绘制类型数字
   */
  renderTypeNumber(ctx) {
    // 半透明背景圆圈
    ctx.fillStyle = 'rgba(0, 0, 0, 0.6)'
    ctx.beginPath()
    ctx.arc(this.width / 4, this.height / 4, 8, 0, Math.PI * 2)
    ctx.fill()
    
    // 数字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 12px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(this.type.toString(), this.width / 4, this.height / 4)
  }

  /**
   * 原始占位符方法（向后兼容）
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderPlaceholder(ctx) {
    this.render3DPlaceholder(ctx)
  }

  /**
   * 移动到指定位置
   * @param {number} x - 目标X坐标
   * @param {number} y - 目标Y坐标
   */
  moveTo(x, y) {
    this.targetX = x
    this.targetY = y
    this.isMoving = true
  }

  /**
   * 立即设置位置
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  setPosition(x, y) {
    this.x = x
    this.y = y
    this.targetX = x
    this.targetY = y
    this.isMoving = false
  }

  /**
   * 检查点是否在方块内
   * @param {number} pointX - 点的X坐标
   * @param {number} pointY - 点的Y坐标
   * @returns {boolean}
   */
  containsPoint(pointX, pointY) {
    return pointX >= this.x && 
           pointX <= this.x + this.width &&
           pointY >= this.y && 
           pointY <= this.y + this.height
  }

  /**
   * 播放点击动画
   * @param {AnimationManager} animationManager - 动画管理器
   */
  playClickAnimation(animationManager = null) {
    if (animationManager) {
      // 使用动画管理器播放弹跳动画
      animationManager.animateClickBounce(this)
    } else {
      // 增强的点击动画效果
      this.playEnhancedClickAnimation()
    }
  }

  /**
   * 播放增强的点击动画
   */
  playEnhancedClickAnimation() {
    const originalScale = this.scale
    const originalShadowOffset = this.shadowOffset
    let animationTime = 0
    const duration = 200 // 动画持续时间

    const animate = () => {
      animationTime += 16 // 假设60fps
      const progress = Math.min(animationTime / duration, 1)
      
      if (progress < 0.5) {
        // 前半段：压缩和阴影减小
        const t = progress * 2
        this.scale = originalScale * (1 - 0.15 * Math.sin(t * Math.PI))
        this.shadowOffset = originalShadowOffset * (1 - 0.3 * Math.sin(t * Math.PI))
      } else {
        // 后半段：回弹效果
        const t = (progress - 0.5) * 2
        const bounce = 1 + 0.1 * Math.sin(t * Math.PI * 2) * (1 - t)
        this.scale = originalScale * bounce
        this.shadowOffset = originalShadowOffset
      }
      
      if (progress < 1) {
        requestAnimationFrame(animate)
      } else {
        // 动画结束，恢复原状
        this.scale = originalScale
        this.shadowOffset = originalShadowOffset
      }
    }
    
    animate()
  }

  /**
   * 播放消除动画
   * @param {function} callback - 动画完成回调
   * @param {AnimationManager} animationManager - 动画管理器
   */
  playEliminateAnimation(callback, animationManager = null) {
    if (animationManager) {
      // 使用动画管理器播放消除动画
      animationManager.animateElimination(this, callback)
    } else {
      // 原有的简单动画（向后兼容）
      let startTime = Date.now()
      const duration = 300 // 动画持续时间

      const animate = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)
        
        // 缩放和旋转
        this.scale = 1 - progress
        this.rotation = progress * Math.PI * 2
        this.alpha = 1 - progress
        
        if (progress >= 1) {
          this.isVisible = false
          if (callback) callback()
        } else {
          requestAnimationFrame(animate)
        }
      }
      
      animate()
    }
  }

  /**
   * 重置方块状态
   */
  reset() {
    this.isVisible = true
    this.isClickable = true
    this.isMoving = false
    this.scale = 1
    this.alpha = 1
    this.rotation = 0
  }

  /**
   * 获取方块的边界框
   * @returns {Object} {x, y, width, height}
   */
  getBounds() {
    return {
      x: this.x,
      y: this.y,
      width: this.width,
      height: this.height
    }
  }

  /**
   * 克隆方块
   * @returns {Block}
   */
  clone() {
    const newBlock = new Block(this.type, this.x, this.y, this.layer)
    newBlock.width = this.width
    newBlock.height = this.height
    newBlock.isVisible = this.isVisible
    newBlock.isClickable = this.isClickable
    return newBlock
  }
}

// CommonJS导出
module.exports = Block 