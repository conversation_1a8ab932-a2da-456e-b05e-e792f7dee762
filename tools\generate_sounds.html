<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏音效生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .generator {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        .download-btn {
            background: #2196F3;
        }
        .download-btn:hover {
            background: #1976D2;
        }
        h1 {
            text-align: center;
            color: #333;
        }
        h2 {
            color: #555;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 5px;
        }
        .description {
            color: #666;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>🎮 游戏音效生成器</h1>
    <p style="text-align: center;">为鸭了个鸭呀小游戏生成音效文件</p>

    <div class="generator">
        <h2>🔊 点击音效</h2>
        <p class="description">清脆的点击声音，用于方块点击</p>
        <button onclick="generateClick()">生成点击音效</button>
        <button onclick="playClick()">试听</button>
        <button class="download-btn" onclick="downloadSound('click')">下载 click.mp3</button>
    </div>

    <div class="generator">
        <h2>✨ 匹配音效</h2>
        <p class="description">愉悦的匹配声音，用于三个方块匹配时</p>
        <button onclick="generateMatch()">生成匹配音效</button>
        <button onclick="playMatch()">试听</button>
        <button class="download-btn" onclick="downloadSound('match')">下载 match.mp3</button>
    </div>

    <div class="generator">
        <h2>💥 消除音效</h2>
        <p class="description">满足感的消除声音，用于方块消除时</p>
        <button onclick="generateEliminate()">生成消除音效</button>
        <button onclick="playEliminate()">试听</button>
        <button class="download-btn" onclick="downloadSound('eliminate')">下载 eliminate.mp3</button>
    </div>

    <div class="generator">
        <h2>🎉 胜利音效</h2>
        <p class="description">庆祝胜利的音效，用于关卡通过时</p>
        <button onclick="generateWin()">生成胜利音效</button>
        <button onclick="playWin()">试听</button>
        <button class="download-btn" onclick="downloadSound('win')">下载 win.mp3</button>
    </div>

    <div class="generator">
        <h2>😞 失败音效</h2>
        <p class="description">失败时的音效，用于游戏失败时</p>
        <button onclick="generateLose()">生成失败音效</button>
        <button onclick="playLose()">试听</button>
        <button class="download-btn" onclick="downloadSound('lose')">下载 lose.mp3</button>
    </div>

    <div class="generator">
        <h2>🔀 洗牌音效</h2>
        <p class="description">洗牌时的音效，用于重新排列方块时</p>
        <button onclick="generateShuffle()">生成洗牌音效</button>
        <button onclick="playShuffle()">试听</button>
        <button class="download-btn" onclick="downloadSound('shuffle')">下载 shuffle.mp3</button>
    </div>

    <div class="generator">
        <h2>⚡ 道具音效</h2>
        <p class="description">使用道具时的音效</p>
        <button onclick="generatePowerup()">生成道具音效</button>
        <button onclick="playPowerup()">试听</button>
        <button class="download-btn" onclick="downloadSound('powerup')">下载 powerup.mp3</button>
    </div>

    <div class="generator">
        <h2>🎵 背景音乐</h2>
        <p class="description">轻松愉快的背景音乐循环</p>
        <button onclick="generateBackground()">生成背景音乐</button>
        <button onclick="playBackground()">试听</button>
        <button class="download-btn" onclick="downloadSound('background')">下载 background.mp3</button>
    </div>

    <script>
        let audioContext;
        let sounds = {};

        // 初始化音频上下文
        function initAudioContext() {
            if (!audioContext) {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
            }
            return audioContext;
        }

        // 生成点击音效
        function generateClick() {
            const ctx = initAudioContext();
            const buffer = ctx.createBuffer(1, ctx.sampleRate * 0.1, ctx.sampleRate);
            const data = buffer.getChannelData(0);
            
            for (let i = 0; i < data.length; i++) {
                const t = i / ctx.sampleRate;
                // 短促的高频声音
                data[i] = Math.sin(800 * 2 * Math.PI * t) * Math.exp(-t * 30) * 0.3;
            }
            
            sounds.click = buffer;
            console.log('点击音效生成完成');
        }

        // 生成匹配音效
        function generateMatch() {
            const ctx = initAudioContext();
            const buffer = ctx.createBuffer(1, ctx.sampleRate * 0.3, ctx.sampleRate);
            const data = buffer.getChannelData(0);
            
            for (let i = 0; i < data.length; i++) {
                const t = i / ctx.sampleRate;
                // 上升的音调，愉悦感
                const freq = 400 + t * 200;
                data[i] = Math.sin(freq * 2 * Math.PI * t) * Math.exp(-t * 8) * 0.4;
            }
            
            sounds.match = buffer;
            console.log('匹配音效生成完成');
        }

        // 生成消除音效
        function generateEliminate() {
            const ctx = initAudioContext();
            const buffer = ctx.createBuffer(1, ctx.sampleRate * 0.4, ctx.sampleRate);
            const data = buffer.getChannelData(0);
            
            for (let i = 0; i < data.length; i++) {
                const t = i / ctx.sampleRate;
                // 清脆的爆破声
                const noise = (Math.random() - 0.5) * 0.1;
                const tone = Math.sin(600 * 2 * Math.PI * t) * Math.exp(-t * 12);
                data[i] = (tone + noise) * 0.5;
            }
            
            sounds.eliminate = buffer;
            console.log('消除音效生成完成');
        }

        // 生成胜利音效
        function generateWin() {
            const ctx = initAudioContext();
            const buffer = ctx.createBuffer(1, ctx.sampleRate * 1.5, ctx.sampleRate);
            const data = buffer.getChannelData(0);
            
            for (let i = 0; i < data.length; i++) {
                const t = i / ctx.sampleRate;
                // 胜利的号角声
                let signal = 0;
                signal += Math.sin(523 * 2 * Math.PI * t) * 0.3; // C5
                signal += Math.sin(659 * 2 * Math.PI * t) * 0.3; // E5
                signal += Math.sin(784 * 2 * Math.PI * t) * 0.3; // G5
                
                const envelope = Math.max(0, 1 - t * 0.8);
                data[i] = signal * envelope * 0.4;
            }
            
            sounds.win = buffer;
            console.log('胜利音效生成完成');
        }

        // 生成失败音效
        function generateLose() {
            const ctx = initAudioContext();
            const buffer = ctx.createBuffer(1, ctx.sampleRate * 1.0, ctx.sampleRate);
            const data = buffer.getChannelData(0);
            
            for (let i = 0; i < data.length; i++) {
                const t = i / ctx.sampleRate;
                // 下降的低沉音调
                const freq = 300 - t * 100;
                data[i] = Math.sin(freq * 2 * Math.PI * t) * Math.exp(-t * 2) * 0.4;
            }
            
            sounds.lose = buffer;
            console.log('失败音效生成完成');
        }

        // 生成洗牌音效
        function generateShuffle() {
            const ctx = initAudioContext();
            const buffer = ctx.createBuffer(1, ctx.sampleRate * 0.6, ctx.sampleRate);
            const data = buffer.getChannelData(0);
            
            for (let i = 0; i < data.length; i++) {
                const t = i / ctx.sampleRate;
                // 快速变化的音调模拟洗牌
                const freq = 400 + Math.sin(t * 50) * 200;
                data[i] = Math.sin(freq * 2 * Math.PI * t) * Math.exp(-t * 3) * 0.3;
            }
            
            sounds.shuffle = buffer;
            console.log('洗牌音效生成完成');
        }

        // 生成道具音效
        function generatePowerup() {
            const ctx = initAudioContext();
            const buffer = ctx.createBuffer(1, ctx.sampleRate * 0.5, ctx.sampleRate);
            const data = buffer.getChannelData(0);
            
            for (let i = 0; i < data.length; i++) {
                const t = i / ctx.sampleRate;
                // 神秘的魔法音效
                const freq1 = 440 + Math.sin(t * 20) * 100;
                const freq2 = 660 + Math.sin(t * 30) * 50;
                data[i] = (Math.sin(freq1 * 2 * Math.PI * t) + Math.sin(freq2 * 2 * Math.PI * t)) * Math.exp(-t * 4) * 0.3;
            }
            
            sounds.powerup = buffer;
            console.log('道具音效生成完成');
        }

        // 生成背景音乐
        function generateBackground() {
            const ctx = initAudioContext();
            const buffer = ctx.createBuffer(1, ctx.sampleRate * 8, ctx.sampleRate); // 8秒循环
            const data = buffer.getChannelData(0);
            
            // 简单的旋律循环
            const melody = [523, 587, 659, 698, 784, 698, 659, 587]; // C大调音阶
            const noteDuration = ctx.sampleRate; // 每个音符1秒
            
            for (let i = 0; i < data.length; i++) {
                const t = i / ctx.sampleRate;
                const noteIndex = Math.floor(t) % melody.length;
                const freq = melody[noteIndex];
                const noteTime = t % 1;
                
                // 柔和的正弦波，添加一些泛音
                let signal = Math.sin(freq * 2 * Math.PI * t) * 0.3;
                signal += Math.sin(freq * 2 * 2 * Math.PI * t) * 0.1; // 第二泛音
                
                // 音符包络
                const attack = Math.min(1, noteTime * 10);
                const decay = Math.max(0.3, 1 - noteTime * 0.7);
                
                data[i] = signal * attack * decay * 0.2;
            }
            
            sounds.background = buffer;
            console.log('背景音乐生成完成');
        }

        // 播放音效
        function playSound(soundName) {
            if (!sounds[soundName]) {
                alert('请先生成' + soundName + '音效');
                return;
            }
            
            const ctx = initAudioContext();
            const source = ctx.createBufferSource();
            source.buffer = sounds[soundName];
            source.connect(ctx.destination);
            source.start();
        }

        // 播放函数
        function playClick() { playSound('click'); }
        function playMatch() { playSound('match'); }
        function playEliminate() { playSound('eliminate'); }
        function playWin() { playSound('win'); }
        function playLose() { playSound('lose'); }
        function playShuffle() { playSound('shuffle'); }
        function playPowerup() { playSound('powerup'); }
        function playBackground() { playSound('background'); }

        // 下载音效
        function downloadSound(soundName) {
            if (!sounds[soundName]) {
                alert('请先生成' + soundName + '音效');
                return;
            }

            // 转换为WAV格式
            const buffer = sounds[soundName];
            const wav = audioBufferToWav(buffer);
            const blob = new Blob([wav], { type: 'audio/wav' });
            
            // 创建下载链接
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = soundName + '.wav';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            console.log(soundName + '.wav 下载完成');
        }

        // 将AudioBuffer转换为WAV格式
        function audioBufferToWav(buffer) {
            const length = buffer.length;
            const arrayBuffer = new ArrayBuffer(44 + length * 2);
            const view = new DataView(arrayBuffer);
            const data = buffer.getChannelData(0);

            // WAV文件头
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };

            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, 1, true);
            view.setUint32(24, buffer.sampleRate, true);
            view.setUint32(28, buffer.sampleRate * 2, true);
            view.setUint16(32, 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length * 2, true);

            // 音频数据
            let offset = 44;
            for (let i = 0; i < length; i++) {
                const sample = Math.max(-1, Math.min(1, data[i]));
                view.setInt16(offset, sample * 0x7FFF, true);
                offset += 2;
            }

            return arrayBuffer;
        }

        // 生成所有音效
        function generateAllSounds() {
            generateClick();
            generateMatch();
            generateEliminate();
            generateWin();
            generateLose();
            generateShuffle();
            generatePowerup();
            generateBackground();
            alert('所有音效生成完成！可以点击试听和下载。');
        }

        // 页面加载完成后自动生成所有音效
        window.onload = function() {
            setTimeout(generateAllSounds, 1000);
        };
    </script>

    <div style="text-align: center; margin-top: 30px;">
        <button onclick="generateAllSounds()" style="background: #FF9800; font-size: 16px; padding: 15px 30px;">
            🎯 一键生成所有音效
        </button>
    </div>

    <div style="text-align: center; margin-top: 20px; color: #666;">
        <p>💡 提示：</p>
        <p>1. 点击"生成"按钮创建音效</p>
        <p>2. 点击"试听"预览音效</p>
        <p>3. 点击"下载"保存为WAV文件</p>
        <p>4. 将下载的文件放入游戏的audio目录中</p>
    </div>
</body>
</html> 