/**
 * 粒子系统
 * 用于创建各种庆祝效果：烟花、彩带、星星等
 */
class ParticleSystem {
  constructor() {
    this.particles = []
    this.maxParticles = 200
    this.particlePool = [] // 对象池，提高性能
    
    // 初始化对象池
    for (let i = 0; i < this.maxParticles; i++) {
      this.particlePool.push(this.createParticle())
    }
  }

  /**
   * 创建粒子对象
   * @returns {Object} 粒子对象
   */
  createParticle() {
    return {
      x: 0,
      y: 0,
      vx: 0,
      vy: 0,
      life: 1,
      maxLife: 1,
      size: 1,
      color: '#FFD700',
      type: 'circle',
      rotation: 0,
      rotationSpeed: 0,
      gravity: 0,
      alpha: 1,
      active: false
    }
  }

  /**
   * 从对象池获取粒子
   * @returns {Object|null} 粒子对象或null
   */
  getParticle() {
    for (let particle of this.particlePool) {
      if (!particle.active) {
        particle.active = true
        return particle
      }
    }
    return null
  }

  /**
   * 回收粒子到对象池
   * @param {Object} particle - 要回收的粒子
   */
  recycleParticle(particle) {
    particle.active = false
    const index = this.particles.indexOf(particle)
    if (index > -1) {
      this.particles.splice(index, 1)
    }
  }

  /**
   * 创建烟花爆炸效果
   * @param {number} x - 爆炸中心X坐标
   * @param {number} y - 爆炸中心Y坐标
   * @param {number} count - 粒子数量
   * @param {Array} colors - 颜色数组
   */
  createFirework(x, y, count = 30, colors = ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']) {
    for (let i = 0; i < count; i++) {
      const particle = this.getParticle()
      if (!particle) break

      const angle = (Math.PI * 2 * i) / count + Math.random() * 0.5
      const speed = 2 + Math.random() * 4
      
      particle.x = x
      particle.y = y
      particle.vx = Math.cos(angle) * speed
      particle.vy = Math.sin(angle) * speed
      particle.life = 1
      particle.maxLife = 1 + Math.random() * 2
      particle.size = 2 + Math.random() * 4
      particle.color = colors[Math.floor(Math.random() * colors.length)]
      particle.type = 'circle'
      particle.gravity = 0.1
      particle.alpha = 1

      this.particles.push(particle)
    }
  }

  /**
   * 创建彩带飘落效果
   * @param {number} screenWidth - 屏幕宽度
   * @param {number} count - 彩带数量
   */
  createConfetti(screenWidth, count = 20) {
    const colors = ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98FB98']
    
    for (let i = 0; i < count; i++) {
      const particle = this.getParticle()
      if (!particle) break

      particle.x = Math.random() * screenWidth
      particle.y = -20
      particle.vx = (Math.random() - 0.5) * 2
      particle.vy = 1 + Math.random() * 2
      particle.life = 1
      particle.maxLife = 3 + Math.random() * 2
      particle.size = 3 + Math.random() * 5
      particle.color = colors[Math.floor(Math.random() * colors.length)]
      particle.type = 'rectangle'
      particle.rotation = Math.random() * Math.PI * 2
      particle.rotationSpeed = (Math.random() - 0.5) * 0.2
      particle.gravity = 0.05
      particle.alpha = 1

      this.particles.push(particle)
    }
  }

  /**
   * 创建星星闪烁效果
   * @param {number} screenWidth - 屏幕宽度
   * @param {number} screenHeight - 屏幕高度
   * @param {number} count - 星星数量
   */
  createStars(screenWidth, screenHeight, count = 15) {
    for (let i = 0; i < count; i++) {
      const particle = this.getParticle()
      if (!particle) break

      particle.x = Math.random() * screenWidth
      particle.y = Math.random() * screenHeight * 0.6 + 50
      particle.vx = 0
      particle.vy = 0
      particle.life = 1
      particle.maxLife = 2 + Math.random() * 3
      particle.size = 2 + Math.random() * 3
      particle.color = '#FFD700'
      particle.type = 'star'
      particle.rotation = 0
      particle.rotationSpeed = 0.1
      particle.gravity = 0
      particle.alpha = 0.8 + Math.random() * 0.2

      this.particles.push(particle)
    }
  }

  /**
   * 创建光环扩散效果
   * @param {number} x - 中心X坐标
   * @param {number} y - 中心Y坐标
   */
  createRipple(x, y) {
    const particle = this.getParticle()
    if (!particle) return

    particle.x = x
    particle.y = y
    particle.vx = 0
    particle.vy = 0
    particle.life = 1
    particle.maxLife = 1.5
    particle.size = 0
    particle.color = '#FFD700'
    particle.type = 'ripple'
    particle.rotation = 0
    particle.rotationSpeed = 0
    particle.gravity = 0
    particle.alpha = 0.6

    this.particles.push(particle)
  }

  /**
   * 更新所有粒子
   * @param {number} deltaTime - 时间差
   */
  update(deltaTime) {
    const dt = deltaTime / 1000 // 转换为秒

    for (let i = this.particles.length - 1; i >= 0; i--) {
      const particle = this.particles[i]
      
      // 更新位置
      particle.x += particle.vx
      particle.y += particle.vy
      
      // 应用重力
      particle.vy += particle.gravity
      
      // 更新旋转
      particle.rotation += particle.rotationSpeed
      
      // 更新生命周期
      particle.life -= dt
      
      // 更新透明度（基于生命周期）
      const lifeRatio = particle.life / particle.maxLife
      if (particle.type === 'ripple') {
        // 光环效果：大小增长，透明度减少
        particle.size = (1 - lifeRatio) * 100
        particle.alpha = lifeRatio * 0.6
      } else if (particle.type === 'star') {
        // 星星闪烁效果
        particle.alpha = 0.5 + Math.sin(Date.now() * 0.01 + particle.x) * 0.5
      } else {
        // 其他粒子淡出
        particle.alpha = lifeRatio
      }
      
      // 移除死亡的粒子
      if (particle.life <= 0) {
        this.recycleParticle(particle)
        i-- // 因为数组长度改变了，需要调整索引
      }
    }
  }

  /**
   * 渲染所有粒子
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    ctx.save()
    
    for (let particle of this.particles) {
      if (particle.alpha <= 0) continue
      
      ctx.save()
      ctx.globalAlpha = particle.alpha
      ctx.fillStyle = particle.color
      
      if (particle.type === 'circle') {
        // 圆形粒子
        ctx.beginPath()
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
        ctx.fill()
      } else if (particle.type === 'rectangle') {
        // 矩形彩带
        ctx.translate(particle.x, particle.y)
        ctx.rotate(particle.rotation)
        ctx.fillRect(-particle.size / 2, -particle.size / 4, particle.size, particle.size / 2)
      } else if (particle.type === 'star') {
        // 星形
        this.drawStar(ctx, particle.x, particle.y, particle.size, 5)
      } else if (particle.type === 'ripple') {
        // 光环
        ctx.strokeStyle = particle.color
        ctx.lineWidth = 3
        ctx.beginPath()
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
        ctx.stroke()
      }
      
      ctx.restore()
    }
    
    ctx.restore()
  }

  /**
   * 绘制星形
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} size - 大小
   * @param {number} points - 星形角数
   */
  drawStar(ctx, x, y, size, points) {
    const outerRadius = size
    const innerRadius = size * 0.4
    
    ctx.save()
    ctx.translate(x, y)
    ctx.beginPath()
    
    for (let i = 0; i < points * 2; i++) {
      const angle = (i * Math.PI) / points
      const radius = i % 2 === 0 ? outerRadius : innerRadius
      const px = Math.cos(angle) * radius
      const py = Math.sin(angle) * radius
      
      if (i === 0) {
        ctx.moveTo(px, py)
      } else {
        ctx.lineTo(px, py)
      }
    }
    
    ctx.closePath()
    ctx.fill()
    ctx.restore()
  }

  /**
   * 清除所有粒子
   */
  clear() {
    for (let particle of this.particles) {
      this.recycleParticle(particle)
    }
  }

  /**
   * 获取活动粒子数量
   * @returns {number} 活动粒子数量
   */
  getActiveParticleCount() {
    return this.particles.length
  }
}

// CommonJS导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ParticleSystem
}
