/**
 * 按钮类
 * 用于创建可点击的UI按钮
 */
class Button {
  /**
   * 构造函数
   * @param {number} x - 按钮X坐标
   * @param {number} y - 按钮Y坐标
   * @param {number} width - 按钮宽度
   * @param {number} height - 按钮高度
   * @param {string} text - 按钮文字
   * @param {function} onClick - 点击回调函数
   */
  constructor(x, y, width, height, text, onClick) {
    this.x = x
    this.y = y
    this.width = width
    this.height = height
    this.text = text
    this.onClick = onClick
    
    // 按钮状态
    this.isPressed = false
    this.isHovered = false
    this.isEnabled = true
    this.isVisible = true
    
    // 样式配置
    this.backgroundColor = '#4CAF50'
    this.hoverColor = '#45a049'
    this.pressedColor = '#3d8b40'
    this.disabledColor = '#cccccc'
    this.textColor = '#FFFFFF'
    this.borderColor = '#2e7d2e'
    this.borderWidth = 2
    this.borderRadius = 8
    this.fontSize = 16
    this.fontFamily = 'Arial'
    
    // 动画属性
    this.scale = 1
    this.targetScale = 1
  }

  /**
   * 检查点是否在按钮内
   * @param {number} x - 点的X坐标
   * @param {number} y - 点的Y坐标
   * @returns {boolean}
   */
  containsPoint(x, y) {
    return x >= this.x && 
           x <= this.x + this.width &&
           y >= this.y && 
           y <= this.y + this.height
  }

  /**
   * 处理点击事件
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {boolean} 是否处理了点击
   */
  handleClick(x, y) {
    if (!this.isEnabled || !this.isVisible) {
      return false
    }

    if (this.containsPoint(x, y)) {
      this.playClickAnimation()
      if (this.onClick) {
        this.onClick()
      }
      return true
    }
    return false
  }

  /**
   * 播放点击动画
   */
  playClickAnimation() {
    this.targetScale = 0.9
    setTimeout(() => {
      this.targetScale = 1
    }, 100)
  }

  /**
   * 设置按钮状态
   * @param {boolean} enabled - 是否启用
   */
  setEnabled(enabled) {
    this.isEnabled = enabled
  }

  /**
   * 设置按钮可见性
   * @param {boolean} visible - 是否可见
   */
  setVisible(visible) {
    this.isVisible = visible
  }

  /**
   * 设置按钮文字
   * @param {string} text - 新文字
   */
  setText(text) {
    this.text = text
  }

  /**
   * 设置按钮位置
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  setPosition(x, y) {
    this.x = x
    this.y = y
  }

  /**
   * 设置按钮颜色主题
   * @param {string} theme - 主题类型 ('green', 'blue', 'red', 'orange')
   */
  setTheme(theme) {
    switch (theme) {
      case 'green':
        this.backgroundColor = '#4CAF50'
        this.hoverColor = '#45a049'
        this.pressedColor = '#3d8b40'
        this.borderColor = '#2e7d2e'
        break
      case 'blue':
        this.backgroundColor = '#2196F3'
        this.hoverColor = '#1976D2'
        this.pressedColor = '#1565C0'
        this.borderColor = '#1565C0'
        break
      case 'red':
        this.backgroundColor = '#f44336'
        this.hoverColor = '#d32f2f'
        this.pressedColor = '#c62828'
        this.borderColor = '#c62828'
        break
      case 'orange':
        this.backgroundColor = '#FF9800'
        this.hoverColor = '#F57C00'
        this.pressedColor = '#E65100'
        this.borderColor = '#E65100'
        break
    }
  }

  /**
   * 更新按钮状态
   * @param {number} deltaTime - 时间差
   */
  update(deltaTime) {
    // 更新缩放动画
    const scaleDiff = this.targetScale - this.scale
    if (Math.abs(scaleDiff) > 0.01) {
      this.scale += scaleDiff * 0.3
    } else {
      this.scale = this.targetScale
    }
  }

  /**
   * 渲染按钮
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    if (!this.isVisible) return

    ctx.save()

    // 应用缩放
    const centerX = this.x + this.width / 2
    const centerY = this.y + this.height / 2
    ctx.translate(centerX, centerY)
    ctx.scale(this.scale, this.scale)
    ctx.translate(-centerX, -centerY)

    // 选择颜色
    let bgColor = this.backgroundColor
    if (!this.isEnabled) {
      bgColor = this.disabledColor
    } else if (this.isPressed) {
      bgColor = this.pressedColor
    } else if (this.isHovered) {
      bgColor = this.hoverColor
    }

    // 绘制按钮背景（圆角矩形）
    this.drawRoundedRect(ctx, this.x, this.y, this.width, this.height, this.borderRadius, bgColor)

    // 绘制边框
    if (this.borderWidth > 0) {
      ctx.strokeStyle = this.isEnabled ? this.borderColor : this.disabledColor
      ctx.lineWidth = this.borderWidth
      this.strokeRoundedRect(ctx, this.x, this.y, this.width, this.height, this.borderRadius)
    }

    // 绘制文字
    this.renderText(ctx)

    ctx.restore()
  }

  /**
   * 绘制圆角矩形
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} radius - 圆角半径
   * @param {string} color - 填充颜色
   */
  drawRoundedRect(ctx, x, y, width, height, radius, color) {
    ctx.fillStyle = color
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
    ctx.fill()
  }

  /**
   * 绘制圆角矩形边框
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} radius - 圆角半径
   */
  strokeRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
    ctx.stroke()
  }

  /**
   * 渲染按钮文字
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderText(ctx) {
    ctx.fillStyle = this.isEnabled ? this.textColor : '#999999'
    ctx.font = `${this.fontSize}px ${this.fontFamily}`
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    
    const centerX = this.x + this.width / 2
    const centerY = this.y + this.height / 2
    
    ctx.fillText(this.text, centerX, centerY)
  }

  /**
   * 获取按钮的边界框
   * @returns {Object} {x, y, width, height}
   */
  getBounds() {
    return {
      x: this.x,
      y: this.y,
      width: this.width,
      height: this.height
    }
  }
}

// CommonJS导出
module.exports = Button 