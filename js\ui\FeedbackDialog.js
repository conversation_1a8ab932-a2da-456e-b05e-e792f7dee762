const Button = require('./Button.js')
const logger = require('../utils/AsyncLogger')


/**
 * 意见反馈对话框类
 * 提供用户输入意见并提交的功能
 */
class FeedbackDialog {
  /**
   * 构造函数
   * @param {number} x - 对话框X坐标
   * @param {number} y - 对话框Y坐标
   * @param {number} width - 对话框宽度
   * @param {number} height - 对话框高度
   * @param {Object} callbacks - 回调函数集合
   */
  constructor(x, y, width, height, callbacks) {
    this.x = x
    this.y = y
    this.width = width
    this.height = height
    this.callbacks = callbacks || {}
    
    // 对话框状态
    this.isVisible = false
    this.isAnimating = false
    this.isSubmitting = false
    
    // 动画属性
    this.scale = 0
    this.targetScale = 0
    this.alpha = 0
    this.targetAlpha = 0
    
    // 输入框属性
    this.feedbackText = ''
    this.isInputFocused = false
    this.cursorVisible = true
    this.cursorTimer = 0
    this.maxLength = 500
    
    // 样式配置
    this.backgroundColor = 'rgba(0, 0, 0, 0.8)'
    this.dialogColor = 'rgba(255, 255, 255, 0.98)'
    this.borderRadius = 15
    this.padding = 25
    
    // 创建UI组件
    this.createUIComponents()
  }

  /**
   * 创建UI组件
   */
  createUIComponents() {
    const buttonWidth = 100
    const buttonHeight = 40
    const buttonSpacing = 20
    
    // 计算按钮位置
    const centerX = this.x + this.width / 2
    const bottomY = this.y + this.height - this.padding - buttonHeight
    
    // 提交按钮
    this.submitButton = new Button(
      centerX - buttonWidth - buttonSpacing / 2,
      bottomY,
      buttonWidth,
      buttonHeight,
      '提交',
      () => this.submitFeedback()
    )
    this.submitButton.setTheme('green')
    
    // 取消按钮
    this.cancelButton = new Button(
      centerX + buttonSpacing / 2,
      bottomY,
      buttonWidth,
      buttonHeight,
      '取消',
      () => this.hide()
    )
    this.cancelButton.setTheme('red')
    
    // 初始化按钮可见性
    this.updateButtonsVisibility()
  }

  /**
   * 显示对话框
   */
  show() {
    if (this.isVisible) return
    
    this.isVisible = true
    this.isAnimating = true
    this.targetScale = 1
    this.targetAlpha = 1
    this.feedbackText = ''
    this.isInputFocused = true
    
    this.updateButtonsVisibility()
    
    // 播放显示音效
    if (this.callbacks.onShow) {
      this.callbacks.onShow()
    }
  }

  /**
   * 隐藏对话框
   */
  hide() {
    if (!this.isVisible || this.isSubmitting) return
    
    this.isAnimating = true
    this.targetScale = 0
    this.targetAlpha = 0
    this.isInputFocused = false
    
    // 延迟设置不可见状态
    setTimeout(() => {
      this.isVisible = false
      this.isAnimating = false
      this.updateButtonsVisibility()
    }, 300)
    
    // 播放隐藏音效
    if (this.callbacks.onHide) {
      this.callbacks.onHide()
    }
  }

  /**
   * 提交反馈
   */
  async submitFeedback() {
    if (this.isSubmitting) return
    
    let trimmedText = this.feedbackText.trim()
    if (!trimmedText) {
      // 如果输入为空，使用默认反馈内容
      trimmedText = '用户提交了游戏反馈（未填写具体内容）'
    }
    
    try {
      this.isSubmitting = true
      this.submitButton.setText('提交中...')
      this.submitButton.setEnabled(false)
      
      // 调用提交回调
      if (this.callbacks.onSubmit) {
        await this.callbacks.onSubmit(trimmedText)
      }
      
      // 提交成功，隐藏对话框
      this.hide()
      
      // 提交成功回调
      if (this.callbacks.onSuccess) {
        this.callbacks.onSuccess()
      }
      
    } catch (error) {
      // 提交失败
      logger.error('提交反馈失败:', error)
      
      if (this.callbacks.onError) {
        this.callbacks.onError('提交失败，请稍后重试')
      }
    } finally {
      this.isSubmitting = false
      this.submitButton.setText('提交')
      this.submitButton.setEnabled(true)
    }
  }

  /**
   * 设置输入文本（用于小程序环境的输入）
   * @param {string} text - 输入的文本
   */
  setInputText(text) {
    this.feedbackText = text.substring(0, this.maxLength)
  }

  /**
   * 获取输入文本
   * @returns {string} 当前输入的文本
   */
  getInputText() {
    return this.feedbackText
  }

  /**
   * 更新按钮可见性
   */
  updateButtonsVisibility() {
    const visible = this.isVisible || this.isAnimating
    
    this.submitButton.setVisible(visible)
    this.cancelButton.setVisible(visible)
  }

  /**
   * 处理点击事件
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {boolean} 是否处理了点击
   */
  handleClick(x, y) {
    if (!this.isVisible) return false
    
    // 检查是否点击了按钮
    if (this.submitButton.handleClick(x, y)) return true
    if (this.cancelButton.handleClick(x, y)) return true
    
    // 检查是否点击了输入框区域
    const inputArea = this.getInputArea()
    if (x >= inputArea.x && x <= inputArea.x + inputArea.width &&
        y >= inputArea.y && y <= inputArea.y + inputArea.height) {
      this.isInputFocused = true
      
      // 在小程序环境中触发输入，提供更好的用户体验
      if (this.callbacks.onInputFocus) {
        // 立即调用输入回调，不需要等待
        this.callbacks.onInputFocus(this.feedbackText)
      }
      
      return true
    }
    
    // 检查是否点击了对话框外部
    if (!this.containsPoint(x, y)) {
      this.hide()
      return true
    }
    
    return false
  }

  /**
   * 检查点是否在对话框内
   * @param {number} x - 点的X坐标
   * @param {number} y - 点的Y坐标
   * @returns {boolean}
   */
  containsPoint(x, y) {
    return x >= this.x && 
           x <= this.x + this.width &&
           y >= this.y && 
           y <= this.y + this.height
  }

  /**
   * 获取输入区域的位置和大小
   * @returns {Object} 输入区域信息
   */
  getInputArea() {
    return {
      x: this.x + this.padding,
      y: this.y + this.padding + 60,
      width: this.width - this.padding * 2,
      height: 120
    }
  }

  /**
   * 更新对话框状态
   * @param {number} deltaTime - 时间差（毫秒）
   */
  update(deltaTime) {
    // 更新缩放动画
    const scaleDiff = this.targetScale - this.scale
    if (Math.abs(scaleDiff) > 0.01) {
      this.scale += scaleDiff * 0.15
    } else {
      this.scale = this.targetScale
    }

    // 更新透明度动画
    const alphaDiff = this.targetAlpha - this.alpha
    if (Math.abs(alphaDiff) > 0.01) {
      this.alpha += alphaDiff * 0.15
    } else {
      this.alpha = this.targetAlpha
    }

    // 更新光标闪烁
    this.cursorTimer += deltaTime
    if (this.cursorTimer >= 500) {
      this.cursorVisible = !this.cursorVisible
      this.cursorTimer = 0
    }

    // 更新按钮
    if (this.isVisible || this.isAnimating) {
      this.submitButton.update(deltaTime)
      this.cancelButton.update(deltaTime)
    }
  }

  /**
   * 渲染对话框
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    if (!this.isVisible && !this.isAnimating) return
    if (this.alpha <= 0) return

    ctx.save()

    // 设置整体透明度
    ctx.globalAlpha = this.alpha

    // 绘制背景遮罩
    this.drawBackgroundMask(ctx)

    // 应用缩放动画
    const centerX = this.x + this.width / 2
    const centerY = this.y + this.height / 2
    ctx.translate(centerX, centerY)
    ctx.scale(this.scale, this.scale)
    ctx.translate(-centerX, -centerY)

    // 绘制对话框背景
    this.drawDialogBackground(ctx)

    // 绘制标题
    this.drawTitle(ctx)

    // 绘制输入框
    this.drawInputArea(ctx)

    // 绘制字符计数
    this.drawCharacterCount(ctx)

    // 绘制按钮
    if (this.scale > 0.5) {
      this.submitButton.render(ctx)
      this.cancelButton.render(ctx)
    }

    ctx.restore()
  }

  /**
   * 绘制背景遮罩
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  drawBackgroundMask(ctx) {
    ctx.fillStyle = this.backgroundColor
    ctx.fillRect(0, 0, ctx.canvas.width, ctx.canvas.height)
  }

  /**
   * 绘制对话框背景
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  drawDialogBackground(ctx) {
    // 绘制圆角矩形背景
    this.drawRoundedRect(ctx, this.x, this.y, this.width, this.height, this.borderRadius, this.dialogColor)
    
    // 绘制边框
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)'
    ctx.lineWidth = 2
    this.strokeRoundedRect(ctx, this.x, this.y, this.width, this.height, this.borderRadius)
  }

  /**
   * 绘制标题
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  drawTitle(ctx) {
    ctx.fillStyle = '#333333'
    ctx.font = 'bold 20px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    
    const titleX = this.x + this.width / 2
    const titleY = this.y + this.padding + 15
    
    ctx.fillText('意见反馈', titleX, titleY)
    
    // 绘制副标题
    ctx.fillStyle = '#666666'
    ctx.font = '14px Arial'
    const subtitleY = titleY + 25
    ctx.fillText('点击输入框输入内容，或直接提交反馈', titleX, subtitleY)
  }

  /**
   * 绘制输入区域
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  drawInputArea(ctx) {
    const inputArea = this.getInputArea()
    
    // 绘制输入框背景
    ctx.fillStyle = '#ffffff'
    this.drawRoundedRect(ctx, inputArea.x, inputArea.y, inputArea.width, inputArea.height, 8)
    
    // 绘制输入框边框
    ctx.strokeStyle = this.isInputFocused ? '#4CAF50' : '#ddd'
    ctx.lineWidth = 2
    this.strokeRoundedRect(ctx, inputArea.x, inputArea.y, inputArea.width, inputArea.height, 8)
    
    // 绘制提示文字或输入内容
    ctx.fillStyle = this.feedbackText ? '#333333' : '#999999'
    ctx.font = '16px Arial'
    ctx.textAlign = 'left'
    ctx.textBaseline = 'top'
    
    const textPadding = 12
    const textX = inputArea.x + textPadding
    const textY = inputArea.y + textPadding
    const textWidth = inputArea.width - textPadding * 2
    
    if (this.feedbackText) {
      // 绘制输入的文字（支持多行）
      this.drawMultiLineText(ctx, this.feedbackText, textX, textY, textWidth, 20)
      
      // 绘制光标
      if (this.isInputFocused && this.cursorVisible) {
        const lines = this.feedbackText.split('\n')
        const lastLine = lines[lines.length - 1]
        const cursorX = textX + ctx.measureText(lastLine).width
        const cursorY = textY + (lines.length - 1) * 20
        
        ctx.strokeStyle = '#333333'
        ctx.lineWidth = 1
        ctx.beginPath()
        ctx.moveTo(cursorX, cursorY)
        ctx.lineTo(cursorX, cursorY + 18)
        ctx.stroke()
      }
    } else {
      // 绘制提示文字
      ctx.fillText('点击此处输入您的意见和建议...', textX, textY)
    }
  }

  /**
   * 绘制多行文本
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {string} text - 文本内容
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} maxWidth - 最大宽度
   * @param {number} lineHeight - 行高
   */
  drawMultiLineText(ctx, text, x, y, maxWidth, lineHeight) {
    const lines = text.split('\n')
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      const words = line.split('')
      let currentLine = ''
      let currentY = y + i * lineHeight
      
      for (let j = 0; j < words.length; j++) {
        const testLine = currentLine + words[j]
        const metrics = ctx.measureText(testLine)
        
        if (metrics.width > maxWidth && currentLine !== '') {
          ctx.fillText(currentLine, x, currentY)
          currentLine = words[j]
          currentY += lineHeight
        } else {
          currentLine = testLine
        }
      }
      
      if (currentLine) {
        ctx.fillText(currentLine, x, currentY)
      }
    }
  }

  /**
   * 绘制字符计数
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  drawCharacterCount(ctx) {
    const count = this.feedbackText.length
    const countText = `${count}/${this.maxLength}`
    
    ctx.fillStyle = count >= this.maxLength ? '#f44336' : '#999999'
    ctx.font = '12px Arial'
    ctx.textAlign = 'right'
    ctx.textBaseline = 'middle'
    
    const countX = this.x + this.width - this.padding
    const countY = this.y + this.height - this.padding - 60
    
    ctx.fillText(countText, countX, countY)
  }

  /**
   * 绘制圆角矩形
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} radius - 圆角半径
   * @param {string} color - 填充颜色
   */
  drawRoundedRect(ctx, x, y, width, height, radius, color) {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
    
    if (color) {
      ctx.fillStyle = color
      ctx.fill()
    }
  }

  /**
   * 描边圆角矩形
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} radius - 圆角半径
   */
  strokeRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
    ctx.stroke()
  }
}

module.exports = FeedbackDialog