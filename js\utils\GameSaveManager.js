const logger = require('./AsyncLogger')

// 延迟加载GameConfig，避免循环依赖
let gameConfig = null
function getGameConfig() {
  if (!gameConfig) {
    try {
      gameConfig = require('./GameConfig')
    } catch (e) {
      // 如果GameConfig还未加载，返回null
      return null
    }
  }
  return gameConfig
}

/**
 * 游戏存档管理器
 * 负责保存和加载游戏进度数据
 * 支持基于用户ID的多用户存档
 */
class GameSaveManager {
  constructor() {
    this.baseSaveKey = 'duckduck_game_save'
    this.userManager = null
    this.currentUserId = null

    // 缓存相关
    this.saveDataCache = null
    this.cacheTimestamp = 0
    this.cacheExpireTime = 5000 // 缓存5秒过期
    this.lastLoggedSaveKey = null // 记录上次打印日志的saveKey，避免重复打印

    this.defaultSaveData = {
      currentLevel: 1,          // 当前解锁的关卡
      maxLevel: 1,              // 最高解锁关卡
      totalScore: 0,            // 总得分
      playTime: 0,              // 总游戏时间（秒）
      achievements: [],         // 成就列表
      settings: {
        soundEnabled: true,     // 音效开关
        musicEnabled: true      // 音乐开关
      },
      levelRecords: {},         // 各关卡最佳记录
      firstPlay: true,          // 是否首次游戏
      lastPlayTime: Date.now()  // 最后游戏时间
    }

    console.log('📱 GameSaveManager初始化完成')
  }

  /**
   * 获取开发者模式状态（从GameConfig）
   * @returns {boolean}
   */
  isDeveloperMode() {
    const config = getGameConfig()
    if (config && typeof config.isDeveloperMode === 'function') {
      return config.isDeveloperMode()
    }

    // 如果GameConfig不可用，返回默认值（与game.json保持一致）
    logger.warn('⚠️ GameConfig不可用，使用默认开发者模式: false')
    return false
  }
  
  /**
   * 设置开发者模式（委托给GameConfig）
   * @param {boolean} enabled - 是否启用开发者模式
   */
  setDeveloperMode(enabled) {
    const config = getGameConfig()
    if (config && typeof config.setDeveloperMode === 'function') {
      config.setDeveloperMode(enabled)
      logger.info(`🛠️ 通过GameConfig设置开发者模式: ${enabled}`)
    } else {
      logger.error('❌ GameConfig不可用，无法设置开发者模式')
    }
  }

  /**
   * 切换开发者模式（委托给GameConfig）
   * @returns {boolean} 新的开发者模式状态
   */
  toggleDeveloperMode() {
    const config = getGameConfig()
    if (config && typeof config.toggleDeveloperMode === 'function') {
      const newState = config.toggleDeveloperMode()
      logger.info(`🔄 开发者模式已切换为: ${newState}`)
      return newState
    } else {
      logger.error('❌ GameConfig不可用，无法切换开发者模式')
      return false
    }
  }

  /**
   * 设置用户管理器
   * @param {UserManager} userManager - 用户管理器实例
   */
  setUserManager(userManager) {
    this.userManager = userManager
    const newUserId = userManager ? userManager.getUserId() : null
    
    // 如果用户ID发生变化，清除缓存
    if (this.currentUserId !== newUserId) {
      this.currentUserId = newUserId
      this.clearCache()
      logger.info('📱 存档管理器设置用户ID:', this.currentUserId)
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.saveDataCache = null
    this.cacheTimestamp = 0
    this.lastLoggedSaveKey = null
  }

  /**
   * 检查缓存是否有效
   * @returns {boolean} 缓存是否有效
   */
  isCacheValid() {
    return this.saveDataCache && 
           (Date.now() - this.cacheTimestamp) < this.cacheExpireTime
  }

  /**
   * 获取当前用户的存档key
   * @returns {string} 存档key
   */
  getSaveKey() {
    if (this.currentUserId) {
      return `${this.baseSaveKey}_${this.currentUserId}`
    }
    return this.baseSaveKey // 兜底使用全局key
  }

  /**
   * 保存游戏数据
   * @param {Object} gameData - 要保存的游戏数据
   */
  saveGame(gameData) {
    try {
      const saveData = this.getSaveData()
      let hasChanges = false
      
      // 更新存档数据
      if (gameData.currentLevel !== undefined && saveData.currentLevel !== gameData.currentLevel) {
        saveData.currentLevel = gameData.currentLevel
        // 更新最高关卡
        saveData.maxLevel = Math.max(saveData.maxLevel, gameData.currentLevel)
        hasChanges = true
      }
      
      if (gameData.score !== undefined) {
        saveData.totalScore += gameData.score
        hasChanges = true
      }
      
      if (gameData.playTime !== undefined) {
        saveData.playTime += gameData.playTime
        hasChanges = true
      }
      
      if (gameData.levelRecord !== undefined) {
        const { level, record } = gameData.levelRecord
        if (!saveData.levelRecords[level] || record.score > saveData.levelRecords[level].score) {
          saveData.levelRecords[level] = record
          hasChanges = true
        }
      }
      
      if (gameData.settings !== undefined) {
        saveData.settings = { ...saveData.settings, ...gameData.settings }
        hasChanges = true
      }
      
      if (gameData.achievements !== undefined) {
        const oldLength = saveData.achievements.length
        saveData.achievements = [...new Set([...saveData.achievements, ...gameData.achievements])]
        if (saveData.achievements.length !== oldLength) {
          hasChanges = true
        }
      }
      
      if (hasChanges) {
        saveData.firstPlay = false
        saveData.lastPlayTime = Date.now()
        
        // 保存到本地存储
        const saveKey = this.getSaveKey()
        wx.setStorageSync(saveKey, saveData)
        
        // 更新缓存
        this.saveDataCache = { ...saveData }
        this.cacheTimestamp = Date.now()
        
        logger.info('📱 游戏数据已保存:', { 
          userId: this.currentUserId, 
          key: saveKey, 
          changes: gameData,
          newLevel: saveData.currentLevel,
          maxLevel: saveData.maxLevel
        })
      }
      
      return true
    } catch (error) {
      logger.error('❌ 保存游戏数据失败:', error)
      return false
    }
  }

  /**
   * 获取存档数据
   * @returns {Object} 存档数据
   */
  getSaveData() {
    try {
      // 检查缓存是否有效
      if (this.isCacheValid()) {
        return { ...this.saveDataCache }
      }
      
      const saveKey = this.getSaveKey()
      const saveData = wx.getStorageSync(saveKey)
      
      if (saveData && typeof saveData === 'object') {
        // 合并默认数据，确保所有字段都存在
        const mergedData = { ...this.defaultSaveData, ...saveData }
        
        // 更新缓存
        this.saveDataCache = { ...mergedData }
        this.cacheTimestamp = Date.now()
        
        // 只在saveKey变化时打印日志，避免重复打印
        if (this.lastLoggedSaveKey !== saveKey) {
          logger.info('📱 读取存档成功:', { 
            userId: this.currentUserId, 
            key: saveKey,
            currentLevel: mergedData.currentLevel,
            maxLevel: mergedData.maxLevel
          })
          this.lastLoggedSaveKey = saveKey
        }
        
        return mergedData
      } else {
        // 使用默认数据
        const defaultData = { ...this.defaultSaveData }
        
        // 更新缓存
        this.saveDataCache = { ...defaultData }
        this.cacheTimestamp = Date.now()
        
        // 只在saveKey变化时打印日志，避免重复打印
        if (this.lastLoggedSaveKey !== saveKey) {
          logger.info('📱 没有找到存档，使用默认数据', { 
            userId: this.currentUserId, 
            key: saveKey 
          })
          this.lastLoggedSaveKey = saveKey
        }
        
        return defaultData
      }
    } catch (error) {
      logger.error('❌ 读取存档失败:', error)
      return { ...this.defaultSaveData }
    }
  }

  /**
   * 获取当前解锁的关卡
   * @returns {number} 当前关卡
   */
  getCurrentLevel() {
    const saveData = this.getSaveData()
    return saveData.currentLevel
  }

  /**
   * 获取最高解锁关卡
   * @returns {number} 最高关卡
   */
  getMaxLevel() {
    const saveData = this.getSaveData()
    return saveData.maxLevel
  }

  /**
   * 检查是否首次游戏
   * @returns {boolean} 是否首次游戏
   */
  isFirstPlay() {
    const saveData = this.getSaveData()
    return saveData.firstPlay
  }

  /**
   * 关卡完成时保存进度
   * @param {number} level - 完成的关卡
   * @param {Object} record - 关卡记录（得分、用时等）
   */
  levelCompleted(level, record) {
    logger.info(`🎯 关卡${level}完成，保存进度:`, record)
    this.saveGame({
      currentLevel: level + 1,  // 解锁下一关
      score: record.score,
      playTime: record.playTime,
      levelRecord: {
        level: level,
        record: record
      }
    })
  }

  /**
   * 更新游戏设置
   * @param {Object} settings - 设置对象
   */
  updateSettings(settings) {
    logger.info('⚙️ 更新游戏设置:', settings)
    this.saveGame({ settings })
  }

  /**
   * 获取关卡最佳记录
   * @param {number} level - 关卡编号
   * @returns {Object|null} 最佳记录
   */
  getLevelRecord(level) {
    const saveData = this.getSaveData()
    return saveData.levelRecords[level] || null
  }

  /**
   * 清除所有存档数据
   */
  clearSave() {
    try {
      const saveKey = this.getSaveKey()
      wx.removeStorageSync(saveKey)
      this.clearCache()
      logger.info('📱 存档已清除:', { userId: this.currentUserId, key: saveKey })
      return true
    } catch (error) {
      logger.error('❌ 清除存档失败:', error)
      return false
    }
  }

  /**
   * 导出存档数据（用于备份）
   * @returns {string} JSON格式的存档数据
   */
  exportSave() {
    const saveData = this.getSaveData()
    logger.info('📤 导出存档数据')
    return JSON.stringify(saveData, null, 2)
  }

  /**
   * 导入存档数据（用于恢复）
   * @param {string} saveDataJson - JSON格式的存档数据
   * @returns {boolean} 是否导入成功
   */
  importSave(saveDataJson) {
    try {
      const saveData = JSON.parse(saveDataJson)
      const saveKey = this.getSaveKey()
      wx.setStorageSync(saveKey, saveData)
      this.clearCache()
      logger.info('📥 存档导入成功:', { userId: this.currentUserId, key: saveKey })
      return true
    } catch (error) {
      logger.error('❌ 存档导入失败:', error)
      return false
    }
  }

  /**
   * 检查关卡是否已解锁
   * @param {number} level - 关卡编号
   * @returns {boolean} 是否已解锁
   */
  isLevelUnlocked(level) {
    const maxLevel = this.getMaxLevel()

    // 使用GameConfig的统一关卡解锁检查
    const config = getGameConfig()
    if (config && typeof config.isLevelUnlocked === 'function') {
      return config.isLevelUnlocked(level, maxLevel)
    }

    // 降级方案：直接检查开发者模式
    if (this.isDeveloperMode()) {
      return true
    }

    return level <= maxLevel
  }

  /**
   * 获取缓存状态信息（用于调试）
   * @returns {Object} 缓存状态
   */
  getCacheStatus() {
    return {
      hasCache: !!this.saveDataCache,
      cacheAge: this.saveDataCache ? Date.now() - this.cacheTimestamp : 0,
      isValid: this.isCacheValid(),
      lastLoggedKey: this.lastLoggedSaveKey
    }
  }

  /**
   * 手动启用开发者模式（调试用）
   * @param {boolean} force - 是否强制启用
   */
  enableDeveloperMode(force = false) {
    logger.info('🛠️ 手动启用开发者模式...')

    // 使用setDeveloperMode方法，确保通过GameConfig统一管理
    this.setDeveloperMode(true)

    logger.info('✅ 开发者模式已手动启用')
    logger.info('🔓 所有关卡现在都应该已解锁')
    logger.info('📝 DEBUG/INFO日志已启用')
  }
}

module.exports = GameSaveManager 