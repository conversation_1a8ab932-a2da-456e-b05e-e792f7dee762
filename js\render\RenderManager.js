/**
 * 渲染管理器
 * 负责游戏的所有界面渲染逻辑
 */
class RenderManager {
  constructor(mainInstance) {
    this.main = mainInstance
  }

  /**
   * 主渲染方法
   */
  render() {
    // 清空画布
    this.main.ctx.clearRect(0, 0, this.main.canvasSize.screenWidth, this.main.canvasSize.screenHeight)
    
    // 绘制背景
    this.renderBackground()
    
    // 根据游戏状态渲染不同内容
    switch (this.main.gameState) {
      case 'loading':
        this.renderLoadingScreen()
        break
      case 'menu':
        this.renderMenuScreen()
        break
      case 'levelSelect':
        this.renderLevelSelectScreen()
        break
      case 'playing':
        this.renderGameScreen()
        break
      case 'win':
        this.renderWinScreen()
        break
      case 'lose':
        this.renderLoseScreen()
        break
    }

    // 渲染文件信息面板（显示在所有内容上方）
    if (this.main.fileInfoPanel && this.main.debugMode) {
      this.main.fileInfoPanel.render(this.main.ctx)
    }
    
    // 渲染临时消息（显示在最上层）
    this.renderTemporaryMessage()
  }

  /**
   * 渲染临时消息
   */
  renderTemporaryMessage() {
    if (!this.main.temporaryMessage) return
    
    const now = Date.now()
    const elapsed = now - this.main.temporaryMessage.startTime
    
    // 检查消息是否过期
    if (elapsed > this.main.temporaryMessage.duration) {
      this.main.temporaryMessage = null
      return
    }
    
    const ctx = this.main.ctx
    const centerX = this.main.canvasSize.screenWidth / 2
    const centerY = this.main.canvasSize.screenHeight / 3
    
    // 计算透明度（淡入淡出效果）
    const fadeTime = 300 // 300ms淡入淡出时间
    let alpha = 1
    
    if (elapsed < fadeTime) {
      // 淡入
      alpha = elapsed / fadeTime
    } else if (elapsed > this.main.temporaryMessage.duration - fadeTime) {
      // 淡出
      alpha = (this.main.temporaryMessage.duration - elapsed) / fadeTime
    }
    
    ctx.save()
    ctx.globalAlpha = alpha
    
    // 绘制消息背景
    const padding = 20
    const textMetrics = ctx.measureText(this.main.temporaryMessage.text)
    const textWidth = textMetrics.width
    const textHeight = 24
    const bgWidth = textWidth + padding * 2
    const bgHeight = textHeight + padding
    
    // 背景渐变
    const gradient = ctx.createLinearGradient(
      centerX - bgWidth / 2, centerY - bgHeight / 2,
      centerX + bgWidth / 2, centerY + bgHeight / 2
    )
    gradient.addColorStop(0, 'rgba(0, 0, 0, 0.8)')
    gradient.addColorStop(1, 'rgba(50, 50, 50, 0.8)')
    
    ctx.fillStyle = gradient
    this.drawRoundedRect(ctx, centerX - bgWidth / 2, centerY - bgHeight / 2, bgWidth, bgHeight, 12)
    ctx.fill()
    
    // 绘制边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
    ctx.lineWidth = 1
    this.drawRoundedRect(ctx, centerX - bgWidth / 2, centerY - bgHeight / 2, bgWidth, bgHeight, 12)
    ctx.stroke()
    
    // 绘制消息文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 18px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(this.main.temporaryMessage.text, centerX, centerY)
    
    ctx.restore()
  }

  /**
   * 绘制背景
   */
  renderBackground() {
    // 更美观的渐变背景
    const gradient = this.main.ctx.createLinearGradient(
      0, 0, 0, this.main.canvasSize.screenHeight
    )
    gradient.addColorStop(0, '#FFE4E1') // 薄雾玫瑰
    gradient.addColorStop(0.3, '#E6E6FA') // 薰衣草
    gradient.addColorStop(0.7, '#F0F8FF') // 爱丽丝蓝
    gradient.addColorStop(1, '#F5FFFA') // 薄荷奶油
    
    this.main.ctx.fillStyle = gradient
    this.main.ctx.fillRect(0, 0, this.main.canvasSize.screenWidth, this.main.canvasSize.screenHeight)
    
    // 添加动态装饰元素
    this.renderDecorations()
    
    // 添加光效
    this.renderLightEffects()
  }

  /**
   * 绘制装饰元素
   */
  renderDecorations() {
    const ctx = this.main.ctx
    const time = Date.now() * 0.001 // 用于动画
    
    // 绘制浮动的装饰圆圈
    ctx.globalAlpha = 0.3
    for (let i = 0; i < 8; i++) {
      const x = (i * this.main.canvasSize.screenWidth / 7) + Math.sin(time + i) * 20
      const y = 100 + Math.cos(time * 0.5 + i) * 30
      const radius = 15 + Math.sin(time * 2 + i) * 5
      
      const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius)
      gradient.addColorStop(0, '#FFB6C1') // 浅粉色
      gradient.addColorStop(1, 'transparent')
      
      ctx.fillStyle = gradient
      ctx.beginPath()
      ctx.arc(x, y, radius, 0, Math.PI * 2)
      ctx.fill()
    }
    
    // 绘制星星装饰
    ctx.fillStyle = '#FFD700'
    ctx.globalAlpha = 0.6
    for (let i = 0; i < 5; i++) {
      const x = Math.random() * this.main.canvasSize.screenWidth
      const y = 50 + Math.random() * 100
      const size = 3 + Math.sin(time * 3 + i) * 2
      
      this.drawStar(ctx, x, y, size, 5)
    }
    
    ctx.globalAlpha = 1
  }

  /**
   * 绘制光效
   */
  renderLightEffects() {
    const ctx = this.main.ctx
    const time = Date.now() * 0.002
    
    // 绘制柔和的光晕效果
    ctx.globalAlpha = 0.1
    for (let i = 0; i < 3; i++) {
      const x = this.main.canvasSize.screenWidth * (0.2 + i * 0.3)
      const y = this.main.canvasSize.screenHeight * 0.3
      const radius = 100 + Math.sin(time + i * 2) * 20
      
      const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius)
      gradient.addColorStop(0, '#FFFFFF')
      gradient.addColorStop(1, 'transparent')
      
      ctx.fillStyle = gradient
      ctx.beginPath()
      ctx.arc(x, y, radius, 0, Math.PI * 2)
      ctx.fill()
    }
    
    ctx.globalAlpha = 1
  }

  /**
   * 绘制星星
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} size - 大小
   * @param {number} points - 星星的角数
   */
  drawStar(ctx, x, y, size, points) {
    ctx.save()
    ctx.translate(x, y)
    ctx.beginPath()
    
    for (let i = 0; i < points * 2; i++) {
      const angle = (i * Math.PI) / points
      const radius = i % 2 === 0 ? size : size * 0.5
      const px = Math.cos(angle) * radius
      const py = Math.sin(angle) * radius
      
      if (i === 0) {
        ctx.moveTo(px, py)
      } else {
        ctx.lineTo(px, py)
      }
    }
    
    ctx.closePath()
    ctx.fill()
    ctx.restore()
  }

  /**
   * 渲染加载界面
   */
  renderLoadingScreen() {
    this.main.ctx.fillStyle = '#333333'
    this.main.ctx.font = '24px Arial'
    this.main.ctx.textAlign = 'center'
    this.main.ctx.fillText(
      '游戏加载中...',
      this.main.canvasSize.screenWidth / 2,
      this.main.canvasSize.screenHeight / 2
    )
  }

  /**
   * 渲染菜单界面
   */
  renderMenuScreen() {
    const ctx = this.main.ctx
    const centerX = this.main.canvasSize.screenWidth / 2
    const centerY = this.main.canvasSize.screenHeight / 2
    const time = Date.now() * 0.001 // 用于动画

    // 添加菜单专用的装饰背景
    this.renderMenuDecorations(ctx, time)

    // 游戏标题 - 增强版本
    this.renderEnhancedTitle(ctx, centerX, centerY - 120, time)

    // 显示用户信息和进度（如果有的话）
    if (!this.main.isFirstPlay) {
      this.main.uiRenderer.drawUserInfoCard(ctx, centerX)
    } else {
      // 首次游戏的欢迎信息
      this.renderWelcomeMessage(ctx, centerX, centerY - 20, time)
    }

    // 美化的进入提示
    this.renderEnhancedStartPrompt(ctx, centerX, centerY + 120, time)
  }

  /**
   * 渲染菜单装饰背景
   */
  renderMenuDecorations(ctx, time) {
    // 添加浮动的装饰元素
    ctx.globalAlpha = 0.15

    // 绘制浮动的鸭子图标
    for (let i = 0; i < 6; i++) {
      const x = (i * this.main.canvasSize.screenWidth / 5) + Math.sin(time * 0.5 + i) * 30
      const y = 150 + Math.cos(time * 0.3 + i) * 40
      const size = 20 + Math.sin(time * 2 + i) * 5

      ctx.fillStyle = '#FFD700'
      ctx.font = `${size}px Arial`
      ctx.textAlign = 'center'
      ctx.fillText('🦆', x, y)
    }

    // 绘制星星装饰
    for (let i = 0; i < 8; i++) {
      const x = Math.random() * this.main.canvasSize.screenWidth
      const y = 100 + Math.sin(time + i) * 20
      const alpha = 0.3 + Math.sin(time * 3 + i) * 0.2

      ctx.globalAlpha = alpha
      ctx.fillStyle = '#FFD700'
      this.drawStar(ctx, x, y, 8, 5)
    }

    ctx.globalAlpha = 1
  }

  /**
   * 渲染增强版标题
   */
  renderEnhancedTitle(ctx, centerX, centerY, time) {
    // 标题发光效果
    ctx.save()

    // 外层光晕
    ctx.shadowColor = '#FFD700'
    ctx.shadowBlur = 20
    ctx.shadowOffsetX = 0
    ctx.shadowOffsetY = 0

    // 标题文字渐变
    const titleGradient = ctx.createLinearGradient(centerX - 150, centerY - 30, centerX + 150, centerY + 30)
    titleGradient.addColorStop(0, '#FFD700')
    titleGradient.addColorStop(0.5, '#FFA500')
    titleGradient.addColorStop(1, '#FF8C00')

    // 绘制标题描边
    ctx.strokeStyle = '#8B4513'
    ctx.lineWidth = 4
    ctx.font = 'bold 52px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.strokeText('鸭了个鸭呀', centerX, centerY)

    // 绘制标题填充
    ctx.fillStyle = titleGradient
    ctx.fillText('鸭了个鸭呀', centerX, centerY)

    // 添加闪烁效果
    const sparkleAlpha = 0.3 + Math.sin(time * 4) * 0.3
    ctx.globalAlpha = sparkleAlpha
    ctx.fillStyle = '#FFFFFF'
    ctx.fillText('鸭了个鸭呀', centerX, centerY)

    ctx.restore()

    // 添加副标题
    ctx.fillStyle = '#666666'
    ctx.font = 'italic 16px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('— 小动物们在前面等着你 —', centerX, centerY + 40)
  }

  /**
   * 渲染欢迎信息
   */
  renderWelcomeMessage(ctx, centerX, centerY, time) {
    // 欢迎卡片背景
    const cardWidth = 280
    const cardHeight = 80

    // 卡片阴影
    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'
    this.drawRoundedRect(ctx, centerX - cardWidth / 2 + 3, centerY - cardHeight / 2 + 3, cardWidth, cardHeight, 15)
    ctx.fill()

    // 卡片渐变背景
    const cardGradient = ctx.createLinearGradient(centerX - cardWidth / 2, centerY - cardHeight / 2, centerX + cardWidth / 2, centerY + cardHeight / 2)
    cardGradient.addColorStop(0, 'rgba(255, 255, 255, 0.9)')
    cardGradient.addColorStop(1, 'rgba(240, 248, 255, 0.9)')

    ctx.fillStyle = cardGradient
    this.drawRoundedRect(ctx, centerX - cardWidth / 2, centerY - cardHeight / 2, cardWidth, cardHeight, 15)
    ctx.fill()

    // 卡片边框
    ctx.strokeStyle = 'rgba(74, 144, 226, 0.3)'
    ctx.lineWidth = 2
    this.drawRoundedRect(ctx, centerX - cardWidth / 2, centerY - cardHeight / 2, cardWidth, cardHeight, 15)
    ctx.stroke()

    // 欢迎文字
    ctx.fillStyle = '#4CAF50'
    ctx.font = 'bold 18px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText('🎉 欢迎来到鸭了个鸭呀！', centerX, centerY - 5)
  }

  /**
   * 渲染增强版开始提示
   */
  renderEnhancedStartPrompt(ctx, centerX, centerY, time) {
    // 脉冲动画效果
    const pulseScale = 1 + Math.sin(time * 3) * 0.1
    const pulseAlpha = 0.7 + Math.sin(time * 3) * 0.3

    ctx.save()
    ctx.globalAlpha = pulseAlpha
    ctx.translate(centerX, centerY)
    ctx.scale(pulseScale, pulseScale)

    // 提示背景
    const promptWidth = 260
    const promptHeight = 50

    // 渐变背景
    const promptGradient = ctx.createLinearGradient(-promptWidth / 2, -promptHeight / 2, promptWidth / 2, promptHeight / 2)
    promptGradient.addColorStop(0, '#FF6B9D')
    promptGradient.addColorStop(1, '#FF8FB3')

    ctx.fillStyle = promptGradient
    this.drawRoundedRect(ctx, -promptWidth / 2, -promptHeight / 2, promptWidth, promptHeight, 25)
    ctx.fill()

    // 边框
    ctx.strokeStyle = '#FF6B9D'
    ctx.lineWidth = 2
    this.drawRoundedRect(ctx, -promptWidth / 2, -promptHeight / 2, promptWidth, promptHeight, 25)
    ctx.stroke()

    // 提示文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText('🎮 点击屏幕开始游戏', 0, 0)

    ctx.restore()
  }

  /**
   * 渲染用户信息卡片 - 美化版本
   */
  renderUserInfoCard(ctx, centerX, centerY) {
    const saveData = this.main.saveManager.getSaveData()
    const userInfo = this.main.userManager.getUserInfo()
    const cardWidth = 320
    const cardHeight = 100

    // 卡片阴影
    ctx.fillStyle = 'rgba(0, 0, 0, 0.15)'
    this.drawRoundedRect(ctx, centerX - cardWidth / 2 + 4, centerY - cardHeight / 2 + 4, cardWidth, cardHeight, 15)
    ctx.fill()

    // 卡片渐变背景
    const cardGradient = ctx.createLinearGradient(
      centerX - cardWidth / 2, centerY - cardHeight / 2,
      centerX + cardWidth / 2, centerY + cardHeight / 2
    )
    cardGradient.addColorStop(0, 'rgba(255, 255, 255, 0.95)')
    cardGradient.addColorStop(0.5, 'rgba(240, 248, 255, 0.95)')
    cardGradient.addColorStop(1, 'rgba(230, 240, 255, 0.95)')

    ctx.fillStyle = cardGradient
    this.drawRoundedRect(ctx, centerX - cardWidth / 2, centerY - cardHeight / 2, cardWidth, cardHeight, 15)
    ctx.fill()

    // 卡片边框
    ctx.strokeStyle = 'rgba(74, 144, 226, 0.4)'
    ctx.lineWidth = 2
    this.drawRoundedRect(ctx, centerX - cardWidth / 2, centerY - cardHeight / 2, cardWidth, cardHeight, 15)
    ctx.stroke()

    // 显示用户信息
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'

    // 用户昵称
    if (userInfo && userInfo.profile && userInfo.profile.nickName) {
      ctx.fillStyle = '#4CAF50'
      ctx.font = 'bold 16px Arial'
      ctx.fillText(`👤 ${userInfo.profile.nickName}`, centerX, centerY - 25)

      // 服务器连接状态
      if (this.main.userManager.isConnectedToServer()) {
        if (this.main.isLoadingServerProgress) {
          ctx.fillStyle = '#FF9800'
          ctx.font = '12px Arial'
          ctx.fillText('🔄 正在同步进度...', centerX, centerY - 5)
        } else if (this.main.serverProgress) {
          ctx.fillStyle = '#2196F3'
          ctx.font = '12px Arial'
          ctx.fillText('🌐 云端已同步', centerX, centerY - 5)
        } else {
          ctx.fillStyle = '#F44336'
          ctx.font = '12px Arial'
          ctx.fillText('⚠️ 同步失败', centerX, centerY - 5)
        }
      } else {
        ctx.fillStyle = '#FF9800'
        ctx.font = '12px Arial'
        ctx.fillText('📱 本地模式', centerX, centerY - 5)
      }
    } else {
      ctx.fillStyle = '#666666'
      ctx.font = '16px Arial'
      ctx.fillText('👤 游客模式', centerX, centerY - 20)
    }

    // 游戏进度信息 - 统一使用与关卡选择页相同的数据源
    ctx.fillStyle = '#333333'
    ctx.font = 'bold 14px Arial'

    // 优先使用服务器进度，如果没有则使用本地进度
    let maxLevel, totalScore
    if (this.main.serverProgress) {
      maxLevel = this.main.serverProgress.maxLevel
      totalScore = this.main.serverProgress.totalScore || 0
    } else {
      maxLevel = saveData.maxLevel
      totalScore = saveData.totalScore
    }

    ctx.fillText(`🏆 最高关卡: ${maxLevel}`, centerX - 80, centerY + 20)
    ctx.fillText(`💎 总得分: ${totalScore}`, centerX + 80, centerY + 20)
  }

  /**
   * 渲染关卡选择界面
   */
  renderLevelSelectScreen() {
    const ctx = this.main.ctx
    const centerX = this.main.canvasSize.screenWidth / 2
    const time = Date.now() * 0.001 // 用于动画

    // 优先使用服务器进度，如果没有则使用本地进度
    let maxLevel, totalScore
    if (this.main.serverProgress) {
      maxLevel = this.main.serverProgress.maxLevel
      totalScore = this.main.serverProgress.totalScore || 0
    } else {
      maxLevel = this.main.saveManager.getMaxLevel()
      const saveData = this.main.saveManager.getSaveData()
      totalScore = saveData.totalScore
    }

    const totalLevels = 20 // 游戏总共有20关，无论是否解锁都要显示

    // 渲染增强版标题区域
    this.renderLevelSelectHeader(ctx, centerX, time, maxLevel, totalScore)

    // 渲染关卡网格
    this.renderLevelGrid(ctx, centerX, time, maxLevel, totalLevels)
  }

  /**
   * 渲染关卡选择标题区域
   */
  renderLevelSelectHeader(ctx, centerX, time, maxLevel, totalScore) {
    // 增强版标题背景 - 更丰富的渐变，增加高度避免UI重叠
    const titleGradient = ctx.createLinearGradient(0, 0, this.main.canvasSize.screenWidth, 140)
    titleGradient.addColorStop(0, 'rgba(74, 144, 226, 0.9)')
    titleGradient.addColorStop(0.3, 'rgba(80, 227, 194, 0.9)')
    titleGradient.addColorStop(0.7, 'rgba(184, 233, 134, 0.9)')
    titleGradient.addColorStop(1, 'rgba(255, 182, 193, 0.9)')

    ctx.fillStyle = titleGradient
    ctx.fillRect(0, 0, this.main.canvasSize.screenWidth, 140)

    // 添加动态装饰元素
    this.renderHeaderDecorations(ctx, centerX, time)

    // 美化标题文字 - 向下移动避免刘海屏遮挡
    ctx.save()
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)'
    ctx.shadowBlur = 8
    ctx.shadowOffsetX = 2
    ctx.shadowOffsetY = 2

    ctx.fillStyle = '#FFFFFF'
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.4)'
    ctx.lineWidth = 3
    ctx.font = 'bold 38px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.strokeText('🎮 关卡选择', centerX, 55)
    ctx.fillText('🎮 关卡选择', centerX, 55)
    ctx.restore()

    // 美化进度信息显示
    this.renderProgressInfo(ctx, centerX, maxLevel, totalScore)
  }

  /**
   * 渲染标题装饰元素
   */
  renderHeaderDecorations(ctx, centerX, time) {
    // 浮动的光效
    ctx.globalAlpha = 0.4
    for (let i = 0; i < 5; i++) {
      const x = centerX + Math.sin(time * 1.5 + i * 1.2) * 120
      const y = 60 + Math.cos(time * 0.8 + i * 0.8) * 20
      const radius = 12 + Math.sin(time * 2 + i) * 4

      const sparkleGradient = ctx.createRadialGradient(x, y, 0, x, y, radius)
      sparkleGradient.addColorStop(0, '#FFFFFF')
      sparkleGradient.addColorStop(0.7, 'rgba(255, 255, 255, 0.5)')
      sparkleGradient.addColorStop(1, 'transparent')

      ctx.fillStyle = sparkleGradient
      ctx.beginPath()
      ctx.arc(x, y, radius, 0, Math.PI * 2)
      ctx.fill()
    }

    // 添加星星装饰
    for (let i = 0; i < 3; i++) {
      const x = 50 + i * (this.main.canvasSize.screenWidth - 100) / 2
      const y = 25 + Math.sin(time * 2 + i * 2) * 8
      const size = 6 + Math.sin(time * 3 + i) * 2

      ctx.fillStyle = '#FFD700'
      this.drawStar(ctx, x, y, size, 5)
    }

    ctx.globalAlpha = 1
  }

  /**
   * 渲染进度信息
   */
  renderProgressInfo(ctx, centerX, maxLevel, totalScore) {
    // 进度信息背景
    const infoWidth = 300
    const infoHeight = 30
    const infoY = 95  // 去掉返回按钮后，可以向上移动

    // 半透明背景
    ctx.fillStyle = 'rgba(255, 255, 255, 0.2)'
    this.drawRoundedRect(ctx, centerX - infoWidth / 2, infoY - infoHeight / 2, infoWidth, infoHeight, 15)
    ctx.fill()

    // 进度文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'

    let progressText
    if (this.main.serverProgress) {
      progressText = `🏆 最高关卡: ${maxLevel}   💎 总得分: ${totalScore} 🌐`
    } else {
      progressText = `🏆 最高关卡: ${maxLevel}   💎 总得分: ${totalScore}`
    }

    // 添加文字阴影
    ctx.save()
    ctx.shadowColor = 'rgba(0, 0, 0, 0.5)'
    ctx.shadowBlur = 3
    ctx.shadowOffsetX = 1
    ctx.shadowOffsetY = 1
    ctx.fillText(progressText, centerX, infoY)
    ctx.restore()
  }

  /**
   * 渲染关卡网格
   */
  renderLevelGrid(ctx, centerX, time, maxLevel, totalLevels) {
    // 关卡按钮布局 (4列布局，更美观)
    const cols = 4
    const buttonSize = 60 // 调整按钮大小以适应更多关卡
    const spacing = 20 // 调整间距
    const startY = 170 // 增加间距，避免与标题栏重叠（标题栏高度140px + 30px间距）
    const totalWidth = cols * buttonSize + (cols - 1) * spacing
    const startX = centerX - totalWidth / 2

    // 计算可显示的行数 - 确保能显示20关
    const availableHeight = this.main.canvasSize.screenHeight - startY - 80
    const maxRows = Math.floor(availableHeight / (buttonSize + spacing + 35)) // 35是星星和文字的空间
    const levelsPerPage = cols * maxRows

    // 简单分页
    const currentPage = 0
    const startLevel = currentPage * levelsPerPage + 1
    const endLevel = Math.min(startLevel + levelsPerPage - 1, totalLevels)

    // 绘制美化的网格背景
    this.renderGridBackground(ctx, startX, startY, totalWidth, maxRows, buttonSize, spacing)

    // 渲染关卡按钮
    this.renderLevelButtons(ctx, startX, startY, buttonSize, spacing, cols, startLevel, endLevel, maxLevel, time)

    // 显示分页信息（如果有多页）
    if (totalLevels > levelsPerPage) {
      this.renderPaginationInfo(ctx, centerX, currentPage, totalLevels, levelsPerPage)
    }
  }

  /**
   * 渲染网格背景
   */
  renderGridBackground(ctx, startX, startY, totalWidth, maxRows, buttonSize, spacing) {
    const gridHeight = maxRows * (buttonSize + spacing + 40) + 20
    const padding = 20

    // 背景阴影
    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'
    this.drawRoundedRect(ctx, startX - padding + 3, startY - padding + 3, totalWidth + padding * 2, gridHeight, 15)
    ctx.fill()

    // 背景渐变
    const bgGradient = ctx.createLinearGradient(startX - padding, startY - padding, startX - padding, startY + gridHeight)
    bgGradient.addColorStop(0, 'rgba(255, 255, 255, 0.15)')
    bgGradient.addColorStop(1, 'rgba(255, 255, 255, 0.05)')

    ctx.fillStyle = bgGradient
    this.drawRoundedRect(ctx, startX - padding, startY - padding, totalWidth + padding * 2, gridHeight, 15)
    ctx.fill()

    // 背景边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
    ctx.lineWidth = 1
    this.drawRoundedRect(ctx, startX - padding, startY - padding, totalWidth + padding * 2, gridHeight, 15)
    ctx.stroke()
  }

  /**
   * 渲染关卡按钮
   */
  renderLevelButtons(ctx, startX, startY, buttonSize, spacing, cols, startLevel, endLevel, maxLevel, time) {
    for (let i = startLevel; i <= endLevel; i++) {
      const index = i - startLevel
      const row = Math.floor(index / cols)
      const col = index % cols
      const buttonX = startX + col * (buttonSize + spacing)
      const buttonY = startY + row * (buttonSize + spacing + 40)

      // 检查关卡是否已解锁 - 优先使用服务器数据
      let isUnlocked, record
      if (this.main.serverProgress && this.main.serverProgress.records) {
        isUnlocked = i <= maxLevel
        record = this.main.serverProgress.records.find(r => r.level === i)
      } else {
        isUnlocked = this.main.saveManager.isLevelUnlocked(i)
        record = this.main.saveManager.getLevelRecord(i)
      }

      // 渲染单个关卡按钮
      this.renderSingleLevelButton(ctx, buttonX, buttonY, buttonSize, i, isUnlocked, record, time)
    }
  }

  /**
   * 渲染单个关卡按钮
   */
  renderSingleLevelButton(ctx, buttonX, buttonY, buttonSize, level, isUnlocked, record, time) {
    // 添加按钮悬浮动画效果
    const hoverOffset = Math.sin(time * 2 + level * 0.5) * 2
    const finalY = buttonY + hoverOffset

    // 绘制增强版按钮阴影
    ctx.fillStyle = 'rgba(0, 0, 0, 0.25)'
    this.drawRoundedRect(ctx, buttonX + 4, finalY + 4, buttonSize, buttonSize, 12)
    ctx.fill()

    if (isUnlocked) {
      // 已解锁关卡
      const primaryColor = record ? '#4CAF50' : '#2196F3'
      const secondaryColor = record ? '#66BB6A' : '#42A5F5'

      // 绘制关卡按钮
      this.main.uiRenderer.drawLevelButton(ctx, buttonX, finalY, buttonSize, level.toString(), primaryColor, secondaryColor, true)

      // 显示星级评价
      if (record) {
        const stars = this.calculateStars(record)
        this.drawEnhancedStars(ctx, buttonX + buttonSize / 2, finalY + buttonSize + 18, stars)

        // 显示最佳成绩
        ctx.fillStyle = '#555555'
        ctx.font = 'bold 12px Arial'
        ctx.textAlign = 'center'
        ctx.fillText(`${record.moves}步`, buttonX + buttonSize / 2, finalY + buttonSize + 35)
      }
    } else {
      // 未解锁关卡
      this.drawEnhancedLevelButton(ctx, buttonX, finalY, buttonSize, '🔒', '#CCCCCC', '#E0E0E0', false)

      // 显示解锁条件
      ctx.fillStyle = '#999999'
      ctx.font = '11px Arial'
      ctx.textAlign = 'center'
      ctx.fillText('未解锁', buttonX + buttonSize / 2, finalY + buttonSize + 25)
    }
  }

  /**
   * 渲染分页信息
   */
  renderPaginationInfo(ctx, centerX, currentPage, totalLevels, levelsPerPage) {
    const totalPages = Math.ceil(totalLevels / levelsPerPage)

    // 分页信息背景
    const infoWidth = 200
    const infoHeight = 30
    const infoY = this.main.canvasSize.screenHeight - 40

    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'
    this.drawRoundedRect(ctx, centerX - infoWidth / 2, infoY - infoHeight / 2, infoWidth, infoHeight, 15)
    ctx.fill()

    // 分页文字
    ctx.fillStyle = '#666666'
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(`第 ${currentPage + 1} 页 / 共 ${totalPages} 页`, centerX, infoY)
  }



  /**
   * 绘制增强版星星评价
   */
  drawEnhancedStars(ctx, centerX, y, starCount) {
    const starSize = 10
    const spacing = 18
    const totalWidth = 3 * starSize + 2 * spacing
    const startX = centerX - totalWidth / 2

    ctx.save()
    for (let i = 0; i < 3; i++) {
      const x = startX + i * spacing
      if (i < starCount) {
        // 金色星星
        ctx.fillStyle = '#FFD700'
        ctx.strokeStyle = '#FFA500'
        ctx.lineWidth = 1
        this.drawStar(ctx, x, y, starSize, 5)
        ctx.fill()
        ctx.stroke()
      } else {
        // 灰色星星
        ctx.fillStyle = '#DDDDDD'
        ctx.strokeStyle = '#CCCCCC'
        ctx.lineWidth = 1
        this.drawStar(ctx, x, y, starSize, 5)
        ctx.fill()
        ctx.stroke()
      }
    }
    ctx.restore()
  }

  /**
   * 计算关卡星级评价
   */
  calculateStars(record) {
    // 简单的星级评价算法
    if (record.moves <= 20) return 3
    if (record.moves <= 35) return 2
    return 1
  }

  /**
   * 渲染游戏界面
   */
  renderGameScreen() {
    // 每帧渲染前更新可点击状态，保证状态与视觉同步
    this.main.board.updateClickableStates()
    // 渲染UI信息
    this.renderGameUI()

    // 渲染游戏板
    if (this.main.board) {
      this.main.board.render(this.main.ctx, this.main.resources)

      // 如果启用调试模式，渲染层级信息
      if (this.main.debugMode) {
        this.renderDebugInfo()
      }
    }

    // 渲染槽位
    if (this.main.slot) {
      this.main.slot.render(this.main.ctx, this.main.resources)
    }

    // 渲染水印
    this.renderWatermark()

    // 渲染道具按钮
    this.main.powerUpButtons.forEach(button => {
      button.render(this.main.ctx)
    })

    // 渲染设置按钮
    if (this.main.settingsButton) {
      this.main.settingsButton.render(this.main.ctx)
    }

    // 渲染设置面板（最上层）
    if (this.main.settingsPanel) {
      this.main.settingsPanel.render(this.main.ctx)
    }

    // 渲染分享对话框（最最上层）
    if (this.main.shareDialog && this.main.shareDialog.visible) {
      this.main.shareDialog.render(this.main.ctx)
    }
  }

  /**
   * 渲染游戏UI
   */
  renderGameUI() {
    const ctx = this.main.ctx
    const time = Date.now() * 0.001

    // 渲染增强版顶部信息栏
    this.renderEnhancedTopBar(ctx, time)

    // 渲染游戏状态信息
    this.renderGameStatusInfo(ctx, time)
  }

  /**
   * 渲染增强版顶部信息栏
   */
  renderEnhancedTopBar(ctx, time) {
    const barHeight = 100  // 从70增加到100，扩大标签栏高度

    // 顶部信息栏渐变背景
    const topGradient = ctx.createLinearGradient(0, 0, 0, barHeight)
    topGradient.addColorStop(0, 'rgba(74, 144, 226, 0.9)')
    topGradient.addColorStop(0.5, 'rgba(80, 227, 194, 0.8)')
    topGradient.addColorStop(1, 'rgba(184, 233, 134, 0.7)')

    ctx.fillStyle = topGradient
    ctx.fillRect(0, 0, this.main.canvasSize.screenWidth, barHeight)

    // 添加装饰光效
    ctx.globalAlpha = 0.3
    for (let i = 0; i < 3; i++) {
      const x = (i + 1) * this.main.canvasSize.screenWidth / 4 + Math.sin(time + i * 2) * 20
      const y = barHeight / 2
      const sparkleGradient = ctx.createRadialGradient(x, y, 0, x, y, 15)
      sparkleGradient.addColorStop(0, '#FFFFFF')
      sparkleGradient.addColorStop(1, 'transparent')

      ctx.fillStyle = sparkleGradient
      ctx.beginPath()
      ctx.arc(x, y, 10 + Math.sin(time * 3 + i) * 3, 0, Math.PI * 2)
      ctx.fill()
    }
    ctx.globalAlpha = 1

    // 底部边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.moveTo(0, barHeight)
    ctx.lineTo(this.main.canvasSize.screenWidth, barHeight)
    ctx.stroke()
  }

  /**
   * 渲染游戏状态信息
   */
  renderGameStatusInfo(ctx, time) {
    const centerX = this.main.canvasSize.screenWidth / 2
    const barHeight = 100  // 更新为新的标签栏高度

    // 关卡信息（右上角）
    this.renderLevelInfo(ctx, time)

    // 分数信息（居中，稍微往下调整）
    this.renderScoreInfo(ctx, centerX, barHeight / 2 + 5, time)  // 调整分数位置

    // 移动次数（左上角）
    this.renderMovesInfo(ctx, time)
  }

  /**
   * 渲染关卡信息
   */
  renderLevelInfo(ctx, time) {
    const x = this.main.canvasSize.screenWidth - 25
    const y = 50  // 从35增加到50，往下移动15px

    // 关卡信息背景
    const bgWidth = 100
    const bgHeight = 30

    ctx.fillStyle = 'rgba(255, 255, 255, 0.2)'
    this.drawRoundedRect(ctx, x - bgWidth, y - bgHeight / 2, bgWidth, bgHeight, 15)
    ctx.fill()

    // 关卡文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 18px Arial'
    ctx.textAlign = 'right'
    ctx.textBaseline = 'middle'

    // 添加文字阴影
    ctx.save()
    ctx.shadowColor = 'rgba(0, 0, 0, 0.5)'
    ctx.shadowBlur = 3
    ctx.shadowOffsetX = 1
    ctx.shadowOffsetY = 1
    ctx.fillText(`第${this.main.currentLevel}关`, x - 10, y)
    ctx.restore()
  }

  /**
   * 渲染分数信息
   */
  renderScoreInfo(ctx, centerX, centerY, time) {
    // 分数背景
    const bgWidth = 120
    const bgHeight = 35

    // 脉冲效果
    const pulseScale = 1 + Math.sin(time * 4) * 0.05

    ctx.save()
    ctx.translate(centerX, centerY)
    ctx.scale(pulseScale, pulseScale)

    // 分数背景
    ctx.fillStyle = 'rgba(255, 255, 255, 0.25)'
    this.drawRoundedRect(ctx, -bgWidth / 2, -bgHeight / 2, bgWidth, bgHeight, 18)
    ctx.fill()

    // 分数边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)'
    ctx.lineWidth = 2
    this.drawRoundedRect(ctx, -bgWidth / 2, -bgHeight / 2, bgWidth, bgHeight, 18)
    ctx.stroke()

    // 分数文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'

    // 添加文字阴影
    ctx.shadowColor = 'rgba(0, 0, 0, 0.7)'
    ctx.shadowBlur = 4
    ctx.shadowOffsetX = 1
    ctx.shadowOffsetY = 1
    ctx.fillText(`💎 ${this.main.score}`, 0, 0)

    ctx.restore()
  }

  /**
   * 渲染移动次数信息
   */
  renderMovesInfo(ctx, time) {
    const x = 25
    const y = 35

    // 移动次数背景
    const bgWidth = 80
    const bgHeight = 30

    ctx.fillStyle = 'rgba(255, 255, 255, 0.2)'
    this.drawRoundedRect(ctx, x, y - bgHeight / 2, bgWidth, bgHeight, 15)
    ctx.fill()

    // 移动次数文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'left'
    ctx.textBaseline = 'middle'

    // 添加文字阴影
    ctx.save()
    ctx.shadowColor = 'rgba(0, 0, 0, 0.5)'
    ctx.shadowBlur = 3
    ctx.shadowOffsetX = 1
    ctx.shadowOffsetY = 1
    ctx.fillText(`🎯 ${this.main.moves}`, x + 10, y)
    ctx.restore()
  }

  /**
   * 渲染水印
   */
  renderWatermark() {
    const ctx = this.main.ctx
    const centerX = this.main.canvasSize.screenWidth / 2

    // 计算水印位置（游戏板下方，道具按钮上方的空白区域）
    const gameboardBottom = 80 + this.main.canvasSize.screenHeight * 0.5 // 游戏板底部
    const slotTop = this.main.canvasSize.screenHeight - 70 - 70 // 槽位顶部
    const watermarkY = gameboardBottom + (slotTop - gameboardBottom) / 2 // 中间位置

    // 保存当前状态
    ctx.save()

    // 设置透明度，让水印不太突兀
    ctx.globalAlpha = 0.6

    // 创建渐变文字效果
    const gradient = ctx.createLinearGradient(centerX - 100, watermarkY - 20, centerX + 100, watermarkY + 20)
    gradient.addColorStop(0, '#FF6B9D')    // 粉色
    gradient.addColorStop(0.3, '#FFD700')  // 金色
    gradient.addColorStop(0.6, '#FF8FB3')  // 浅粉色
    gradient.addColorStop(1, '#FFA500')    // 橙色

    // 绘制水印文字
    ctx.fillStyle = gradient
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)'
    ctx.lineWidth = 2
    ctx.font = 'bold 28px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'

    // 先绘制描边
    ctx.strokeText('鸭了个鸭呀', centerX, watermarkY)
    // 再绘制填充
    ctx.fillText('鸭了个鸭呀', centerX, watermarkY)

    // 添加一些装饰效果
    const time = Date.now() * 0.003

    // 绘制小星星装饰
    ctx.globalAlpha = 0.4
    for (let i = 0; i < 3; i++) {
      const offsetX = Math.sin(time + i * 2) * 80
      const offsetY = Math.cos(time * 0.5 + i) * 10
      const starX = centerX + offsetX
      const starY = watermarkY + offsetY
      const starSize = 4 + Math.sin(time * 2 + i) * 2

      ctx.fillStyle = '#FFD700'
      this.drawStar(ctx, starX, starY, starSize, 5)
    }

    // 恢复状态
    ctx.restore()
  }

  /**
   * 渲染胜利界面
   */
  renderWinScreen() {
    // 先渲染游戏界面作为背景
    this.renderGameScreen()

    // 使用新的胜利页面组件渲染
    if (this.main.winScreen) {
      this.main.winScreen.render(this.main.ctx)
    }
  }

  /**
   * 渲染失败界面
   */
  renderLoseScreen() {
    // 先渲染游戏界面
    this.renderGameScreen()

    // 添加失败遮罩
    const ctx = this.main.ctx
    const centerX = this.main.canvasSize.screenWidth / 2
    const centerY = this.main.canvasSize.screenHeight / 2

    // 半透明背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    ctx.fillRect(0, 0, this.main.canvasSize.screenWidth, this.main.canvasSize.screenHeight)

    // 失败文字
    ctx.fillStyle = '#FF6B6B'
    ctx.strokeStyle = '#8B0000'
    ctx.lineWidth = 3
    ctx.font = 'bold 36px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'

    ctx.strokeText('游戏失败', centerX, centerY - 50)
    ctx.fillText('游戏失败', centerX, centerY - 50)

    // 游戏统计 - 修复时间计算，确保不为负数
    ctx.fillStyle = '#FFFFFF'
    ctx.font = '18px Arial'

    // 计算用时，确保不为负数
    let playTime = 0
    if (this.main.endTime > 0 && this.main.startTime > 0 && this.main.endTime >= this.main.startTime) {
      playTime = Math.floor((this.main.endTime - this.main.startTime) / 1000)
    }

    ctx.fillText(`用时: ${playTime}秒`, centerX, centerY + 30)
    ctx.fillText(`总分: ${this.main.score}`, centerX, centerY + 60)

    // 重试提示
    ctx.font = '16px Arial'
    ctx.fillText('点击重新开始', centerX, centerY + 110)
  }

  /**
   * 渲染调试信息
   */
  renderDebugInfo() {
    if (!this.main.board) return

    const ctx = this.main.ctx
    ctx.save()

    // 渲染方块层级数字
    this.main.board.blocks.forEach(block => {
      if (block.isVisible) {
        // 绘制层级数字背景
        ctx.fillStyle = block.isClickable ? 'rgba(0, 255, 0, 0.8)' : 'rgba(255, 0, 0, 0.8)'
        ctx.fillRect(block.x - 2, block.y - 2, 16, 16)

        // 绘制层级数字
        ctx.fillStyle = '#FFFFFF'
        ctx.font = '12px Arial'
        ctx.textAlign = 'center'
        ctx.fillText(block.layer.toString(), block.x + 6, block.y + 10)

        // 如果不可点击，在方块上绘制红色X
        if (!block.isClickable) {
          ctx.strokeStyle = '#FF0000'
          ctx.lineWidth = 3
          ctx.beginPath()
          ctx.moveTo(block.x + 5, block.y + 5)
          ctx.lineTo(block.x + block.width - 5, block.y + block.height - 5)
          ctx.moveTo(block.x + block.width - 5, block.y + 5)
          ctx.lineTo(block.x + 5, block.y + block.height - 5)
          ctx.stroke()
        }
      }
    })

    // 显示调试信息面板
    const debugPanelY = this.main.canvasSize.screenHeight - 160
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)'
    ctx.fillRect(0, debugPanelY, this.main.canvasSize.screenWidth, 160)

    ctx.fillStyle = '#FFFFFF'
    ctx.font = '14px Arial'
    ctx.textAlign = 'left'

    const totalBlocks = this.main.board.blocks.length
    const visibleBlocks = this.main.board.blocks.filter(b => b.isVisible).length
    const clickableBlocks = this.main.board.blocks.filter(b => b.isVisible && b.isClickable).length

    ctx.fillText(`调试信息:`, 10, debugPanelY + 20)
    ctx.fillText(`总方块数: ${totalBlocks}`, 10, debugPanelY + 40)
    ctx.fillText(`可见方块: ${visibleBlocks}`, 10, debugPanelY + 60)
    ctx.fillText(`可点击方块: ${clickableBlocks}`, 10, debugPanelY + 80)

    // 显示层级分布
    const layerStats = new Map()
    this.main.board.blocks.forEach(block => {
      if (block.isVisible) {
        const count = layerStats.get(block.layer) || 0
        layerStats.set(block.layer, count + 1)
      }
    })

    let y = debugPanelY + 100
    ctx.fillText(`层级分布:`, 10, y)
    y += 20
    layerStats.forEach((count, layer) => {
      ctx.fillText(`层级${layer}: ${count}个方块`, 10, y)
      y += 15
    })

    ctx.restore()
  }

  /**
   * 绘制圆角矩形
   */
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
  }
}

// CommonJS导出
module.exports = RenderManager
