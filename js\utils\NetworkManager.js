const logger = require('AsyncLogger')

/**
 * 网络管理器
 * 负责与后端API服务器通信
 */
class NetworkManager {
  constructor() {
    // 后端服务器地址 - 开发环境
    this.baseURL = 'https://word2pic.chat2excel.top:63226'
    
    // 请求超时时间
    this.timeout = 10000
    
    // 请求重试次数
    this.maxRetries = 3
    
    // 请求计数器，用于跟踪请求
    this.requestCounter = 0
  }

  /**
   * 发送HTTP请求（带重试机制）
   * @param {string} url - 请求URL
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应数据
   */
  async request(url, options = {}) {
    const requestId = ++this.requestCounter
    const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`
    const maxRetries = options.maxRetries !== undefined ? options.maxRetries : this.maxRetries
    
    let lastError = null
    
    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      const requestOptions = {
        url: fullUrl,
        method: options.method || 'GET',
        timeout: this.timeout,
        header: {
          'content-type': 'application/json',
          ...options.headers
        },
        ...options
      }

      // 详细的请求日志
      if (attempt === 1) {
        logger.info(`🌐 [请求${requestId}] 发送网络请求:`)
        logger.info(`   方法: ${requestOptions.method}`)
        logger.info(`   URL: ${fullUrl}`)
        if (requestOptions.data) {
          logger.info(`   请求数据:`, JSON.parse(requestOptions.data))
        }
        logger.info(`   超时时间: ${this.timeout}ms`)
        logger.info(`   最大重试次数: ${maxRetries}`)
      } else {
        logger.info(`🔄 [请求${requestId}] 第${attempt-1}次重试 (${attempt}/${maxRetries + 1})`)
      }
      
      const startTime = Date.now()
      
      try {
        const result = await new Promise((resolve, reject) => {
          wx.request({
            ...requestOptions,
            success: (res) => {
              const duration = Date.now() - startTime
              
              logger.info(`✅ [请求${requestId}] 网络请求成功:`)
              logger.info(`   状态码: ${res.statusCode}`)
              logger.info(`   耗时: ${duration}ms`)
              if (attempt > 1) {
                logger.info(`   重试第${attempt-1}次成功`)
              }
              logger.info(`   响应数据:`, res.data)
              
              if (res.statusCode >= 200 && res.statusCode < 300) {
                // 检查业务逻辑是否成功
                if (res.data && res.data.success === false) {
                  logger.warn(`⚠️ [请求${requestId}] 业务逻辑失败:`, res.data.error || '未知错误')
                } else {
                  logger.info(`🎉 [请求${requestId}] 请求完全成功`)
                }
                resolve(res.data)
              } else {
                const errorMsg = `HTTP ${res.statusCode}: ${res.data?.error || '请求失败'}`
                logger.error(`❌ [请求${requestId}] HTTP状态码错误: ${errorMsg}`)
                reject(new Error(errorMsg))
              }
            },
            fail: (err) => {
              const duration = Date.now() - startTime
              
              logger.error(`❌ [请求${requestId}] 网络请求失败:`)
              logger.error(`   耗时: ${duration}ms`)
              logger.error(`   错误信息:`, err)
              logger.error(`   错误代码: ${err.errMsg || '未知错误'}`)
              if (attempt > 1) {
                logger.error(`   重试第${attempt-1}次失败`)
              }
              
              reject(new Error(`网络请求失败: ${err.errMsg || '未知错误'}`))
            }
          })
        })
        
        // 请求成功，返回结果
        return result
        
      } catch (error) {
        lastError = error
        
        if (attempt <= maxRetries) {
          // 计算重试延迟（指数退避：1秒、2秒、4秒）
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000)
          logger.warn(`⏳ [请求${requestId}] ${delay}ms后进行第${attempt}次重试...`)
          
          // 等待重试延迟
          await new Promise(resolve => setTimeout(resolve, delay))
        } else {
          // 所有重试都失败了
          logger.error(`💥 [请求${requestId}] 请求失败，已达到最大重试次数(${maxRetries})`)
          throw lastError
        }
      }
    }
  }

  /**
   * 发送GET请求
   * @param {string} url - 请求URL
   * @param {Object} params - 查询参数
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应数据
   */
  async get(url, params = {}, options = {}) {
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')
    
    const fullUrl = queryString ? `${url}?${queryString}` : url
    
    return this.request(fullUrl, {
      method: 'GET',
      ...options
    })
  }

  /**
   * 发送POST请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应数据
   */
  async post(url, data = {}, options = {}) {
    return this.request(url, {
      method: 'POST',
      data: JSON.stringify(data),
      ...options
    })
  }

  /**
   * 用户登录
   * @param {string} code - 微信登录凭证
   * @param {Object} userInfo - 用户信息
   * @returns {Promise<Object>} 登录结果
   */
  async userLogin(code, userInfo) {
    try {
      logger.info('🔐 开始用户登录流程...')
      logger.info('   登录凭证:', code)
      logger.info('   用户信息:', userInfo)
      
      const response = await this.post('/api/auth/login', {
        code: code,
        userInfo: userInfo
      })
      
      if (response.success) {
        logger.info('✅ 用户登录成功:')
        logger.info('   用户ID:', response.data.userId)
        logger.info('   会话密钥:', response.data.sessionKey ? '已获取' : '未获取')
        logger.info('   用户详情:', response.data.userInfo)
        return response.data
      } else {
        const errorMsg = response.error || '登录失败'
        logger.error('❌ 用户登录业务逻辑失败:', errorMsg)
        throw new Error(errorMsg)
      }
    } catch (error) {
      logger.error('❌ 用户登录异常:', error.message)
      throw error
    }
  }

  /**
   * 获取用户信息
   * @param {number} userId - 用户ID
   * @returns {Promise<Object>} 用户信息
   */
  async getUserInfo(userId) {
    try {
      logger.info(`👤 开始获取用户信息: userId=${userId}`)
      
      const response = await this.get('/api/user/info', { user_id: userId })
      
      if (response.success) {
        logger.info('✅ 用户信息获取成功:', response.data)
        return response.data
      } else {
        const errorMsg = response.error || '获取用户信息失败'
        logger.error('❌ 获取用户信息业务逻辑失败:', errorMsg)
        throw new Error(errorMsg)
      }
    } catch (error) {
      logger.error('❌ 获取用户信息异常:', error.message)
      throw error
    }
  }

  /**
   * 获取用户登录历史
   * @param {number} userId - 用户ID
   * @param {number} limit - 限制返回数量
   * @returns {Promise<Array>} 登录历史
   */
  async getUserLoginHistory(userId, limit = 10) {
    try {
      logger.info(`📝 开始获取用户登录历史: userId=${userId}, limit=${limit}`)
      
      const response = await this.get('/api/user/login-history', { 
        user_id: userId, 
        limit: limit 
      })
      
      if (response.success) {
        logger.info(`✅ 登录历史获取成功，共${response.data.length}条记录`)
        return response.data
      } else {
        const errorMsg = response.error || '获取登录历史失败'
        logger.error('❌ 获取登录历史业务逻辑失败:', errorMsg)
        throw new Error(errorMsg)
      }
    } catch (error) {
      logger.error('❌ 获取登录历史异常:', error.message)
      throw error
    }
  }

  /**
   * 获取关卡最佳记录
   * @param {number} userId - 用户ID
   * @param {number} level - 关卡编号
   * @returns {Promise<Object>} 关卡最佳记录
   */
  async getLevelRecord(userId, level) {
    try {
      logger.info(`📈 开始获取关卡记录: userId=${userId}, level=${level}`)
      
      const response = await this.get('/api/level/record', { 
        user_id: userId, 
        level: level 
      })
      
      if (response.success) {
        logger.info(`✅ 关卡${level}记录获取成功:`)
        logger.info('   最佳得分:', response.data?.bestScore || '无记录')
        logger.info('   最少步数:', response.data?.bestMoves || '无记录')
        logger.info('   最短时间:', response.data?.bestTime || '无记录')
        return response
      } else {
        const errorMsg = response.error || '获取关卡记录失败'
        logger.error(`❌ 关卡${level}记录获取业务逻辑失败:`, errorMsg)
        throw new Error(errorMsg)
      }
    } catch (error) {
      logger.error('❌ 获取关卡记录异常:', error.message)
      throw error
    }
  }

  /**
   * 设置后端服务器地址
   * @param {string} baseURL - 服务器地址
   */
  setBaseURL(baseURL) {
    this.baseURL = baseURL
    logger.info(`🔧 设置后端服务器地址: ${baseURL}`)
  }

  /**
   * 保存关卡通关记录
   * @param {number} userId - 用户ID
   * @param {number} level - 关卡编号
   * @param {number} score - 得分
   * @param {number} moves - 移动步数
   * @param {number} playTime - 游戏时长（秒）
   * @returns {Promise<Object>} 保存结果
   */
  async saveLevelProgress(userId, level, score, moves, playTime) {
    try {
      logger.info(`💾 开始保存关卡进度:`)
      logger.info(`   用户ID: ${userId}`)
      logger.info(`   关卡: ${level}`)
      logger.info(`   得分: ${score}`)
      logger.info(`   步数: ${moves}`)
      logger.info(`   时长: ${playTime}秒`)
      
      const response = await this.post('/api/level/complete', {
        userId: userId,
        level: level,
        score: score,
        moves: moves,
        playTime: playTime
      })
      
      if (response.success) {
        logger.info('✅ 关卡进度保存成功:')
        logger.info('   是否最佳记录:', response.data?.isBestRecord ? '是' : '否')
        logger.info('   排名:', response.data?.rank || '未提供')
        logger.info('   保存时间:', response.data?.saveTime || '未提供')
        return response
      } else {
        const errorMsg = response.error || '保存关卡进度失败'
        logger.error('❌ 保存关卡进度业务逻辑失败:', errorMsg)
        throw new Error(errorMsg)
      }
    } catch (error) {
      logger.error('❌ 保存关卡进度异常:', error.message)
      throw error
    }
  }

  /**
   * 获取用户通关进度
   * @param {number} userId - 用户ID
   * @returns {Promise<Object>} 用户进度信息
   */
  async getUserProgress(userId) {
    try {
      logger.info(`📊 开始获取用户进度: userId=${userId}`)
      
      const response = await this.get('/api/user/progress', { user_id: userId })
      
      if (response.success) {
        logger.info('✅ 用户进度获取成功:')
        logger.info('   最高关卡:', response.data?.maxLevel || '未提供')
        logger.info('   总得分:', response.data?.totalScore || '未提供')
        logger.info('   总游戏时长:', response.data?.totalPlayTime || '未提供')
        return response
      } else {
        const errorMsg = response.error || '获取用户进度失败'
        logger.error('❌ 获取用户进度业务逻辑失败:', errorMsg)
        throw new Error(errorMsg)
      }
    } catch (error) {
      logger.error('❌ 获取用户进度异常:', error.message)
      throw error
    }
  }

  /**
   * 提交用户反馈
   * @param {Object} feedbackData - 反馈数据
   * @param {string} feedbackData.content - 反馈内容
   * @param {number|null} feedbackData.userId - 用户ID（可选）
   * @param {string} feedbackData.timestamp - 时间戳
   * @param {string} feedbackData.gameVersion - 游戏版本
   * @param {string} feedbackData.platform - 平台信息
   * @returns {Promise<Object>} 提交结果
   */
  async submitFeedback(feedbackData) {
    try {
      logger.info('📝 开始提交用户反馈...')
      logger.info('   反馈数据:', {
        content: feedbackData.content.length > 50 ? 
          feedbackData.content.substring(0, 50) + '...' : 
          feedbackData.content,
        userId: feedbackData.userId,
        timestamp: feedbackData.timestamp,
        gameVersion: feedbackData.gameVersion,
        platform: feedbackData.platform
      })
      
      const response = await this.post('/api/feedback/submit', feedbackData)
      
      if (response.success) {
        logger.info('✅ 用户反馈提交成功:')
        logger.info('   反馈ID:', response.data?.feedbackId)
        logger.info('   提交时间:', response.data?.submittedAt)
        return response
      } else {
        const errorMsg = response.error || '反馈提交失败'
        logger.error('❌ 用户反馈提交业务逻辑失败:', errorMsg)
        throw new Error(errorMsg)
      }
    } catch (error) {
      logger.error('❌ 提交用户反馈异常:', error.message)
      throw error
    }
  }

  /**
   * 获取用户反馈历史
   * @param {number} userId - 用户ID
   * @param {number} limit - 返回数量限制，默认10条
   * @returns {Promise<Object>} 反馈历史数据
   */
  async getUserFeedbackHistory(userId, limit = 10) {
    try {
      logger.info('📖 获取用户反馈历史...')
      logger.info('   用户ID:', userId)
      logger.info('   数量限制:', limit)
      
      const response = await this.get('/api/feedback/history', {
        userId: userId,
        limit: limit
      })
      
      if (response.success) {
        logger.info('✅ 获取用户反馈历史成功:')
        logger.info('   反馈数量:', response.data?.feedbacks?.length || 0)
        return response.data
      } else {
        const errorMsg = response.error || '获取反馈历史失败'
        logger.error('❌ 获取用户反馈历史业务逻辑失败:', errorMsg)
        throw new Error(errorMsg)
      }
    } catch (error) {
      logger.error('❌ 获取用户反馈历史异常:', error.message)
      throw error
    }
  }

  /**
   * 检查网络连接
   * @returns {Promise<boolean>} 连接状态
   */
  async checkConnection() {
    try {
      // 简单的连接测试 - 尝试获取用户进度（如果有用户ID的话）
      // 这里可以改为一个更轻量的测试接口，或者直接返回true
      // 让实际的API调用来处理连接问题
      return true
    } catch (error) {
      return false
    }
  }
}

module.exports = NetworkManager 