const Button = require('./Button.js')
const logger = require('../utils/AsyncLogger.js')
// const FeedbackDialog = require('./FeedbackDialog.js') // 不再使用自定义反馈对话框

// 延迟加载DeveloperModeManager，避免循环依赖
let developerModeManager = null
function getDeveloperModeManager() {
  if (!developerModeManager) {
    try {
      developerModeManager = require('../utils/DeveloperModeManager')
    } catch (e) {
      return null
    }
  }
  return developerModeManager
}

/**
 * 设置面板类
 * 包含音效、背景音乐设置、意见反馈和返回关卡选择功能
 */
class SettingsPanel {
  /**
   * 构造函数
   * @param {number} x - 面板X坐标
   * @param {number} y - 面板Y坐标
   * @param {number} width - 面板宽度
   * @param {number} height - 面板高度
   * @param {Object} callbacks - 回调函数集合
   */
  constructor(x, y, width, height, callbacks) {
    this.x = x
    this.y = y
    this.width = width
    this.height = height
    this.callbacks = callbacks || {}
    
    // 面板状态
    this.isVisible = false
    this.isAnimating = false
    
    // 动画属性
    this.scale = 0
    this.targetScale = 0
    this.alpha = 0
    this.targetAlpha = 0
    
    // 设置状态
    this.soundEnabled = true
    this.musicEnabled = true


    // 样式配置
    this.backgroundColor = 'rgba(0, 0, 0, 0.8)'
    this.panelColor = 'rgba(255, 255, 255, 0.95)'
    this.borderRadius = 15
    this.padding = 20
    
    // 创建UI组件
    this.createUIComponents()
    
    // 创建意见反馈对话框
    this.createFeedbackDialog()
  }

  /**
   * 创建UI组件
   */
  createUIComponents() {
    const buttonWidth = 200
    const buttonHeight = 50
    const buttonSpacing = 20
    
    // 计算按钮位置
    const centerX = this.x + this.width / 2
    const startY = this.y + this.padding + 60
    
    // 音效开关按钮
    this.soundButton = new Button(
      centerX - buttonWidth / 2,
      startY,
      buttonWidth,
      buttonHeight,
      '音效: 开启',
      () => this.toggleSound()
    )
    this.soundButton.setTheme('blue')
    
    // 背景音乐开关按钮  
    this.musicButton = new Button(
      centerX - buttonWidth / 2,
      startY + buttonHeight + buttonSpacing,
      buttonWidth,
      buttonHeight,
      '背景音乐: 开启',
      () => this.toggleMusic()
    )
    this.musicButton.setTheme('green')
    
    // 重新开始本关按钮
    this.restartLevelButton = new Button(
      centerX - buttonWidth / 2,
      startY + (buttonHeight + buttonSpacing) * 2,
      buttonWidth,
      buttonHeight,
      '重新开始本关',
      () => this.restartLevel()
    )
    this.restartLevelButton.setTheme('purple')
    
    // 返回关卡选择按钮
    this.backToLevelsButton = new Button(
      centerX - buttonWidth / 2,
      startY + (buttonHeight + buttonSpacing) * 3,
      buttonWidth,
      buttonHeight,
      '回到关卡选择',
      () => this.backToLevels()
    )
    this.backToLevelsButton.setTheme('orange')
    
    // 意见反馈按钮
    this.feedbackButton = new Button(
      centerX - buttonWidth / 2,
      startY + (buttonHeight + buttonSpacing) * 4,
      buttonWidth,
      buttonHeight,
      '意见反馈',
      () => this.handleFeedbackClick()
    )
    this.feedbackButton.setTheme('cyan')
    
    // 关闭按钮
    this.closeButton = new Button(
      centerX - buttonWidth / 2,
      startY + (buttonHeight + buttonSpacing) * 5,
      buttonWidth,
      buttonHeight,
      '关闭',
      () => this.hide()
    )
    this.closeButton.setTheme('red')
    
    // 初始化按钮可见性
    this.updateButtonsVisibility()
  }

  /**
   * 显示面板
   */
  show() {
    if (this.isVisible) return
    
    this.isVisible = true
    this.isAnimating = true
    this.targetScale = 1
    this.targetAlpha = 1
    
    this.updateButtonsVisibility()
    
    // 播放显示音效
    if (this.callbacks.onShow) {
      this.callbacks.onShow()
    }
  }

  /**
   * 隐藏面板
   */
  hide() {
    if (!this.isVisible) return
    
    this.isAnimating = true
    this.targetScale = 0
    this.targetAlpha = 0
    
    // 延迟设置不可见状态
    setTimeout(() => {
      this.isVisible = false
      this.isAnimating = false
      this.updateButtonsVisibility()
    }, 300)
    
    // 播放隐藏音效
    if (this.callbacks.onHide) {
      this.callbacks.onHide()
    }
  }

  /**
   * 切换音效开关
   */
  toggleSound() {
    this.soundEnabled = !this.soundEnabled
    this.updateSoundButtonText()
    
    if (this.callbacks.onSoundToggle) {
      this.callbacks.onSoundToggle(this.soundEnabled)
    }
  }

  /**
   * 切换背景音乐开关
   */
  toggleMusic() {
    this.musicEnabled = !this.musicEnabled
    this.updateMusicButtonText()
    
    if (this.callbacks.onMusicToggle) {
      this.callbacks.onMusicToggle(this.musicEnabled)
    }
  }

  /**
   * 重新开始本关
   */
  restartLevel() {
    this.hide()
    
    if (this.callbacks.onRestartLevel) {
      this.callbacks.onRestartLevel()
    }
  }

  /**
   * 返回关卡选择
   */
  backToLevels() {
    this.hide()
    
    if (this.callbacks.onBackToLevels) {
      this.callbacks.onBackToLevels()
    }
  }

  /**
   * 处理意见反馈按钮点击
   */
  handleFeedbackClick() {
    this.showFeedback()
  }

  /**
   * 切换开发者模式
   */
  toggleDeveloperMode() {
    const devManager = getDeveloperModeManager()
    if (devManager) {
      const newState = devManager.toggle()

      // 显示切换结果
      const message = newState ?
        '🛠️ 开发者模式已启用！\n🔓 所有关卡已解锁\n📝 DEBUG日志已启用' :
        '🔒 开发者模式已禁用！\n🔐 关卡按顺序解锁\n⚠️ 仅显示警告日志'

      console.log(message)

      // 如果有微信小程序环境，显示Toast提示
      if (typeof wx !== 'undefined' && wx.showToast) {
        wx.showToast({
          title: newState ? '开发者模式已启用' : '开发者模式已禁用',
          icon: 'success',
          duration: 2000
        })
      }

      // 调用回调通知主程序
      if (this.callbacks.onDeveloperModeToggle) {
        this.callbacks.onDeveloperModeToggle(newState)
      }
    } else {
      console.error('❌ DeveloperModeManager不可用，无法切换开发者模式')
    }
  }

  /**
   * 显示意见反馈对话框
   */
  showFeedback() {
    // 直接使用微信小程序的输入对话框
    if (this.callbacks.onFeedbackInputFocus) {
      this.callbacks.onFeedbackInputFocus('', (inputText) => {
        // 如果用户输入了内容，直接提交
        if (inputText && inputText.trim()) {
          this.submitFeedback(inputText.trim())
        } else {
          // 如果没有输入内容，提交默认反馈
          this.submitFeedback('用户提交了游戏反馈（未填写具体内容）')
        }
      })
    }
  }


  /**
   * 创建意见反馈对话框
   * 注意：现在直接使用微信小程序的输入对话框，不再创建自定义FeedbackDialog
   */
  createFeedbackDialog() {
    // 不再创建FeedbackDialog，避免套娃问题
    // 反馈功能直接通过 showFeedback() 方法调用微信小程序的输入对话框
    this.feedbackDialog = null
  }

  /**
   * 提交用户反馈到数据库
   * @param {string} feedbackText - 反馈内容
   */
  async submitFeedback(feedbackText) {
    try {
      logger.info('📝 开始提交用户反馈...')
      logger.info('   反馈内容:', feedbackText)
      
      // 获取网络管理器实例
      const NetworkManager = require('../utils/NetworkManager.js')
      const networkManager = new NetworkManager()
      
      // 获取当前用户信息（通过回调或者直接访问）
      let userId = null
      let userInfo = null
      
      try {
        // 优先通过回调获取用户信息
        if (this.callbacks.getUserInfo) {
          userInfo = this.callbacks.getUserInfo()
        } else {
          // 备用方案：创建新的UserManager实例
          const UserManager = require('../utils/UserManager.js')
const logger = require('../utils/AsyncLogger')

          const userManager = new UserManager()
          userInfo = userManager.getUserInfo()
        }
        
        // 优先使用服务器用户ID，如果没有则使用本地用户ID
        userId = userInfo ? (userInfo.serverUserId || userInfo.userId) : null
        logger.info('📋 获取用户ID用于反馈:', {
          hasUserInfo: !!userInfo,
          serverUserId: userInfo?.serverUserId,
          localUserId: userInfo?.userId,
          finalUserId: userId
        })
      } catch (error) {
        logger.warn('⚠️ 无法获取用户信息，以匿名方式提交反馈:', error.message)
      }
      
      // 构造反馈数据
      const feedbackData = {
        content: feedbackText,
        userId: userId || 'anonymous', // 如果没有用户ID，使用匿名标识
        timestamp: new Date().toISOString(),
        gameVersion: '1.0.0',  // 可以从配置文件读取
        platform: this.detectPlatform()
      }
      
      // 如果用户ID为空，添加额外的标识信息
      if (!userId) {
        feedbackData.isAnonymous = true
        feedbackData.deviceInfo = this.getDeviceInfo()
        logger.info('📋 以匿名方式提交反馈')
      }
      
      // 发送反馈到后端
      const response = await networkManager.submitFeedback(feedbackData)
      
      if (response && response.success) {
        logger.info('✅ 反馈提交成功:', response.message)
        return response
      } else {
        throw new Error(response?.error || '提交失败')
      }
      
    } catch (error) {
      logger.error('❌ 提交反馈时发生错误:', error)
      throw error
    }
  }

  /**
   * 检测当前平台
   * @returns {string} 平台名称
   */
  detectPlatform() {
    if (typeof wx !== 'undefined') {
      return 'WeChat Mini Program'
    } else if (typeof window !== 'undefined') {
      return 'Web Browser'
    } else {
      return 'Unknown'
    }
  }

  /**
   * 获取设备信息（用于匿名反馈）
   * @returns {Object} 设备信息
   */
  getDeviceInfo() {
    const deviceInfo = {
      timestamp: Date.now(),
      userAgent: 'unknown'
    }

    try {
      if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
        const systemInfo = wx.getSystemInfoSync()
        deviceInfo.platform = systemInfo.platform
        deviceInfo.system = systemInfo.system
        deviceInfo.model = systemInfo.model
        deviceInfo.brand = systemInfo.brand
        deviceInfo.screenWidth = systemInfo.screenWidth
        deviceInfo.screenHeight = systemInfo.screenHeight
      } else if (typeof window !== 'undefined') {
        deviceInfo.userAgent = window.navigator.userAgent
        deviceInfo.screenWidth = window.screen.width
        deviceInfo.screenHeight = window.screen.height
      }
    } catch (error) {
      logger.warn('获取设备信息失败:', error.message)
    }

    return deviceInfo
  }

  /**
   * 更新音效按钮文字
   */
  updateSoundButtonText() {
    this.soundButton.setText(`音效: ${this.soundEnabled ? '开启' : '关闭'}`)
    this.soundButton.setTheme(this.soundEnabled ? 'blue' : 'red')
  }

  /**
   * 更新背景音乐按钮文字
   */
  updateMusicButtonText() {
    this.musicButton.setText(`背景音乐: ${this.musicEnabled ? '开启' : '关闭'}`)
    this.musicButton.setTheme(this.musicEnabled ? 'green' : 'red')
  }

  /**
   * 设置音效状态
   * @param {boolean} enabled - 是否启用音效
   */
  setSoundEnabled(enabled) {
    this.soundEnabled = enabled
    this.updateSoundButtonText()
  }

  /**
   * 设置背景音乐状态
   * @param {boolean} enabled - 是否启用背景音乐
   */
  setMusicEnabled(enabled) {
    this.musicEnabled = enabled
    this.updateMusicButtonText()
  }

  /**
   * 更新按钮可见性
   */
  updateButtonsVisibility() {
    const visible = this.isVisible || this.isAnimating
    
    this.soundButton.setVisible(visible)
    this.musicButton.setVisible(visible)
    this.restartLevelButton.setVisible(visible)
    this.feedbackButton.setVisible(visible)
    this.backToLevelsButton.setVisible(visible)
    this.closeButton.setVisible(visible)
  }

  /**
   * 处理点击事件
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {boolean} 是否处理了点击
   */
  handleClick(x, y) {
    if (!this.isVisible) return false
    
    // 不再需要处理自定义反馈对话框的点击，直接使用微信小程序输入框
    
    // 检查是否点击了按钮
    if (this.soundButton.handleClick(x, y)) return true
    if (this.musicButton.handleClick(x, y)) return true
    if (this.restartLevelButton.handleClick(x, y)) return true
    if (this.feedbackButton.handleClick(x, y)) return true
    if (this.backToLevelsButton.handleClick(x, y)) return true
    if (this.closeButton.handleClick(x, y)) return true
    
    // 检查是否点击了面板外部
    if (!this.containsPoint(x, y)) {
      this.hide()
      return true
    }
    
    return false
  }

  /**
   * 检查点是否在面板内
   * @param {number} x - 点的X坐标
   * @param {number} y - 点的Y坐标
   * @returns {boolean}
   */
  containsPoint(x, y) {
    return x >= this.x && 
           x <= this.x + this.width &&
           y >= this.y && 
           y <= this.y + this.height
  }

  /**
   * 更新面板状态
   * @param {number} deltaTime - 时间差（毫秒）
   */
  update(deltaTime) {
    // 更新缩放动画
    const scaleDiff = this.targetScale - this.scale
    if (Math.abs(scaleDiff) > 0.01) {
      this.scale += scaleDiff * 0.15
    } else {
      this.scale = this.targetScale
    }

    // 更新透明度动画
    const alphaDiff = this.targetAlpha - this.alpha
    if (Math.abs(alphaDiff) > 0.01) {
      this.alpha += alphaDiff * 0.15
    } else {
      this.alpha = this.targetAlpha
    }

    // 更新按钮
    if (this.isVisible || this.isAnimating) {
      this.soundButton.update(deltaTime)
      this.musicButton.update(deltaTime)
      this.restartLevelButton.update(deltaTime)
      this.feedbackButton.update(deltaTime)
      this.backToLevelsButton.update(deltaTime)
      this.closeButton.update(deltaTime)
    }
    
    // 不再需要更新自定义反馈对话框
  }

  /**
   * 渲染面板
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    if (!this.isVisible && !this.isAnimating) return
    if (this.alpha <= 0) return

    ctx.save()

    // 设置整体透明度
    ctx.globalAlpha = this.alpha

    // 绘制背景遮罩
    this.drawBackgroundMask(ctx)

    // 应用缩放动画
    const centerX = this.x + this.width / 2
    const centerY = this.y + this.height / 2
    ctx.translate(centerX, centerY)
    ctx.scale(this.scale, this.scale)
    ctx.translate(-centerX, -centerY)

    // 绘制面板背景
    this.drawPanelBackground(ctx)

    // 绘制标题
    this.drawTitle(ctx)

    // 绘制按钮
    if (this.scale > 0.5) { // 只有在缩放到一定程度时才绘制按钮
      this.soundButton.render(ctx)
      this.musicButton.render(ctx)
      this.restartLevelButton.render(ctx)
      this.feedbackButton.render(ctx)
      this.backToLevelsButton.render(ctx)
      this.closeButton.render(ctx)
    }

    ctx.restore()
    
    // 不再需要渲染自定义反馈对话框
  }

  /**
   * 绘制背景遮罩
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  drawBackgroundMask(ctx) {
    ctx.fillStyle = this.backgroundColor
    ctx.fillRect(0, 0, ctx.canvas.width, ctx.canvas.height)
  }

  /**
   * 绘制面板背景
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  drawPanelBackground(ctx) {
    // 绘制圆角矩形背景
    this.drawRoundedRect(ctx, this.x, this.y, this.width, this.height, this.borderRadius, this.panelColor)
    
    // 绘制边框
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)'
    ctx.lineWidth = 2
    this.strokeRoundedRect(ctx, this.x, this.y, this.width, this.height, this.borderRadius)
  }

  /**
   * 绘制标题
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  drawTitle(ctx) {
    ctx.fillStyle = '#333333'
    ctx.font = 'bold 24px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    
    const titleX = this.x + this.width / 2
    const titleY = this.y + this.padding + 20
    
    ctx.fillText('游戏设置', titleX, titleY)
  }

  /**
   * 绘制圆角矩形
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} radius - 圆角半径
   * @param {string} color - 填充颜色
   */
  drawRoundedRect(ctx, x, y, width, height, radius, color) {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
    
    if (color) {
      ctx.fillStyle = color
      ctx.fill()
    }
  }

  /**
   * 描边圆角矩形
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} radius - 圆角半径
   */
  strokeRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
    ctx.stroke()
  }

  /**
   * 获取面板边界
   * @returns {Object} 边界信息
   */
  getBounds() {
    return {
      x: this.x,
      y: this.y,
      width: this.width,
      height: this.height
    }
  }
}

module.exports = SettingsPanel 