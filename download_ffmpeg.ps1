# Download FFmpeg portable version
Write-Host "Download FFmpeg..." -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

$ffmpegUrl = "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip"
$zipFile = "ffmpeg.zip"
$extractDir = "ffmpeg"

try {
    # Download FFmpeg
    Write-Host "Downloading FFmpeg..." -ForegroundColor Yellow
    Invoke-WebRequest -Uri $ffmpegUrl -OutFile $zipFile -UseBasicParsing
    Write-Host "Download completed" -ForegroundColor Green

    # Extract files
    Write-Host "Extracting..." -ForegroundColor Yellow
    Expand-Archive -Path $zipFile -DestinationPath $extractDir -Force
    
    # Find ffmpeg.exe
    $ffmpegExe = Get-ChildItem -Path $extractDir -Name "ffmpeg.exe" -Recurse | Select-Object -First 1
    if ($ffmpegExe) {
        $ffmpegPath = Get-ChildItem -Path $extractDir -Name "ffmpeg.exe" -Recurse | Select-Object -First 1 -ExpandProperty FullName
        $ffmpegDir = Split-Path $ffmpegPath -Parent
        
        # Copy to current directory
        Copy-Item $ffmpegPath -Destination "." -Force
        Write-Host "FFmpeg copied to current directory" -ForegroundColor Green
        
        # Test FFmpeg
        Write-Host "Testing FFmpeg..." -ForegroundColor Yellow
        .\ffmpeg.exe -version | Select-Object -First 3
        
        Write-Host ""
        Write-Host "FFmpeg installation successful!" -ForegroundColor Green
        Write-Host "Now you can run: node convert_audio.js" -ForegroundColor Cyan
    } else {
        Write-Host "Cannot find ffmpeg.exe" -ForegroundColor Red
    }

    # Clean up temporary files
    Remove-Item $zipFile -Force -ErrorAction SilentlyContinue
    Remove-Item $extractDir -Recurse -Force -ErrorAction SilentlyContinue
    
} catch {
    Write-Host "Download failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please download manually: $ffmpegUrl" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Run: node convert_audio.js" -ForegroundColor White
Write-Host "2. After conversion: node finalize_audio_conversion.js" -ForegroundColor White
Write-Host "3. Recompile and test the mini-game" -ForegroundColor White 