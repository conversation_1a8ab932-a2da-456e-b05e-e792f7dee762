const NetworkManager = require('./NetworkManager.js')
const logger = require('AsyncLogger')


/**
 * 卡通图片加载器
 * 用于从后端API加载卡通动物图片
 */
class CartoonImageLoader {
  /**
   * 检查微信小游戏环境
   */
  static checkEnvironment() {
    logger.info('🔍 检查微信小游戏环境:')
    logger.info('  wx:', typeof wx !== 'undefined' ? '✓ 存在' : '✗ 不存在')
    logger.info('  wx.createImage:', typeof wx.createImage === 'function' ? '✓ 可用' : '✗ 不可用')
    logger.info('  NetworkManager:', typeof NetworkManager !== 'undefined' ? '✓ 存在' : '✗ 不存在')
  }

  /**
   * 从后端API加载卡通动物图片
   * @returns {Promise<Object>} 包含所有动物图片的对象
   */
  static async loadCartoonImages() {
    const images = {}
    
    // 检查环境
    this.checkEnvironment()
    
    try {
      logger.info('🎨 开始从后端API加载卡通动物图片...')
      
      // 创建网络管理器实例
      const networkManager = new NetworkManager()
      
      // 获取图片列表
      const imageList = await this.getImageListFromServer(networkManager)
      
      if (!imageList || imageList.length === 0) {
        logger.warn('⚠️ 后端返回的图片列表为空，直接使用本地备用图片')
        // 直接使用本地备用图片，不再尝试加载默认的后端图片
        return await this.loadLocalFallbackImages()
      }
      
      logger.info(`📋 获取到 ${imageList.length} 个图片配置`)
      
      // 并发加载所有图片
      const loadPromises = imageList.map(async (imageConfig, index) => {
        const animalKey = `animal${index + 1}` // animal1, animal2, ...
        
        try {
          logger.info(`🔍 开始加载图片: ${animalKey} -> ID:${imageConfig.id} URL:${imageConfig.url || 'N/A'}`)
          
          const img = await this.loadImageFromServer(networkManager, imageConfig)
          
          if (img) {
            images[animalKey] = img
            logger.info(`✅ 图片加载成功: ${animalKey}`)
            return { key: animalKey, img, success: true, config: imageConfig }
          } else {
            logger.warn(`❌ 图片加载失败: ${animalKey}`)
            return { key: animalKey, img: null, success: false, config: imageConfig }
          }
        } catch (error) {
          logger.error(`💥 图片加载异常: ${animalKey}`, error.message)
          return { key: animalKey, img: null, success: false, config: imageConfig, error: error.message }
        }
      })
      
      const results = await Promise.all(loadPromises)
      
      // 统计加载结果
      const successCount = results.filter(r => r.success).length
      const totalCount = results.length
      
      logger.info(`🎨 图片加载完成: ${successCount}/${totalCount} 张成功`)
      
      // 详细输出结果
      results.forEach(result => {
        if (result.success) {
          logger.info(`  ✓ ${result.key}: ID:${result.config.id} 成功`)
        } else {
          logger.info(`  ✗ ${result.key}: ${result.error || '失败'}`)
        }
      })
      
      if (successCount === 0) {
        logger.warn('⚠️ 所有后端图片加载失败，使用本地备用图片')
        return await this.loadLocalFallbackImages()
      }
      
      return images
      
    } catch (error) {
      logger.error('❌ 从后端加载图片失败:', error.message)
      
      // 如果后端加载失败，尝试加载本地备用图片
      logger.info('🔄 尝试加载本地备用图片...')
      return await this.loadLocalFallbackImages()
    }
  }

  /**
   * 从服务器获取图片列表配置
   * @param {NetworkManager} networkManager - 网络管理器实例
   * @returns {Promise<Array>} 图片配置列表
   */
  static async getImageListFromServer(networkManager) {
    const requestStartTime = Date.now()
    const apiEndpoint = '/api/images/list'
    const fullUrl = `${networkManager.baseURL}${apiEndpoint}`
    
    try {
      logger.info('📡 ========== 开始请求图片列表配置 ==========')
      logger.info(`🔗 请求URL: ${fullUrl}`)
      logger.info(`📋 请求方法: GET`)
      logger.info(`⏰ 请求时间: ${new Date().toISOString()}`)
      logger.info(`🌐 基础域名: ${networkManager.baseURL}`)
      logger.info(`📍 API端点: ${apiEndpoint}`)
      
      const response = await networkManager.get(apiEndpoint)
      const requestDuration = Date.now() - requestStartTime
      
      logger.info('📡 ========== 收到响应 ==========')
      logger.info(`⏱️ 请求耗时: ${requestDuration}ms`)
      logger.info(`📊 响应状态: ${response ? '成功' : '失败'}`)
      
      if (response) {
        logger.info(`📋 响应数据结构:`)
        logger.info(`  - success: ${response.success}`)
        logger.info(`  - data: ${response.data ? `数组，长度${response.data.length}` : '空或未定义'}`)
        logger.info(`  - message: ${response.message || '无'}`)
        logger.info(`📄 完整响应:`, JSON.stringify(response, null, 2))
        
        if (response.success && response.data) {
          logger.info(`✅ 图片列表获取成功: ${response.data.length} 个图片`)
          
          // 详细输出每个图片配置
          response.data.forEach((item, index) => {
            logger.info(`  ${index + 1}. ID: ${item.id || 'N/A'}, URL: ${item.url || 'N/A'}`)
          })
          
          return response.data
        } else {
          logger.warn('⚠️ 响应格式不正确或success为false')
          logger.warn(`   - success字段: ${response.success}`)
          logger.warn(`   - data字段: ${response.data}`)
          return null
        }
      } else {
        logger.error('❌ 响应为空或未定义')
        return null
      }
    } catch (error) {
      const requestDuration = Date.now() - requestStartTime
      
      logger.info('📡 ========== 请求异常 ==========')
      logger.error(`❌ 请求图片列表失败`)
      logger.error(`⏱️ 请求耗时: ${requestDuration}ms`)
      logger.error(`🚨 错误类型: ${error.name || 'Unknown'}`)
      logger.error(`💬 错误信息: ${error.message}`)
      logger.error(`📍 错误堆栈:`, error.stack)
      
      // 如果是网络错误，输出更多信息
      if (error.code) {
        logger.error(`🔢 错误代码: ${error.code}`)
      }
      if (error.status) {
        logger.error(`📊 HTTP状态码: ${error.status}`)
      }
      
      return null
    } finally {
      logger.info('📡 ========== 请求结束 ==========\n')
    }
  }

  /**
   * 从服务器加载单张图片（带重试机制）
   * @param {NetworkManager} networkManager - 网络管理器实例
   * @param {Object} imageConfig - 图片配置 {id, url}
   * @param {number} maxRetries - 最大重试次数，默认3次
   * @returns {Promise<Image|null>}
   */
  static async loadImageFromServer(networkManager, imageConfig, maxRetries = 3) {
    let lastError = null
    
    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        let imageUrl
        
        if (imageConfig.url) {
          // 检查URL是否为相对路径
          if (imageConfig.url.startsWith('http://') || imageConfig.url.startsWith('https://')) {
            // 完整URL，直接使用
            imageUrl = imageConfig.url
          } else {
            // 相对路径，拼接基础域名
            imageUrl = `${networkManager.baseURL}${imageConfig.url}`
          }
        } else if (imageConfig.id) {
          // 如果只有ID，构建API URL
          imageUrl = `${networkManager.baseURL}/api/images/${imageConfig.id}`
        } else {
          throw new Error('图片配置缺少URL或ID')
        }
        
        if (attempt === 1) {
          logger.info(`    🔗 开始加载图片: ${imageUrl}`)
          logger.info(`    📋 图片信息: ID:${imageConfig.id || 'N/A'}`)
          logger.info(`    🔄 最大重试次数: ${maxRetries}`)
        } else {
          logger.info(`    🔄 第${attempt-1}次重试加载图片: ${imageUrl}`)
        }
        
        const img = wx.createImage()
        
        const result = await new Promise((resolve) => {
          // 设置超时
          const timeout = setTimeout(() => {
            logger.info(`    ⏰ 图片加载超时: ${imageUrl} (尝试${attempt}/${maxRetries + 1})`)
            resolve(null)
          }, 10000) // 10秒超时
          
          img.onload = () => {
            clearTimeout(timeout)
            const successMsg = attempt === 1 
              ? `    ✅ 图片加载成功: ${imageUrl} (尺寸: ${img.width}x${img.height})`
              : `    ✅ 图片重试第${attempt-1}次成功: ${imageUrl} (尺寸: ${img.width}x${img.height})`
            logger.info(successMsg)
            resolve(img)
          }
          
          img.onerror = (error) => {
            clearTimeout(timeout)
            const errorMsg = attempt === 1
              ? `    ❌ 图片加载失败: ${imageUrl}`
              : `    ❌ 图片重试第${attempt-1}次失败: ${imageUrl}`
            logger.info(errorMsg, error)
            resolve(null)
          }
          
          // 设置图片源
          img.src = imageUrl
        })
        
        if (result) {
          // 加载成功，返回图片
          return result
        } else {
          // 这次尝试失败，记录错误并继续重试
          throw new Error(`图片加载失败: ${imageUrl}`)
        }
        
      } catch (error) {
        lastError = error
        
        if (attempt <= maxRetries) {
          // 计算重试延迟（指数退避：1秒、2秒、4秒）
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000)
          logger.warn(`    ⏳ ${delay}ms后进行第${attempt}次重试图片加载...`)
          
          // 等待重试延迟
          await new Promise(resolve => setTimeout(resolve, delay))
        } else {
          // 所有重试都失败了
          logger.error(`    💥 图片加载失败，已达到最大重试次数(${maxRetries}): ${error.message}`)
          return null
        }
      }
    }
    
    return null
  }

  /**
   * 获取默认图片配置（当后端不可用时）
   * @returns {Array} 默认图片配置列表
   */
  static getDefaultImageList() {
    return [
      { id: 1, description: '可爱小动物1' },
      { id: 2, description: '可爱小动物2' },
      { id: 3, description: '可爱小动物3' },
      { id: 4, description: '可爱小动物4' },
      { id: 5, description: '可爱小动物5' },
      { id: 6, description: '可爱小动物6' },
      { id: 7, description: '可爱小动物7' },
      { id: 8, description: '可爱小动物8' }
    ]
  }

  /**
   * 加载本地备用图片（当后端不可用时）
   * @returns {Promise<Object>} 包含本地备用图片的对象
   */
  static async loadLocalFallbackImages() {
    const images = {}
    
    logger.info('🎨 开始加载本地备用图片...')
    
    // 定义本地图片路径映射
    const localImagePaths = {
      animal1: ['images/cartoon_animals/duck3.png', './images/cartoon_animals/duck3.png'],
      animal2: ['images/cartoon_animals/bear1.png', './images/cartoon_animals/bear1.png'],
      animal3: ['images/cartoon_animals/rabbit1.png', './images/cartoon_animals/rabbit1.png']
    }
    
    // 尝试加载本地图片
    const loadPromises = Object.entries(localImagePaths).map(async ([key, paths]) => {
      for (const path of paths) {
        try {
          const img = await this.loadLocalImage(path)
          if (img) {
            images[key] = img
            logger.info(`✅ 本地图片加载成功: ${key} -> ${path}`)
            return { key, success: true }
          }
        } catch (error) {
          logger.info(`⚠️ 本地图片加载失败: ${key} -> ${path}`)
        }
      }
      return { key, success: false }
    })
    
    await Promise.all(loadPromises)
    
    const successCount = Object.keys(images).length
    logger.info(`🎨 本地备用图片加载完成: ${successCount} 张成功`)
    
    return images
  }

  /**
   * 加载本地图片文件
   * @param {string} src - 图片路径
   * @returns {Promise<Image|null>}
   */
  static loadLocalImage(src) {
    return new Promise((resolve) => {
      const img = wx.createImage()
      
      const timeout = setTimeout(() => {
        resolve(null)
      }, 3000)
      
      img.onload = () => {
        clearTimeout(timeout)
        resolve(img)
      }
      
      img.onerror = () => {
        clearTimeout(timeout)
        resolve(null)
      }
      
      img.src = src
    })
  }

  /**
   * 获取所需的图片数量
   * @returns {number} 图片数量
   */
  static getRequiredImageCount() {
    return 8 // 游戏需要8种不同的动物图片
  }

  /**
   * 获取图片命名规则说明
   * @returns {Array} 图片信息数组
   */
  static getImageNamingInfo() {
    return [
      { key: 'animal1', description: '动物图片1 - 对应方块类型0' },
      { key: 'animal2', description: '动物图片2 - 对应方块类型1' },
      { key: 'animal3', description: '动物图片3 - 对应方块类型2' },
      { key: 'animal4', description: '动物图片4 - 对应方块类型3' },
      { key: 'animal5', description: '动物图片5 - 对应方块类型4' },
      { key: 'animal6', description: '动物图片6 - 对应方块类型5' },
      { key: 'animal7', description: '动物图片7 - 对应方块类型6' },
      { key: 'animal8', description: '动物图片8 - 对应方块类型7' }
    ]
  }
}

module.exports = CartoonImageLoader