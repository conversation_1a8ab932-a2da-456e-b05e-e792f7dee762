/**
 * 分享对话框组件
 * 用于提示用户分享获得额外道具次数
 */
class ShareDialog {
  constructor(x, y, width, height, powerUpType) {
    this.x = x
    this.y = y
    this.width = width
    this.height = height
    this.powerUpType = powerUpType // 'undo' 或 'shuffle'
    
    // 对话框状态
    this.visible = false
    this.alpha = 0
    this.scale = 0.8
    
    // 按钮区域
    this.confirmButton = null
    this.cancelButton = null
    
    // 动画
    this.animating = false
    this.targetAlpha = 0
    this.targetScale = 0.8
    
    // 文本内容
    this.title = this.powerUpType === 'undo' ? '撤回次数已用完' : '洗牌次数已用完'
    this.message = '分享给好友或朋友圈\n即可获得1次使用机会'
    
    this.setupButtons()
  }
  
  /**
   * 设置按钮区域
   */
  setupButtons() {
    const buttonWidth = 100
    const buttonHeight = 40
    const buttonSpacing = 20
    const buttonY = this.y + this.height - 60
    
    // 确认按钮
    this.confirmButton = {
      x: this.x + this.width / 2 - buttonWidth - buttonSpacing / 2,
      y: buttonY,
      width: buttonWidth,
      height: buttonHeight,
      text: '去分享',
      color: '#4CAF50',
      hoverColor: '#45a049'
    }
    
    // 取消按钮
    this.cancelButton = {
      x: this.x + this.width / 2 + buttonSpacing / 2,
      y: buttonY,
      width: buttonWidth,
      height: buttonHeight,
      text: '取消',
      color: '#f44336',
      hoverColor: '#da190b'
    }
    
    console.log(`分享对话框按钮设置:`)
    console.log(`  对话框: (${this.x}, ${this.y}) 尺寸: ${this.width}x${this.height}`)
    console.log(`  确认按钮: (${this.confirmButton.x}, ${this.confirmButton.y}) 尺寸: ${this.confirmButton.width}x${this.confirmButton.height}`)
    console.log(`  取消按钮: (${this.cancelButton.x}, ${this.cancelButton.y}) 尺寸: ${this.cancelButton.width}x${this.cancelButton.height}`)
  }
  
  /**
   * 显示对话框
   */
  show() {
    this.visible = true
    this.animating = true
    this.targetAlpha = 1
    this.targetScale = 1
  }
  
  /**
   * 隐藏对话框
   */
  hide() {
    this.animating = true
    this.targetAlpha = 0
    this.targetScale = 0.8
  }
  
  /**
   * 更新动画
   * @param {number} deltaTime - 时间增量
   */
  update(deltaTime) {
    if (!this.animating) return
    
    // 更新透明度
    const alphaDiff = this.targetAlpha - this.alpha
    if (Math.abs(alphaDiff) > 0.01) {
      this.alpha += alphaDiff * 0.15
    } else {
      this.alpha = this.targetAlpha
    }
    
    // 更新缩放
    const scaleDiff = this.targetScale - this.scale
    if (Math.abs(scaleDiff) > 0.01) {
      this.scale += scaleDiff * 0.15
    } else {
      this.scale = this.targetScale
    }
    
    // 检查动画是否完成
    if (this.alpha === this.targetAlpha && this.scale === this.targetScale) {
      this.animating = false
      if (this.alpha === 0) {
        this.visible = false
      }
    }
  }
  
  /**
   * 渲染对话框
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    if (!this.visible) return
    
    ctx.save()
    
    // 应用透明度
    ctx.globalAlpha = this.alpha * 0.9
    
    // 绘制背景遮罩
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
    ctx.fillRect(0, 0, ctx.canvas.width / ctx.getTransform().a, ctx.canvas.height / ctx.getTransform().d)
    
    // 应用缩放和位置
    ctx.translate(this.x + this.width / 2, this.y + this.height / 2)
    ctx.scale(this.scale, this.scale)
    ctx.translate(-(this.x + this.width / 2), -(this.y + this.height / 2))
    
    // 绘制对话框背景
    ctx.fillStyle = '#FFFFFF'
    this.drawRoundedRect(ctx, this.x, this.y, this.width, this.height, 10)
    ctx.fill()
    
    // 绘制边框
    ctx.strokeStyle = '#E0E0E0'
    ctx.lineWidth = 2
    this.drawRoundedRect(ctx, this.x, this.y, this.width, this.height, 10)
    ctx.stroke()
    
    // 绘制标题
    ctx.fillStyle = '#333333'
    ctx.font = 'bold 20px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'top'
    ctx.fillText(this.title, this.x + this.width / 2, this.y + 30)
    
    // 绘制消息
    ctx.fillStyle = '#666666'
    ctx.font = '16px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    const lines = this.message.split('\n')
    lines.forEach((line, index) => {
      ctx.fillText(line, this.x + this.width / 2, this.y + 80 + index * 25)
    })
    
    // 绘制分享图标
    this.drawShareIcon(ctx, this.x + this.width / 2, this.y + 140)
    
    // 绘制按钮
    this.drawButton(ctx, this.confirmButton)
    this.drawButton(ctx, this.cancelButton)
    
    ctx.restore()
  }
  
  /**
   * 绘制分享图标
   */
  drawShareIcon(ctx, x, y) {
    ctx.save()
    
    // 绘制简单的分享图标
    ctx.strokeStyle = '#4CAF50'
    ctx.lineWidth = 3
    ctx.lineCap = 'round'
    
    // 绘制三个连接的圆点
    const radius = 6
    const distance = 25
    
    // 中心点
    ctx.beginPath()
    ctx.arc(x, y, radius, 0, Math.PI * 2)
    ctx.fill()
    
    // 左上点
    ctx.beginPath()
    ctx.arc(x - distance, y - distance, radius, 0, Math.PI * 2)
    ctx.fill()
    
    // 右上点
    ctx.beginPath()
    ctx.arc(x + distance, y - distance, radius, 0, Math.PI * 2)
    ctx.fill()
    
    // 连接线
    ctx.beginPath()
    ctx.moveTo(x, y)
    ctx.lineTo(x - distance, y - distance)
    ctx.moveTo(x, y)
    ctx.lineTo(x + distance, y - distance)
    ctx.stroke()
    
    ctx.restore()
  }
  
  /**
   * 绘制按钮
   */
  drawButton(ctx, button) {
    // 绘制按钮背景
    ctx.fillStyle = button.color
    this.drawRoundedRect(ctx, button.x, button.y, button.width, button.height, 5)
    ctx.fill()
    
    // 绘制按钮文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(button.text, button.x + button.width / 2, button.y + button.height / 2)
  }
  
  /**
   * 绘制圆角矩形
   */
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
  }
  
  /**
   * 处理点击事件
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {string|null} - 返回 'confirm', 'cancel' 或 null
   */
  handleClick(x, y) {
    // 只要对话框可见就允许点击，不管是否在动画中
    if (!this.visible) return null
    
    console.log(`分享对话框点击检测:`)
    console.log(`  原始点击坐标: (${x}, ${y})`)
    console.log(`  对话框状态: visible=${this.visible}, animating=${this.animating}, scale=${this.scale}`)
    console.log(`  确认按钮区域: (${this.confirmButton.x}, ${this.confirmButton.y}) 到 (${this.confirmButton.x + this.confirmButton.width}, ${this.confirmButton.y + this.confirmButton.height})`)
    console.log(`  取消按钮区域: (${this.cancelButton.x}, ${this.cancelButton.y}) 到 (${this.cancelButton.x + this.cancelButton.width}, ${this.cancelButton.y + this.cancelButton.height})`)
    
    // 先尝试直接点击检测，不考虑缩放变换
    console.log(`  测试直接点击检测（无缩放变换）:`)
    if (this.isPointInButton(x, y, this.confirmButton)) {
      console.log(`  -> 直接命中确认按钮`)
      return 'confirm'
    }
    
    if (this.isPointInButton(x, y, this.cancelButton)) {
      console.log(`  -> 直接命中取消按钮`)
      return 'cancel'
    }
    
    // 如果直接检测失败，尝试缩放变换
    console.log(`  测试缩放变换后的点击检测:`)
    const centerX = this.x + this.width / 2
    const centerY = this.y + this.height / 2
    const transformedX = (x - centerX) / this.scale + centerX
    const transformedY = (y - centerY) / this.scale + centerY
    
    console.log(`  对话框中心: (${centerX}, ${centerY})`)
    console.log(`  变换后坐标: (${transformedX.toFixed(1)}, ${transformedY.toFixed(1)})`)
    
    if (this.isPointInButton(transformedX, transformedY, this.confirmButton)) {
      console.log(`  -> 变换后命中确认按钮`)
      return 'confirm'
    }
    
    if (this.isPointInButton(transformedX, transformedY, this.cancelButton)) {
      console.log(`  -> 变换后命中取消按钮`)
      return 'cancel'
    }
    
    console.log(`  -> 两种方式都未命中任何按钮`)
    return null
  }
  
  /**
   * 检查点是否在按钮内
   */
  isPointInButton(x, y, button) {
    return x >= button.x && x <= button.x + button.width &&
           y >= button.y && y <= button.y + button.height
  }
}

// CommonJS导出
module.exports = ShareDialog 