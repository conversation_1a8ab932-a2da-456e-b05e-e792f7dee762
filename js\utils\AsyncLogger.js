/**
 * 异步日志管理器
 * 提供高性能的异步日志输出，避免阻塞游戏主线程
 */
class AsyncLogger {
  constructor() {
    // 日志级别定义
    this.LOG_LEVELS = {
      DEBUG: 0,
      INFO: 1,
      WARN: 2,
      ERROR: 3,
      OFF: 4
    }

    // 当前日志级别（生产环境建议设为INFO或WARN）
    this.currentLevel = this.LOG_LEVELS.INFO

    // 日志队列
    this.logQueue = []
    this.maxQueueSize = 1000

    // 批量输出配置
    this.batchSize = 10
    this.flushInterval = 100 // 100ms批量输出一次

    // 性能监控
    this.stats = {
      totalLogs: 0,
      droppedLogs: 0,
      flushCount: 0,
      avgFlushTime: 0
    }

    // 初始化性能测量工具
    this.initPerformanceUtils()

    // 启动批量输出定时器
    this.startBatchFlush()

    // 游戏暂停时的处理
    this.isPaused = false
  }

  /**
   * 初始化性能测量工具
   * 提供跨平台的时间测量功能
   */
  initPerformanceUtils() {
    // 检查performance对象是否可用
    if (typeof performance !== 'undefined' && performance.now) {
      this.getTime = () => performance.now()
    } else {
      // 降级方案：使用Date.now()
      this.getTime = () => Date.now()
      console.warn('[AsyncLogger] performance对象不可用，使用Date.now()作为降级方案')
    }
  }
  
  /**
   * 设置日志级别
   * @param {string|number} level - 日志级别
   * @param {boolean} silent - 是否静默设置（不输出设置信息）
   */
  setLevel(level, silent = false) {
    if (typeof level === 'string') {
      level = this.LOG_LEVELS[level.toUpperCase()]
    }

    if (level !== undefined && level >= 0 && level <= 4) {
      this.currentLevel = level
      if (!silent) {
        // 使用console.log直接输出，避免递归调用
        console.log(`[${new Date().toLocaleTimeString()}] [INFO] 日志级别设置为: ${this.getLevelName(level)}`)
      }
    }
  }

  /**
   * 根据开发者模式设置日志级别
   * @param {boolean} isDeveloperMode - 是否为开发者模式
   */
  setLevelByDeveloperMode(isDeveloperMode) {
    if (isDeveloperMode) {
      // 开发者模式：显示所有日志
      this.setLevel('DEBUG', true)
      console.log(`[${new Date().toLocaleTimeString()}] [INFO] 🛠️ 开发者模式已启用，日志级别设为DEBUG`)
    } else {
      // 非开发者模式：只显示警告和错误
      this.setLevel('WARN', true)
      console.log(`[${new Date().toLocaleTimeString()}] [INFO] 🔒 开发者模式已禁用，日志级别设为WARN`)
    }
  }
  
  /**
   * 获取日志级别名称
   * @param {number} level - 日志级别数值
   * @returns {string}
   */
  getLevelName(level) {
    const names = ['DEBUG', 'INFO', 'WARN', 'ERROR', 'OFF']
    return names[level] || 'UNKNOWN'
  }
  
  /**
   * 检查是否应该输出指定级别的日志
   * @param {number} level - 日志级别
   * @returns {boolean}
   */
  shouldLog(level) {
    return level >= this.currentLevel && this.currentLevel < this.LOG_LEVELS.OFF
  }
  
  /**
   * 添加日志到队列
   * @param {number} level - 日志级别
   * @param {Array} args - 日志参数
   */
  addToQueue(level, args) {
    if (!this.shouldLog(level)) return
    
    const logEntry = {
      level: level,
      timestamp: Date.now(),
      args: args
    }
    
    // 检查队列大小，防止内存溢出
    if (this.logQueue.length >= this.maxQueueSize) {
      this.logQueue.shift() // 移除最旧的日志
      this.stats.droppedLogs++
    }
    
    this.logQueue.push(logEntry)
    this.stats.totalLogs++
    
    // 如果是错误级别，立即输出
    if (level >= this.LOG_LEVELS.ERROR) {
      this.flushImmediate()
    }
  }
  
  /**
   * DEBUG级别日志
   * @param {...any} args - 日志参数
   */
  debug(...args) {
    this.addToQueue(this.LOG_LEVELS.DEBUG, args)
  }
  
  /**
   * INFO级别日志
   * @param {...any} args - 日志参数
   */
  info(...args) {
    this.addToQueue(this.LOG_LEVELS.INFO, args)
  }
  
  /**
   * WARN级别日志
   * @param {...any} args - 日志参数
   */
  warn(...args) {
    this.addToQueue(this.LOG_LEVELS.WARN, args)
  }
  
  /**
   * ERROR级别日志
   * @param {...any} args - 日志参数
   */
  error(...args) {
    this.addToQueue(this.LOG_LEVELS.ERROR, args)
  }
  
  /**
   * 启动批量输出定时器
   */
  startBatchFlush() {
    this.flushTimer = setInterval(() => {
      if (!this.isPaused && this.logQueue.length > 0) {
        this.flushBatch()
      }
    }, this.flushInterval)
  }
  
  /**
   * 批量输出日志
   */
  flushBatch() {
    if (this.logQueue.length === 0) return

    const startTime = this.getTime()
    const batchSize = Math.min(this.batchSize, this.logQueue.length)
    const batch = this.logQueue.splice(0, batchSize)

    // 使用requestIdleCallback在空闲时输出，避免阻塞游戏
    if (typeof requestIdleCallback !== 'undefined') {
      requestIdleCallback(() => {
        this.outputBatch(batch)
      })
    } else {
      // 降级方案：使用setTimeout
      setTimeout(() => {
        this.outputBatch(batch)
      }, 0)
    }

    // 更新统计信息
    const flushTime = this.getTime() - startTime
    this.stats.flushCount++
    this.stats.avgFlushTime = (this.stats.avgFlushTime * (this.stats.flushCount - 1) + flushTime) / this.stats.flushCount
  }
  
  /**
   * 立即输出日志（用于错误等紧急情况）
   */
  flushImmediate() {
    if (this.logQueue.length === 0) return
    
    const batch = this.logQueue.splice(0)
    this.outputBatch(batch)
  }
  
  /**
   * 输出一批日志
   * @param {Array} batch - 日志批次
   */
  outputBatch(batch) {
    batch.forEach(entry => {
      const levelName = this.getLevelName(entry.level)
      const timestamp = new Date(entry.timestamp).toLocaleTimeString()
      
      // 根据日志级别选择输出方法
      switch (entry.level) {
        case this.LOG_LEVELS.DEBUG:
          console.log(`[${timestamp}] [DEBUG]`, ...entry.args)
          break
        case this.LOG_LEVELS.INFO:
          console.log(`[${timestamp}] [INFO]`, ...entry.args)
          break
        case this.LOG_LEVELS.WARN:
          console.warn(`[${timestamp}] [WARN]`, ...entry.args)
          break
        case this.LOG_LEVELS.ERROR:
          console.error(`[${timestamp}] [ERROR]`, ...entry.args)
          break
      }
    })
  }
  
  /**
   * 暂停日志输出（游戏暂停时调用）
   */
  pause() {
    this.isPaused = true
  }
  
  /**
   * 恢复日志输出（游戏恢复时调用）
   */
  resume() {
    this.isPaused = false
  }
  
  /**
   * 获取性能统计信息
   * @returns {Object}
   */
  getStats() {
    return {
      ...this.stats,
      queueSize: this.logQueue.length,
      currentLevel: this.getLevelName(this.currentLevel),
      isPaused: this.isPaused
    }
  }
  
  /**
   * 清空日志队列
   */
  clear() {
    this.logQueue = []
  }
  
  /**
   * 销毁日志管理器
   */
  destroy() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
      this.flushTimer = null
    }
    
    // 输出剩余日志
    this.flushImmediate()
    
    this.logQueue = []
  }
}

// 创建全局日志实例
const logger = new AsyncLogger()

/**
 * 初始化日志级别
 * 优先级：开发者模式 > DEBUG_MODE > 默认设置
 */
function initializeLogLevel() {
  try {
    // 尝试获取GameConfig（可能还未加载）
    let gameConfig = null
    try {
      gameConfig = require('./GameConfig')
    } catch (e) {
      // GameConfig还未加载，稍后会通过setLevelByDeveloperMode设置
    }

    if (gameConfig && typeof gameConfig.isDeveloperMode === 'function') {
      // 如果GameConfig可用，根据开发者模式设置
      logger.setLevelByDeveloperMode(gameConfig.isDeveloperMode())
    } else if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
      // 降级到DEBUG_MODE
      logger.setLevel('DEBUG', true)
    } else {
      // 默认设置：非开发环境只显示警告和错误
      logger.setLevel('WARN', true)
    }
  } catch (error) {
    // 如果出错，使用安全的默认设置
    logger.setLevel('WARN', true)
    console.warn('日志系统初始化时出错，使用默认WARN级别:', error.message)
  }
}

// 初始化日志级别
initializeLogLevel()

// CommonJS导出
module.exports = logger
