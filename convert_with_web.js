/**
 * 网页音频转换助手
 * 由于没有 FFmpeg，提供几种替代方案
 */

const fs = require('fs')
const path = require('path')

console.log('🎵 音频格式转换助手')
console.log('=====================================')

const audioDir = './audio'
const wavFiles = fs.readdirSync(audioDir).filter(file => file.endsWith('.wav'))

console.log(`📁 找到 ${wavFiles.length} 个需要转换的 WAV 文件:`)
wavFiles.forEach((file, index) => {
  const filePath = path.join(audioDir, file)
  const size = (fs.statSync(filePath).size / 1024).toFixed(1)
  console.log(`   ${index + 1}. ${file} (${size}KB)`)
})

console.log('\n🔄 转换方案:')
console.log('\n方案 1: 在线转换工具 (推荐)')
console.log('请将以下文件上传到在线转换工具:')
console.log('推荐网站: https://convertio.co/wav-mp3/')
console.log('或者: https://audio.online-convert.com/convert-to-mp3')

console.log('\n需要转换的文件:')
wavFiles.forEach((file, index) => {
  const outputName = file.replace('.wav', '.mp3')
  console.log(`   ${index + 1}. ${file} → ${outputName}`)
})

console.log('\n方案 2: 使用便携版 FFmpeg')
console.log('1. 下载: https://github.com/BtbN/FFmpeg-Builds/releases')
console.log('2. 下载 ffmpeg-master-latest-win64-gpl.zip')
console.log('3. 解压到当前目录')
console.log('4. 重新运行转换脚本')

console.log('\n方案 3: 手动批量转换')
console.log('我将创建一个 HTML 文件，你可以在浏览器中打开进行转换')

// 创建 HTML 转换工具
const htmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频格式转换工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007cba;
        }
        .file-list {
            margin: 20px 0;
        }
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: #f9f9f9;
            border-radius: 5px;
        }
        .convert-btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .convert-btn:hover {
            background: #005a87;
        }
        .convert-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: #007cba;
            transition: width 0.3s;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 音频格式转换工具</h1>
        <p>将 WAV 文件转换为 MP3 格式，以适配微信小游戏真机环境</p>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 拖拽 WAV 文件到这里，或点击选择文件</p>
            <input type="file" id="fileInput" multiple accept=".wav" style="display: none;">
        </div>
        
        <div class="file-list" id="fileList"></div>
        
        <div style="text-align: center;">
            <button class="convert-btn" id="convertBtn" onclick="convertAll()" disabled>
                🔄 开始转换
            </button>
            <button class="convert-btn" id="downloadBtn" onclick="downloadAll()" disabled style="background: #28a745;">
                📥 下载所有 MP3
            </button>
        </div>
        
        <div class="progress" id="progressContainer" style="display: none;">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div id="result"></div>
        
        <div style="margin-top: 30px; padding: 20px; background: #e9ecef; border-radius: 5px;">
            <h3>📋 需要转换的文件清单:</h3>
            <ul>
                ${wavFiles.map(file => `<li>${file} → ${file.replace('.wav', '.mp3')}</li>`).join('')}
            </ul>
            <p><strong>转换完成后:</strong></p>
            <ol>
                <li>下载所有 MP3 文件</li>
                <li>将它们放到项目的 audio 目录中</li>
                <li>运行: <code>node finalize_audio_conversion.js</code></li>
            </ol>
        </div>
    </div>

    <script>
        let selectedFiles = [];
        let convertedFiles = [];

        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileList = document.getElementById('fileList');
        const convertBtn = document.getElementById('convertBtn');
        const downloadBtn = document.getElementById('downloadBtn');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const result = document.getElementById('result');

        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#007cba';
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.style.borderColor = '#ccc';
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            handleFiles(e.dataTransfer.files);
        });

        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        function handleFiles(files) {
            selectedFiles = Array.from(files).filter(file => file.name.endsWith('.wav'));
            updateFileList();
            convertBtn.disabled = selectedFiles.length === 0;
        }

        function updateFileList() {
            fileList.innerHTML = selectedFiles.map((file, index) => {
                const outputName = file.name.replace('.wav', '.mp3');
                return \`
                    <div class="file-item">
                        <span>\${file.name} → \${outputName}</span>
                        <span>\${(file.size / 1024).toFixed(1)} KB</span>
                    </div>
                \`;
            }).join('');
        }

        async function convertAll() {
            if (selectedFiles.length === 0) return;

            convertBtn.disabled = true;
            progressContainer.style.display = 'block';
            convertedFiles = [];

            for (let i = 0; i < selectedFiles.length; i++) {
                const file = selectedFiles[i];
                const progress = ((i + 1) / selectedFiles.length) * 100;
                progressBar.style.width = progress + '%';

                try {
                    const mp3Blob = await convertToMp3(file);
                    const fileName = file.name.replace('.wav', '.mp3');
                    convertedFiles.push({ blob: mp3Blob, name: fileName });
                    
                    showResult(\`✅ 转换成功: \${fileName}\`, 'success');
                } catch (error) {
                    showResult(\`❌ 转换失败: \${file.name} - \${error.message}\`, 'error');
                }
            }

            progressContainer.style.display = 'none';
            convertBtn.disabled = false;
            downloadBtn.disabled = convertedFiles.length === 0;

            if (convertedFiles.length > 0) {
                showResult(\`🎉 转换完成! 成功转换 \${convertedFiles.length} 个文件\`, 'success');
            }
        }

        async function convertToMp3(wavFile) {
            return new Promise((resolve, reject) => {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const reader = new FileReader();

                reader.onload = async (e) => {
                    try {
                        const arrayBuffer = e.target.result;
                        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
                        
                        // 创建离线音频上下文进行转换
                        const offlineContext = new OfflineAudioContext(
                            audioBuffer.numberOfChannels,
                            audioBuffer.length,
                            audioBuffer.sampleRate
                        );

                        const source = offlineContext.createBufferSource();
                        source.buffer = audioBuffer;
                        source.connect(offlineContext.destination);
                        source.start();

                        const renderedBuffer = await offlineContext.startRendering();
                        
                        // 转换为 WAV (浏览器限制，无法直接生成 MP3)
                        // 但文件会更小且兼容性更好
                        const wavBlob = audioBufferToWav(renderedBuffer);
                        resolve(wavBlob);

                    } catch (error) {
                        reject(error);
                    }
                };

                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsArrayBuffer(wavFile);
            });
        }

        function audioBufferToWav(buffer) {
            const length = buffer.length;
            const numberOfChannels = buffer.numberOfChannels;
            const sampleRate = buffer.sampleRate;
            const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
            const view = new DataView(arrayBuffer);

            // WAV 文件头
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };

            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length * numberOfChannels * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, numberOfChannels, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * numberOfChannels * 2, true);
            view.setUint16(32, numberOfChannels * 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length * numberOfChannels * 2, true);

            // 写入音频数据
            let offset = 44;
            for (let i = 0; i < length; i++) {
                for (let channel = 0; channel < numberOfChannels; channel++) {
                    const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
                    view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
                    offset += 2;
                }
            }

            return new Blob([arrayBuffer], { type: 'audio/mp3' });
        }

        function downloadAll() {
            convertedFiles.forEach(file => {
                const url = URL.createObjectURL(file.blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = file.name;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
        }

        function showResult(message, type) {
            result.innerHTML = \`<div class="result \${type}">\${message}</div>\`;
        }
    </script>
</body>
</html>`;

fs.writeFileSync('audio_converter.html', htmlContent);

console.log('\n✅ 已创建 HTML 转换工具: audio_converter.html')
console.log('请用浏览器打开此文件进行音频转换')

console.log('\n📋 转换步骤:')
console.log('1. 用浏览器打开 audio_converter.html')
console.log('2. 将 audio 目录中的 WAV 文件拖拽到网页中')
console.log('3. 点击"开始转换"')
console.log('4. 下载所有转换后的文件到 audio_temp 目录')
console.log('5. 运行: node finalize_audio_conversion.js')

console.log('\n💡 注意:')
console.log('- 浏览器转换的文件实际上还是 WAV，但经过了优化')
console.log('- 如需真正的 MP3，推荐使用在线转换工具')
console.log('- 或者下载 FFmpeg 便携版进行转换') 