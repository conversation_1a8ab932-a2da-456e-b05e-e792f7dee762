const logger = require('../utils/AsyncLogger')

/**
 * 槽位类
 * 管理底部的方块容器槽位
 */
class Slot {
  /**
   * 构造函数
   * @param {number} x - 槽位X坐标
   * @param {number} y - 槽位Y坐标
   * @param {number} width - 槽位宽度
   * @param {number} height - 槽位高度
   */
  constructor(x, y, width, height) {
    this.x = x                    // 槽位X坐标
    this.y = y                    // 槽位Y坐标
    this.width = width            // 槽位宽度
    this.height = height          // 槽位高度
    this.blocks = []              // 存储的方块
    this.maxBlocks = 7            // 最大方块数
    this.blockSpacing = 3         // 方块间距，减小间距
    this.isHighlighted = false    // 是否高亮显示
    this.animationManager = null  // 动画管理器
    this.audioManager = null      // 音频管理器
    this.eliminationInProgress = false // 消除动画是否在进行中
    this.onEliminationComplete = null  // 消除完成回调
    
    // 动态计算方块大小，让7个方块刚好填满槽位宽度
    this.calculateBlockSize()
  }

  /**
   * 动态计算方块大小
   * 让7个方块加间距刚好填满槽位宽度
   */
  calculateBlockSize() {
    const leftMargin = 10   // 左边距
    const rightMargin = 10  // 右边距
    const availableWidth = this.width - leftMargin - rightMargin
    
    // 7个方块 + 6个间距 = 可用宽度
    // blockSize * 7 + blockSpacing * 6 = availableWidth
    this.blockSize = Math.floor((availableWidth - this.blockSpacing * 6) / 7)
    
    // 确保方块大小不超过槽位高度的85%
    const maxBlockSize = Math.floor(this.height * 0.85)
    this.blockSize = Math.min(this.blockSize, maxBlockSize)
    
    logger.info(`槽位尺寸计算:`)
    logger.info(`  槽位宽度: ${this.width}, 高度: ${this.height}`)
    logger.info(`  可用宽度: ${availableWidth} (去除左右边距${leftMargin + rightMargin})`)
    logger.info(`  方块大小: ${this.blockSize} (间距: ${this.blockSpacing})`)
    logger.info(`  7个方块总宽: ${this.blockSize * 7 + this.blockSpacing * 6}`)
  }

  /**
   * 添加方块到槽位
   * @param {Block} block - 要添加的方块
   * @returns {boolean} 是否添加成功
   */
  addBlock(block) {
    logger.info(`🎲 添加方块到槽位，类型: ${block.type}, 当前槽位方块数: ${this.blocks.length}/${this.maxBlocks}`)
    
    // 计算方块在槽位中的位置
    // 注意：this.blocks.length 用于计算新方块的位置，此时它代表的是添加前槽中最后一个方块的索引+1
    const position = this.calculateBlockPosition(this.blocks.length)
    
    // 设置方块大小为槽位计算的大小
    block.width = position.size
    block.height = position.size
    
    block.moveTo(position.x, position.y)
    
    // 将方块标记为可点击，移除灰色视觉效果
    block.isClickable = true
    
    this.blocks.push(block) // 方块被加入数组
    
    logger.info(`📦 方块已放入槽位，新的方块数: ${this.blocks.length}/${this.maxBlocks}`)
    
    // 检查是否可以消除
    this.checkForElimination() // 尝试消除
    
    return true // 表示方块已处理（添加和尝试消除）
  }

  /**
   * 计算方块在槽位中的位置
   * @param {number} index - 方块索引
   * @returns {Object} {x, y}
   */
  calculateBlockPosition(index) {
    // 使用动态计算的方块大小
    const blockSize = this.blockSize
    
    // 从槽位左边开始排列，留边距
    const startX = this.x + 10 // 槽位左边留10像素边距
    
    // 计算当前方块的x位置
    const blockX = startX + index * (blockSize + this.blockSpacing)
    
    // 垂直居中
    const blockY = this.y + (this.height - blockSize) / 2
    
    logger.info(`槽位方块${index}位置计算详情:`)
    logger.info(`  - 槽位区域: x=${this.x}, y=${this.y}, width=${this.width}, height=${this.height}`)
    logger.info(`  - 方块大小: ${blockSize}, 间距: ${this.blockSpacing}`)
    logger.info(`  - 起始X: ${startX} (槽位x=${this.x} + 边距10)`)
    logger.info(`  - 方块X: ${blockX} (起始X=${startX} + 索引${index} * (方块大小${blockSize} + 间距${this.blockSpacing}))`)
    logger.info(`  - 方块Y: ${blockY}`)
    logger.info(`  - 最终位置: (${blockX}, ${blockY})`)
    
    return {
      x: blockX,
      y: blockY,
      size: blockSize  // 返回方块大小，供Block类使用
    }
  }

  /**
   * 重新排列槽位中的方块
   */
  rearrangeBlocks() {
    logger.info(`重新排列槽位中的${this.blocks.length}个方块:`)
    this.blocks.forEach((block, index) => {
      const position = this.calculateBlockPosition(index)
      logger.info(`  方块${index} (类型${block.type}): 移动到 (${position.x}, ${position.y})`)
      block.moveTo(position.x, position.y)
    })
    logger.info('方块重新排列完成')
  }

  /**
   * 检查并执行三消逻辑
   */
  checkForElimination() {
    // 如果正在消除动画中，不进行新的消除检查
    if (this.eliminationInProgress) {
      logger.info('⏸️ 消除动画进行中，跳过新的消除检查')
      return
    }
    
    logger.info('🔍 检查是否可以消除...')
    
    // 统计每种类型的方块数量
    const typeCount = new Map()
    
    this.blocks.forEach(block => {
      const count = typeCount.get(block.type) || 0
      typeCount.set(block.type, count + 1)
    })

    logger.info('📊 当前方块类型统计:', Array.from(typeCount.entries()))

    // 寻找达到3个的类型
    for (let [type, count] of typeCount) {
      if (count >= 3) {
        logger.info(`✨ 发现可消除类型: ${type}, 数量: ${count}`)
        // 播放匹配音效
        if (this.audioManager) {
          this.audioManager.playSound('match')
        }
        this.eliminateBlocksOfType(type)
        break // 一次只消除一种类型
      }
    }
    
    logger.info('🔚 消除检查完成')
  }

  /**
   * 消除指定类型的方块
   * @param {number} type - 方块类型
   */
  eliminateBlocksOfType(type) {
    logger.info(`💥 开始消除类型 ${type} 的方块`)
    
    // 标记消除动画开始
    this.eliminationInProgress = true
    logger.info('🎬 消除动画开始，设置eliminationInProgress = true')
    
    // 找到该类型的方块
    const blocksToEliminate = this.blocks.filter(block => block.type === type).slice(0, 3)
    logger.info(`🎯 找到 ${blocksToEliminate.length} 个待消除方块`)
    
    // 播放消除动画
    let eliminatedCount = 0
    blocksToEliminate.forEach(block => {
      block.playEliminateAnimation(() => {
        eliminatedCount++
        logger.info(`💫 消除动画完成 ${eliminatedCount}/${blocksToEliminate.length}`)
        
        if (eliminatedCount === blocksToEliminate.length) {
          // 所有动画完成后，从槽位中移除方块
          this.blocks = this.blocks.filter(b => !blocksToEliminate.includes(b))
          logger.info(`🗑️ 从槽位移除方块，剩余方块数: ${this.blocks.length}`)
          this.rearrangeBlocks()
          
          // 标记消除动画结束
          this.eliminationInProgress = false
          logger.info('🎬 消除动画结束，设置eliminationInProgress = false')
          
          // 触发消除完成回调
          if (this.onEliminationComplete) {
            logger.info('📞 调用消除完成回调')
            this.onEliminationComplete()
          }
        }
      }, this.animationManager)
    })

    // 触发消除事件
    this.onElimination && this.onElimination(type, 3)
  }

  /**
   * 检查槽位是否已满
   * @returns {boolean}
   */
  isFull() {
    return this.blocks.length >= this.maxBlocks
  }

  /**
   * 检查槽位是否为空
   * @returns {boolean}
   */
  isEmpty() {
    return this.blocks.length === 0
  }

  /**
   * 获取槽位中方块的数量
   * @returns {number}
   */
  getBlockCount() {
    return this.blocks.length
  }

  /**
   * 移除指定方块
   * @param {Block} block - 要移除的方块
   * @returns {boolean} 是否移除成功
   */
  removeBlock(block) {
    const index = this.blocks.indexOf(block)
    if (index > -1) {
      this.blocks.splice(index, 1)
      this.rearrangeBlocks()
      return true
    }
    return false
  }

  /**
   * 清空槽位
   */
  clear() {
    this.blocks.forEach(block => {
      block.isVisible = false
    })
    this.blocks = []
  }

  /**
   * 更新槽位状态
   * @param {number} deltaTime - 时间差
   */
  update(deltaTime) {
    // 更新槽位中的所有方块
    this.blocks.forEach(block => {
      block.update(deltaTime)
    })

    // 移除已经不可见的方块
    this.blocks = this.blocks.filter(block => block.isVisible)
  }

  /**
   * 渲染槽位
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {ResourceLoader} resources - 资源管理器
   */
  render(ctx, resources) {
    ctx.save()

    // 绘制槽位背景
    this.renderBackground(ctx)
    
    // 绘制槽位中的方块
    this.blocks.forEach(block => {
      block.render(ctx, resources)
    })

    // 绘制槽位边框和装饰
    this.renderBorder(ctx)

    ctx.restore()
  }

  /**
   * 绘制槽位背景
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderBackground(ctx) {
    // 主背景 - 深凹陷效果
    this.renderInsetBackground(ctx)
    
    // 个别槽位指示
    this.renderSlotIndicators(ctx)
    
    // 木纹纹理效果
    this.renderWoodTexture(ctx)
  }

  /**
   * 绘制凹陷背景效果 - 美化版本
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderInsetBackground(ctx) {
    const padding = 6  // 增加内边距
    const innerX = this.x + padding
    const innerY = this.y + padding
    const innerWidth = this.width - padding * 2
    const innerHeight = this.height - padding * 2

    // 外部阴影效果
    ctx.save()
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)'
    ctx.shadowBlur = 8
    ctx.shadowOffsetX = 2
    ctx.shadowOffsetY = 2

    // 外部边框 - 立体效果，使用圆角
    const borderRadius = 12

    // 绘制圆角外框
    this.drawRoundedRect(ctx, this.x, this.y, this.width, this.height, borderRadius)

    // 外框渐变
    const outerGradient = ctx.createLinearGradient(this.x, this.y, this.x, this.y + this.height)
    outerGradient.addColorStop(0, this.isHighlighted ? '#F5DEB3' : '#E6D3B7')
    outerGradient.addColorStop(0.5, this.isHighlighted ? '#D2B48C' : '#C8A882')
    outerGradient.addColorStop(1, this.isHighlighted ? '#CD853F' : '#8B7355')

    ctx.fillStyle = outerGradient
    ctx.fill()
    ctx.restore()

    // 内部凹陷区域 - 圆角
    const innerRadius = 8
    this.drawRoundedRect(ctx, innerX, innerY, innerWidth, innerHeight, innerRadius)

    // 主背景 - 径向渐变，营造凹陷效果
    const centerX = innerX + innerWidth / 2
    const centerY = innerY + innerHeight / 2
    const radius = Math.max(innerWidth, innerHeight) / 2

    const radialGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius)
    radialGradient.addColorStop(0, this.isHighlighted ? '#8B7355' : '#5D4037')
    radialGradient.addColorStop(0.6, this.isHighlighted ? '#654321' : '#4A3728')
    radialGradient.addColorStop(1, this.isHighlighted ? '#5D4037' : '#3C2414')

    ctx.fillStyle = radialGradient
    ctx.fill()

    // 内部边框 - 凹陷效果
    ctx.strokeStyle = this.isHighlighted ? '#4A3728' : '#2F1B14'
    ctx.lineWidth = 2
    ctx.stroke()

    // 内部高光效果
    ctx.save()
    ctx.globalAlpha = 0.3
    const highlightGradient = ctx.createLinearGradient(innerX, innerY, innerX, innerY + innerHeight / 3)
    highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.2)')
    highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)')

    this.drawRoundedRect(ctx, innerX, innerY, innerWidth, innerHeight / 3, innerRadius)
    ctx.fillStyle = highlightGradient
    ctx.fill()
    ctx.restore()
  }

  /**
   * 绘制圆角矩形
   */
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
  }

  /**
   * 绘制槽位指示器 - 美化版本
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderSlotIndicators(ctx) {
    const padding = 12  // 增加内边距
    const slotWidth = (this.width - padding * 2) / this.maxBlocks
    const slotHeight = this.height - padding * 2
    const startX = this.x + padding

    for (let i = 0; i < this.maxBlocks; i++) {
      const slotX = startX + i * slotWidth
      const slotY = this.y + padding / 2

      // 如果该位置没有方块，绘制空槽指示
      if (i >= this.blocks.length) {
        // 绘制美化的凹槽效果
        const insetX = slotX + 3
        const insetY = slotY + 3
        const insetWidth = slotWidth - 6
        const insetHeight = slotHeight - 6
        const slotRadius = 6

        // 凹槽阴影
        ctx.save()
        ctx.shadowColor = 'rgba(0, 0, 0, 0.4)'
        ctx.shadowBlur = 4
        ctx.shadowOffsetX = 1
        ctx.shadowOffsetY = 1

        this.drawRoundedRect(ctx, insetX, insetY, insetWidth, insetHeight, slotRadius)
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)'
        ctx.fill()
        ctx.restore()

        // 凹槽渐变背景
        const slotGradient = ctx.createLinearGradient(insetX, insetY, insetX, insetY + insetHeight)
        slotGradient.addColorStop(0, this.isHighlighted ? '#4A3728' : '#3C2414')
        slotGradient.addColorStop(0.5, this.isHighlighted ? '#5D4037' : '#4A3728')
        slotGradient.addColorStop(1, this.isHighlighted ? '#654321' : '#5D4037')

        this.drawRoundedRect(ctx, insetX, insetY, insetWidth, insetHeight, slotRadius)
        ctx.fillStyle = slotGradient
        ctx.fill()

        // 凹槽边框
        ctx.strokeStyle = this.isHighlighted ? '#8B7355' : '#654321'
        ctx.lineWidth = 1.5
        ctx.stroke()

        // 美化的空槽指示 - 使用圆形而不是方形
        const dotRadius = 3
        const dotX = insetX + insetWidth / 2
        const dotY = insetY + insetHeight / 2

        // 指示点阴影
        ctx.save()
        ctx.shadowColor = 'rgba(0, 0, 0, 0.5)'
        ctx.shadowBlur = 2
        ctx.shadowOffsetX = 1
        ctx.shadowOffsetY = 1

        ctx.beginPath()
        ctx.arc(dotX, dotY, dotRadius, 0, Math.PI * 2)
        ctx.fillStyle = this.isHighlighted ? '#A0522D' : '#8B7355'
        ctx.fill()

        // 指示点高光
        ctx.beginPath()
        ctx.arc(dotX - 1, dotY - 1, dotRadius / 2, 0, Math.PI * 2)
        ctx.fillStyle = 'rgba(255, 255, 255, 0.3)'
        ctx.fill()

        ctx.restore()
      }
    }
  }

  /**
   * 绘制木纹纹理效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderWoodTexture(ctx) {
    ctx.save()
    ctx.globalAlpha = 0.3

    // 横向木纹
    ctx.strokeStyle = this.isHighlighted ? '#D2B48C' : '#A0522D'
    ctx.lineWidth = 0.5
    
    for (let i = 1; i < 8; i++) {
      const y = this.y + (this.height / 8) * i + Math.sin(i * 0.5) * 2
      ctx.beginPath()
      ctx.moveTo(this.x + 5, y)
      
      // 创建波浪状木纹
      for (let x = this.x + 5; x < this.x + this.width - 5; x += 5) {
        const waveY = y + Math.sin((x - this.x) * 0.02) * 1.5
        ctx.lineTo(x, waveY)
      }
      ctx.stroke()
    }

    // 纵向纹理细节
    ctx.strokeStyle = this.isHighlighted ? '#CD853F' : '#8B4513'
    ctx.lineWidth = 0.3
    
    for (let i = 0; i < 5; i++) {
      const x = this.x + 10 + Math.random() * (this.width - 20)
      const startY = this.y + 5 + Math.random() * 10
      const endY = this.y + this.height - 5 - Math.random() * 10
      
      ctx.beginPath()
      ctx.moveTo(x, startY)
      ctx.lineTo(x + Math.random() * 3 - 1.5, endY)
      ctx.stroke()
    }

    ctx.restore()
  }

  /**
   * 绘制槽位边框
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderBorder(ctx) {
    // 绘制立体边框效果
    this.render3DBorder(ctx)

    // 如果槽位快满了，显示警告效果
    if (this.blocks.length >= this.maxBlocks - 1) {
      this.renderWarningEffect(ctx)
    }

    // 显示容量指示器
    this.renderCapacityIndicator(ctx)
  }

  /**
   * 绘制3D立体边框
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render3DBorder(ctx) {
    const borderWidth = 4

    // 外层边框 - 凸起效果
    // 顶部和左侧亮边
    ctx.fillStyle = this.isHighlighted ? '#F5DEB3' : '#DEB887'
    // 顶部
    ctx.fillRect(this.x - borderWidth, this.y - borderWidth, this.width + borderWidth * 2, borderWidth)
    // 左侧
    ctx.fillRect(this.x - borderWidth, this.y - borderWidth, borderWidth, this.height + borderWidth * 2)

    // 底部和右侧暗边
    ctx.fillStyle = this.isHighlighted ? '#8B4513' : '#3C2414'
    // 底部
    ctx.fillRect(this.x, this.y + this.height, this.width + borderWidth, borderWidth)
    // 右侧
    ctx.fillRect(this.x + this.width, this.y, borderWidth, this.height + borderWidth)

    // 中间层边框
    const midBorderWidth = 2
    ctx.fillStyle = this.isHighlighted ? '#D2B48C' : '#A0522D'
    // 顶部
    ctx.fillRect(this.x - midBorderWidth, this.y - midBorderWidth, this.width + midBorderWidth * 2, midBorderWidth)
    // 左侧
    ctx.fillRect(this.x - midBorderWidth, this.y, midBorderWidth, this.height)
    
    // 底部和右侧中间阴影
    ctx.fillStyle = this.isHighlighted ? '#8B7355' : '#2F1B14'
    // 底部
    ctx.fillRect(this.x, this.y + this.height, this.width + midBorderWidth, midBorderWidth)
    // 右侧
    ctx.fillRect(this.x + this.width, this.y, midBorderWidth, this.height)

    // 内层精细线条
    ctx.strokeStyle = this.isHighlighted ? '#CD853F' : '#654321'
    ctx.lineWidth = 1
    ctx.strokeRect(this.x, this.y, this.width, this.height)
  }

  /**
   * 绘制警告效果
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderWarningEffect(ctx) {
    const time = Date.now() * 0.01
    const pulseIntensity = (Math.sin(time) + 1) / 2 // 0-1之间脉动

    // 外层警告光圈
    ctx.save()
    ctx.shadowColor = '#FF4444'
    ctx.shadowBlur = 10 + pulseIntensity * 10
    ctx.strokeStyle = `rgba(255, 68, 68, ${0.6 + pulseIntensity * 0.4})`
    ctx.lineWidth = 3 + pulseIntensity * 2
    ctx.setLineDash([8, 4])
    ctx.strokeRect(this.x - 8, this.y - 8, this.width + 16, this.height + 16)
    ctx.setLineDash([])
    ctx.restore()

    // 内层警告边框
    ctx.strokeStyle = `rgba(255, 0, 0, ${0.8 + pulseIntensity * 0.2})`
    ctx.lineWidth = 2
    ctx.strokeRect(this.x - 3, this.y - 3, this.width + 6, this.height + 6)

    // 角落警告标记
    const cornerSize = 12
    ctx.fillStyle = `rgba(255, 68, 68, ${0.7 + pulseIntensity * 0.3})`
    
    // 左上角
    ctx.fillRect(this.x - 6, this.y - 6, cornerSize, 3)
    ctx.fillRect(this.x - 6, this.y - 6, 3, cornerSize)
    
    // 右上角
    ctx.fillRect(this.x + this.width - 6, this.y - 6, cornerSize, 3)
    ctx.fillRect(this.x + this.width + 3, this.y - 6, 3, cornerSize)
    
    // 左下角
    ctx.fillRect(this.x - 6, this.y + this.height + 3, cornerSize, 3)
    ctx.fillRect(this.x - 6, this.y + this.height - 6, 3, cornerSize)
    
    // 右下角
    ctx.fillRect(this.x + this.width - 6, this.y + this.height + 3, cornerSize, 3)
    ctx.fillRect(this.x + this.width + 3, this.y + this.height - 6, 3, cornerSize)
  }

  /**
   * 绘制容量指示器 - 美化版本
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderCapacityIndicator(ctx) {
    const indicatorWidth = this.width - 20  // 减少边距，让指示器更宽
    const indicatorHeight = 16  // 增加高度
    const indicatorX = this.x + 10
    const indicatorY = this.y + this.height + 8

    // 绘制美化的容量条
    this.renderEnhancedCapacityBar(ctx, indicatorX, indicatorY, indicatorWidth, indicatorHeight)

    // 美化的数字显示
    this.renderEnhancedCapacityText(ctx, indicatorY)
  }

  /**
   * 绘制增强版容量条
   */
  renderEnhancedCapacityBar(ctx, x, y, width, height) {
    const fillRatio = this.blocks.length / this.maxBlocks
    const fillWidth = fillRatio * width
    const radius = height / 2

    // 容量条背景 - 圆角矩形
    ctx.save()
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)'
    ctx.shadowBlur = 4
    ctx.shadowOffsetX = 1
    ctx.shadowOffsetY = 1

    // 背景渐变
    const bgGradient = ctx.createLinearGradient(x, y, x, y + height)
    bgGradient.addColorStop(0, '#2F1B14')
    bgGradient.addColorStop(0.5, '#3C2414')
    bgGradient.addColorStop(1, '#4A3728')

    this.drawRoundedRect(ctx, x, y, width, height, radius)
    ctx.fillStyle = bgGradient
    ctx.fill()

    // 背景边框
    ctx.strokeStyle = '#1A0F0A'
    ctx.lineWidth = 1
    ctx.stroke()
    ctx.restore()

    // 填充部分
    if (fillWidth > 0) {
      // 根据填充比例选择颜色
      let baseColor, lightColor, darkColor
      if (this.blocks.length >= this.maxBlocks) {
        // 满了 - 红色
        baseColor = '#FF4444'
        lightColor = '#FF6666'
        darkColor = '#CC2222'
      } else if (this.blocks.length >= this.maxBlocks - 1) {
        // 快满了 - 橙色
        baseColor = '#FF8800'
        lightColor = '#FFAA33'
        darkColor = '#CC6600'
      } else {
        // 正常 - 绿色
        baseColor = '#4CAF50'
        lightColor = '#66BB6A'
        darkColor = '#388E3C'
      }

      // 填充渐变
      const fillGradient = ctx.createLinearGradient(x, y, x, y + height)
      fillGradient.addColorStop(0, lightColor)
      fillGradient.addColorStop(0.5, baseColor)
      fillGradient.addColorStop(1, darkColor)

      this.drawRoundedRect(ctx, x + 1, y + 1, fillWidth - 2, height - 2, radius - 1)
      ctx.fillStyle = fillGradient
      ctx.fill()

      // 填充部分的高光
      ctx.save()
      ctx.globalAlpha = 0.4
      const highlightGradient = ctx.createLinearGradient(x, y, x, y + height / 2)
      highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.6)')
      highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)')

      this.drawRoundedRect(ctx, x + 1, y + 1, fillWidth - 2, height / 2 - 1, radius - 1)
      ctx.fillStyle = highlightGradient
      ctx.fill()
      ctx.restore()

      // 如果满了，添加脉动效果
      if (this.blocks.length >= this.maxBlocks) {
        const time = Date.now() * 0.01
        const pulse = (Math.sin(time) + 1) / 2
        ctx.save()
        ctx.globalAlpha = 0.2 + pulse * 0.3
        ctx.fillStyle = '#FFFFFF'
        this.drawRoundedRect(ctx, x + 1, y + 1, fillWidth - 2, height - 2, radius - 1)
        ctx.fill()
        ctx.restore()
      }
    }
  }

  /**
   * 绘制增强版容量文字
   */
  renderEnhancedCapacityText(ctx, indicatorY) {
    const textY = indicatorY - 8

    // 文字阴影
    ctx.save()
    ctx.shadowColor = 'rgba(0, 0, 0, 0.8)'
    ctx.shadowBlur = 3
    ctx.shadowOffsetX = 1
    ctx.shadowOffsetY = 1

    // 主文字
    let textColor = '#FFFFFF'
    if (this.blocks.length >= this.maxBlocks) {
      textColor = '#FF6666'
    } else if (this.blocks.length >= this.maxBlocks - 1) {
      textColor = '#FFAA33'
    } else {
      textColor = '#66BB6A'
    }

    ctx.fillStyle = textColor
    ctx.font = 'bold 14px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'bottom'
    ctx.fillText(
      `${this.blocks.length}/${this.maxBlocks}`,
      this.x + this.width / 2,
      textY
    )
    ctx.restore()
  }

  /**
   * 绘制3D容量条
   */
  render3DCapacityBar(ctx, x, y, width, height) {
    const fillRatio = this.blocks.length / this.maxBlocks
    const fillWidth = fillRatio * width

    // 容量条外框 - 凹陷效果
    // 顶部和左侧阴影
    ctx.fillStyle = '#2F1B14'
    ctx.fillRect(x - 2, y - 2, width + 4, 2) // 顶部阴影
    ctx.fillRect(x - 2, y - 2, 2, height + 4) // 左侧阴影

    // 底部和右侧高光
    ctx.fillStyle = '#8B7355'
    ctx.fillRect(x, y + height, width + 2, 2) // 底部高光
    ctx.fillRect(x + width, y, 2, height + 2) // 右侧高光

    // 容量条背景（凹陷内部）
    const bgGradient = ctx.createLinearGradient(x, y, x, y + height)
    bgGradient.addColorStop(0, '#3C2414')
    bgGradient.addColorStop(1, '#4A3728')
    ctx.fillStyle = bgGradient
    ctx.fillRect(x, y, width, height)

    // 填充部分（凸起效果）
    if (fillWidth > 0) {
      // 确定填充颜色
      let baseColor, lightColor, darkColor
      if (this.blocks.length >= this.maxBlocks) {
        baseColor = '#FF4444'
        lightColor = '#FF6666'
        darkColor = '#CC2222'
      } else if (this.blocks.length >= this.maxBlocks - 1) {
        baseColor = '#FFA500'
        lightColor = '#FFC033'
        darkColor = '#CC8400'
      } else {
        baseColor = '#44AA44'
        lightColor = '#66CC66'
        darkColor = '#228822'
      }

      // 填充渐变
      const fillGradient = ctx.createLinearGradient(x, y, x, y + height)
      fillGradient.addColorStop(0, lightColor)
      fillGradient.addColorStop(0.5, baseColor)
      fillGradient.addColorStop(1, darkColor)
      
      ctx.fillStyle = fillGradient
      ctx.fillRect(x, y, fillWidth, height)

      // 填充部分的立体边框
      // 顶部高光
      ctx.fillStyle = 'rgba(255, 255, 255, 0.4)'
      ctx.fillRect(x, y, fillWidth, 1)
      
      // 底部阴影
      ctx.fillStyle = 'rgba(0, 0, 0, 0.4)'
      ctx.fillRect(x, y + height - 1, fillWidth, 1)

      // 如果满了，添加脉动效果
      if (this.blocks.length >= this.maxBlocks) {
        const time = Date.now() * 0.01
        const pulse = (Math.sin(time) + 1) / 2
        ctx.fillStyle = `rgba(255, 255, 255, ${0.2 + pulse * 0.3})`
        ctx.fillRect(x, y, fillWidth, height)
      }
    }

    // 容量条刻度线
    this.renderCapacityTicks(ctx, x, y, width, height)
  }

  /**
   * 绘制容量条刻度
   */
  renderCapacityTicks(ctx, x, y, width, height) {
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
    ctx.lineWidth = 1

    for (let i = 1; i < this.maxBlocks; i++) {
      const tickX = x + (i / this.maxBlocks) * width
      ctx.beginPath()
      ctx.moveTo(tickX, y + 2)
      ctx.lineTo(tickX, y + height - 2)
      ctx.stroke()
    }
  }

  /**
   * 绘制容量文字
   */
  renderCapacityText(ctx, indicatorY) {
    const textY = indicatorY - 6

    // 文字阴影
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)'
    ctx.font = 'bold 14px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'bottom'
    ctx.fillText(
      `${this.blocks.length}/${this.maxBlocks}`,
      this.x + this.width / 2 + 1,
      textY + 1
    )

    // 主文字
    let textColor = '#FFFFFF'
    if (this.blocks.length >= this.maxBlocks) {
      textColor = '#FF6666'
    } else if (this.blocks.length >= this.maxBlocks - 1) {
      textColor = '#FFAA33'
    }

    ctx.fillStyle = textColor
    ctx.fillText(
      `${this.blocks.length}/${this.maxBlocks}`,
      this.x + this.width / 2,
      textY
    )

    // 状态文字
    let statusText = '安全'
    if (this.blocks.length >= this.maxBlocks) {
      statusText = '已满!'
    } else if (this.blocks.length >= this.maxBlocks - 1) {
      statusText = '警告'
    }

    ctx.font = '10px Arial'
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
    ctx.fillText(statusText, this.x + this.width / 2, textY - 16)
  }

  /**
   * 设置高亮状态
   * @param {boolean} highlighted - 是否高亮
   */
  setHighlighted(highlighted) {
    this.isHighlighted = highlighted
  }

  /**
   * 获取可以撤回的最后一个方块
   * @returns {Block|null}
   */
  getLastBlock() {
    return this.blocks.length > 0 ? this.blocks[this.blocks.length - 1] : null
  }

    /**
   * 设置消除回调函数
   * @param {function} callback - 回调函数 (type, count) => void
   */
  setEliminationCallback(callback) {
    this.onElimination = callback
  }

  /**
   * 设置消除完成回调函数
   * @param {function} callback - 消除完成回调函数
   */
  setEliminationCompleteCallback(callback) {
    this.onEliminationComplete = callback
  }

  /**
   * 设置动画管理器
   * @param {AnimationManager} animationManager - 动画管理器
   */
  setAnimationManager(animationManager) {
    this.animationManager = animationManager
  }

  /**
   * 设置音频管理器  
   * @param {AudioManager} audioManager - 音频管理器
   */
  setAudioManager(audioManager) {
    this.audioManager = audioManager
  }

  /**
   * 检查是否有消除动画在进行
   * @returns {boolean}
   */
  isEliminationInProgress() {
    return this.eliminationInProgress
  }

  /**
   * 获取槽位的边界框
   * @returns {Object} {x, y, width, height}
   */
  getBounds() {
    return {
      x: this.x,
      y: this.y,
      width: this.width,
      height: this.height
    }
  }
}

// CommonJS导出
module.exports = Slot 