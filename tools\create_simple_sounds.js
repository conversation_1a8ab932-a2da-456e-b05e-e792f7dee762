// 简单的音效生成脚本
// 这个脚本创建一些基本的音效文件用于测试

const fs = require('fs');
const path = require('path');

// 创建音频目录
const audioDir = path.join(__dirname, '..', 'audio');
if (!fs.existsSync(audioDir)) {
    fs.mkdirSync(audioDir, { recursive: true });
}

// 创建一个简单的WAV文件头
function createWavHeader(sampleRate, numSamples) {
    const buffer = Buffer.alloc(44);
    
    // RIFF header
    buffer.write('RIFF', 0);
    buffer.writeUInt32LE(36 + numSamples * 2, 4);
    buffer.write('WAVE', 8);
    
    // fmt chunk
    buffer.write('fmt ', 12);
    buffer.writeUInt32LE(16, 16); // chunk size
    buffer.writeUInt16LE(1, 20);  // audio format (PCM)
    buffer.writeUInt16LE(1, 22);  // num channels
    buffer.writeUInt32LE(sampleRate, 24); // sample rate
    buffer.writeUInt32LE(sampleRate * 2, 28); // byte rate
    buffer.writeUInt16LE(2, 32);  // block align
    buffer.writeUInt16LE(16, 34); // bits per sample
    
    // data chunk
    buffer.write('data', 36);
    buffer.writeUInt32LE(numSamples * 2, 40);
    
    return buffer;
}

// 生成正弦波音频数据
function generateSineWave(frequency, duration, sampleRate = 44100, amplitude = 0.3) {
    const numSamples = Math.floor(duration * sampleRate);
    const samples = Buffer.alloc(numSamples * 2);
    
    for (let i = 0; i < numSamples; i++) {
        const t = i / sampleRate;
        const sample = Math.sin(2 * Math.PI * frequency * t) * amplitude * 32767;
        samples.writeInt16LE(Math.round(sample), i * 2);
    }
    
    return samples;
}

// 生成点击音效
function generateClickSound() {
    const sampleRate = 44100;
    const duration = 0.1;
    const numSamples = Math.floor(duration * sampleRate);
    const samples = Buffer.alloc(numSamples * 2);
    
    for (let i = 0; i < numSamples; i++) {
        const t = i / sampleRate;
        const envelope = Math.exp(-t * 30);
        const sample = Math.sin(2 * Math.PI * 800 * t) * envelope * 0.3 * 32767;
        samples.writeInt16LE(Math.round(sample), i * 2);
    }
    
    const header = createWavHeader(sampleRate, numSamples);
    return Buffer.concat([header, samples]);
}

// 生成匹配音效
function generateMatchSound() {
    const sampleRate = 44100;
    const duration = 0.3;
    const numSamples = Math.floor(duration * sampleRate);
    const samples = Buffer.alloc(numSamples * 2);
    
    for (let i = 0; i < numSamples; i++) {
        const t = i / sampleRate;
        const frequency = 400 + t * 200; // 上升音调
        const envelope = Math.exp(-t * 8);
        const sample = Math.sin(2 * Math.PI * frequency * t) * envelope * 0.4 * 32767;
        samples.writeInt16LE(Math.round(sample), i * 2);
    }
    
    const header = createWavHeader(sampleRate, numSamples);
    return Buffer.concat([header, samples]);
}

// 生成消除音效
function generateEliminateSound() {
    const sampleRate = 44100;
    const duration = 0.4;
    const numSamples = Math.floor(duration * sampleRate);
    const samples = Buffer.alloc(numSamples * 2);
    
    for (let i = 0; i < numSamples; i++) {
        const t = i / sampleRate;
        const noise = (Math.random() - 0.5) * 0.1;
        const tone = Math.sin(2 * Math.PI * 600 * t) * Math.exp(-t * 12);
        const sample = (tone + noise) * 0.5 * 32767;
        samples.writeInt16LE(Math.round(sample), i * 2);
    }
    
    const header = createWavHeader(sampleRate, numSamples);
    return Buffer.concat([header, samples]);
}

// 生成胜利音效
function generateWinSound() {
    const sampleRate = 44100;
    const duration = 1.5;
    const numSamples = Math.floor(duration * sampleRate);
    const samples = Buffer.alloc(numSamples * 2);
    
    for (let i = 0; i < numSamples; i++) {
        const t = i / sampleRate;
        const envelope = Math.max(0, 1 - t * 0.8);
        
        // 和弦：C5 + E5 + G5
        let signal = 0;
        signal += Math.sin(2 * Math.PI * 523 * t) * 0.3; // C5
        signal += Math.sin(2 * Math.PI * 659 * t) * 0.3; // E5
        signal += Math.sin(2 * Math.PI * 784 * t) * 0.3; // G5
        
        const sample = signal * envelope * 0.4 * 32767;
        samples.writeInt16LE(Math.round(sample), i * 2);
    }
    
    const header = createWavHeader(sampleRate, numSamples);
    return Buffer.concat([header, samples]);
}

// 生成失败音效
function generateLoseSound() {
    const sampleRate = 44100;
    const duration = 1.0;
    const numSamples = Math.floor(duration * sampleRate);
    const samples = Buffer.alloc(numSamples * 2);
    
    for (let i = 0; i < numSamples; i++) {
        const t = i / sampleRate;
        const frequency = 300 - t * 100; // 下降音调
        const envelope = Math.exp(-t * 2);
        const sample = Math.sin(2 * Math.PI * frequency * t) * envelope * 0.4 * 32767;
        samples.writeInt16LE(Math.round(sample), i * 2);
    }
    
    const header = createWavHeader(sampleRate, numSamples);
    return Buffer.concat([header, samples]);
}

// 生成洗牌音效
function generateShuffleSound() {
    const sampleRate = 44100;
    const duration = 0.6;
    const numSamples = Math.floor(duration * sampleRate);
    const samples = Buffer.alloc(numSamples * 2);
    
    for (let i = 0; i < numSamples; i++) {
        const t = i / sampleRate;
        const frequency = 400 + Math.sin(t * 50) * 200; // 快速变化的音调
        const envelope = Math.exp(-t * 3);
        const sample = Math.sin(2 * Math.PI * frequency * t) * envelope * 0.3 * 32767;
        samples.writeInt16LE(Math.round(sample), i * 2);
    }
    
    const header = createWavHeader(sampleRate, numSamples);
    return Buffer.concat([header, samples]);
}

// 生成道具音效
function generatePowerupSound() {
    const sampleRate = 44100;
    const duration = 0.5;
    const numSamples = Math.floor(duration * sampleRate);
    const samples = Buffer.alloc(numSamples * 2);
    
    for (let i = 0; i < numSamples; i++) {
        const t = i / sampleRate;
        const freq1 = 440 + Math.sin(t * 20) * 100;
        const freq2 = 660 + Math.sin(t * 30) * 50;
        const envelope = Math.exp(-t * 4);
        
        const signal1 = Math.sin(2 * Math.PI * freq1 * t);
        const signal2 = Math.sin(2 * Math.PI * freq2 * t);
        const sample = (signal1 + signal2) * envelope * 0.3 * 32767;
        samples.writeInt16LE(Math.round(sample), i * 2);
    }
    
    const header = createWavHeader(sampleRate, numSamples);
    return Buffer.concat([header, samples]);
}

// 生成背景音乐
function generateBackgroundMusic() {
    const sampleRate = 44100;
    const duration = 8; // 8秒循环
    const numSamples = Math.floor(duration * sampleRate);
    const samples = Buffer.alloc(numSamples * 2);
    
    // C大调音阶
    const melody = [523, 587, 659, 698, 784, 698, 659, 587];
    
    for (let i = 0; i < numSamples; i++) {
        const t = i / sampleRate;
        const noteIndex = Math.floor(t) % melody.length;
        const frequency = melody[noteIndex];
        const noteTime = t % 1;
        
        // 音符包络
        const attack = Math.min(1, noteTime * 10);
        const decay = Math.max(0.3, 1 - noteTime * 0.7);
        
        // 柔和的正弦波，添加泛音
        let signal = Math.sin(2 * Math.PI * frequency * t) * 0.3;
        signal += Math.sin(2 * Math.PI * frequency * 2 * t) * 0.1; // 第二泛音
        
        const sample = signal * attack * decay * 0.2 * 32767;
        samples.writeInt16LE(Math.round(sample), i * 2);
    }
    
    const header = createWavHeader(sampleRate, numSamples);
    return Buffer.concat([header, samples]);
}

// 生成所有音效文件
const sounds = {
    'click.wav': generateClickSound(),
    'match.wav': generateMatchSound(),
    'eliminate.wav': generateEliminateSound(),
    'win.wav': generateWinSound(),
    'lose.wav': generateLoseSound(),
    'shuffle.wav': generateShuffleSound(),
    'powerup.wav': generatePowerupSound(),
    'background.wav': generateBackgroundMusic()
};

// 写入文件
Object.entries(sounds).forEach(([filename, data]) => {
    const filepath = path.join(audioDir, filename);
    fs.writeFileSync(filepath, data);
    console.log(`生成音效文件: ${filename}`);
});

console.log('所有音效文件生成完成！'); 