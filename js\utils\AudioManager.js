const logger = require('./AsyncLogger.js')

/**
 * 音频管理器
 * 管理游戏中的所有音效和背景音乐
 */
class AudioManager {
  constructor() {
    this.audioContext = new Map()
    this.isEnabled = true
    this.soundEnabled = true
    this.musicEnabled = true
    this.musicVolume = 0.7
    this.soundVolume = 0.8
    this.currentBgMusic = null

    // 音效配置 - 使用绝对路径以确保在微信小游戏环境中正确加载
    this.soundConfig = {
      // 游戏音效
      click: { src: this.getAudioPath('audio/click.wav'), volume: 0.6 },
      match: { src: this.getAudioPath('audio/match.wav'), volume: 0.8 },
      eliminate: { src: this.getAudioPath('audio/eliminate.wav'), volume: 0.9 },
      win: { src: this.getAudioPath('audio/win.wav'), volume: 1.0 },
      lose: { src: this.getAudioPath('audio/lose.wav'), volume: 0.8 },
      shuffle: { src: this.getAudioPath('audio/shuffle.wav'), volume: 0.7 },
      powerup: { src: this.getAudioPath('audio/powerup.wav'), volume: 0.8 },

      // 背景音乐
      bgMusic: { src: this.getAudioPath('audio/background.wav'), volume: 0.5, loop: true }
    }

    logger.info('AudioManager 初始化')
  }

  /**
   * 获取音频文件的正确路径
   * 在不同环境中处理路径差异
   * @param {string} relativePath - 相对路径
   * @returns {string} 处理后的路径
   */
  getAudioPath(relativePath) {
    // 检查是否在微信小游戏环境
    if (typeof wx !== 'undefined') {
      // 在微信小游戏真机环境中，音频文件需要使用 http 或 https 协议
      // 或者使用 wxfile:// 协议（本地文件）
      
      // 移除开头的斜杠和 ./ 
      const cleanPath = relativePath.replace(/^(\.\/|\/)+/, '')
      
      // 在真机环境中，直接返回相对路径
      // 微信小游戏会自动处理本地资源路径
      return cleanPath
    } else {
      // 在其他环境中（如开发环境），使用相对路径
      return relativePath
    }
  }

  /**
   * 初始化音频系统
   */
  async init() {
    try {
      // 检查微信小游戏环境
      if (typeof wx === 'undefined') {
        logger.warn('非微信小游戏环境，音频功能将被禁用')
        this.isEnabled = false
        return
      }

      logger.info('🎵 开始初始化音频系统...')
      logger.info(`   微信环境检测: ${typeof wx !== 'undefined' ? '✅' : '❌'}`)
      logger.info(`   createInnerAudioContext: ${typeof wx.createInnerAudioContext === 'function' ? '✅' : '❌'}`)

      // 加载音频设置
      this.loadSettings()

      // 预加载音效
      await this.preloadSounds()

      logger.info('✅ 音频系统初始化完成')
    } catch (error) {
      logger.error('❌ 音频系统初始化失败:', error)
      this.isEnabled = false
    }
  }

  /**
   * 预加载音效文件
   */
  async preloadSounds() {
    const loadPromises = []
    
    for (const [name, config] of Object.entries(this.soundConfig)) {
      loadPromises.push(this.loadSound(name, config))
    }
    
    try {
      await Promise.all(loadPromises)
      logger.info('所有音效加载完成')
    } catch (error) {
      logger.warn('部分音效加载失败:', error)
    }
  }

  /**
   * 加载单个音效
   * @param {string} name - 音效名称
   * @param {Object} config - 音效配置
   */
  loadSound(name, config) {
    return new Promise(async (resolve, reject) => {
      try {
        logger.info(`🎵 开始加载音效: ${name}`)
        logger.info(`   文件路径: ${config.src}`)
        logger.info(`   音量设置: ${config.volume}`)

        // 在微信小游戏真机环境中，直接使用配置的路径
        // 不需要尝试多种路径格式，因为会导致加载错误
        try {
          const audio = await this.tryLoadAudio(name, config.src, config)
          if (audio) {
            this.audioContext.set(name, audio)
            logger.info(`✅ 音效加载成功: ${name}`)
            resolve(audio)
            return
          }
        } catch (error) {
          logger.error(`❌ 音效加载失败: ${name}`)
          logger.error(`   错误详情:`, error.message || error)
          // 在真机环境中，音频加载失败不应该阻止游戏运行
          // 返回 null 而不是 reject
          resolve(null)
        }

      } catch (error) {
        logger.error(`💥 加载音效时发生异常: ${name}`)
        logger.error(`   错误详情:`, error)
        // 同样，返回 null 而不是 reject
        resolve(null)
      }
    })
  }

  /**
   * 尝试加载音频文件
   * @param {string} name - 音效名称
   * @param {string} path - 文件路径
   * @param {Object} config - 音效配置
   * @returns {Promise<InnerAudioContext|null>}
   */
  tryLoadAudio(name, path, config) {
    return new Promise((resolve, reject) => {
      try {
        // 检查是否支持 createInnerAudioContext
        if (typeof wx === 'undefined' || typeof wx.createInnerAudioContext !== 'function') {
          logger.warn('当前环境不支持音频播放')
          resolve(null)
          return
        }

        const audio = wx.createInnerAudioContext()
        
        // 设置音频属性
        audio.src = path
        audio.volume = config.volume * (name === 'bgMusic' ? this.musicVolume : this.soundVolume)
        audio.loop = config.loop || false
        
        // 在真机环境中，autoplay 可能需要用户交互才能生效
        audio.autoplay = false

        let resolved = false
        let timeoutId = null

        // 成功加载的处理
        const handleCanplay = () => {
          if (!resolved) {
            resolved = true
            if (timeoutId) clearTimeout(timeoutId)
            logger.info(`   音频可以播放: ${name}`)
            resolve(audio)
          }
        }

        // 错误处理
        const handleError = (error) => {
          if (!resolved) {
            resolved = true
            if (timeoutId) clearTimeout(timeoutId)
            
            // 记录详细的错误信息
            const errorMsg = error?.errMsg || error?.message || '未知错误'
            logger.warn(`   音频加载错误: ${name} - ${errorMsg}`)
            
            // 在真机环境中，某些错误可能是暂时的
            // 不要 reject，而是返回 null
            resolve(null)
          }
        }

        // 绑定事件监听器
        audio.onCanplay(handleCanplay)
        audio.onError(handleError)

        // 设置更长的超时时间，真机环境可能需要更多时间
        timeoutId = setTimeout(() => {
          if (!resolved) {
            resolved = true
            logger.warn(`   音频加载超时: ${name} (5秒)`)
            // 超时也返回 null 而不是 reject
            resolve(null)
          }
        }, 5000)

        // 某些情况下，onCanplay 可能不会触发
        // 添加一个备用检查
        setTimeout(() => {
          if (!resolved && audio.duration > 0) {
            handleCanplay()
          }
        }, 100)

      } catch (error) {
        logger.error(`   创建音频上下文失败: ${name}`, error)
        resolve(null)
      }
    })
  }

  /**
   * 播放音效
   * @param {string} name - 音效名称
   * @param {number} volume - 音量（可选，0-1）
   */
  playSound(name, volume = null) {
    if (!this.isEnabled || !this.soundEnabled) return // 检查音效开关
    
    const audio = this.audioContext.get(name)
    if (!audio) {
      logger.warn(`音效不存在: ${name}`)
      return
    }
    
    try {
      // 设置音量
      if (volume !== null) {
        audio.volume = Math.max(0, Math.min(1, volume))
      }
      
      // 重置播放位置到开头
      audio.seek(0)
      
      // 播放音效
      audio.play()
      
      logger.info(`播放音效: ${name}`)
    } catch (error) {
      logger.warn(`播放音效失败: ${name}`, error)
    }
  }

  /**
   * 播放背景音乐
   * @param {string} name - 音乐名称，默认为 'bgMusic'
   */
  playBackgroundMusic(name = 'bgMusic') {
    if (!this.isEnabled || !this.musicEnabled) return // 检查背景音乐开关
    
    // 停止当前背景音乐
    this.stopBackgroundMusic()
    
    const audio = this.audioContext.get(name)
    if (!audio) {
      logger.warn(`背景音乐不存在: ${name}`)
      return
    }
    
    try {
      audio.loop = true
      audio.volume = this.musicVolume
      audio.play()
      this.currentBgMusic = audio
      
      logger.info(`播放背景音乐: ${name}`)
    } catch (error) {
      logger.warn(`播放背景音乐失败: ${name}`, error)
    }
  }

  /**
   * 停止背景音乐
   */
  stopBackgroundMusic() {
    if (this.currentBgMusic) {
      try {
        this.currentBgMusic.stop()
        this.currentBgMusic = null
        logger.info('背景音乐已停止')
      } catch (error) {
        logger.warn('停止背景音乐失败:', error)
      }
    }
  }

  /**
   * 暂停背景音乐
   */
  pauseBackgroundMusic() {
    if (this.currentBgMusic) {
      try {
        this.currentBgMusic.pause()
        logger.info('背景音乐已暂停')
      } catch (error) {
        logger.warn('暂停背景音乐失败:', error)
      }
    }
  }

  /**
   * 恢复背景音乐
   */
  resumeBackgroundMusic() {
    if (this.currentBgMusic) {
      try {
        this.currentBgMusic.play()
        logger.info('背景音乐已恢复')
      } catch (error) {
        logger.warn('恢复背景音乐失败:', error)
      }
    }
  }

  /**
   * 设置音效音量
   * @param {number} volume - 音量（0-1）
   */
  setSoundVolume(volume) {
    this.soundVolume = Math.max(0, Math.min(1, volume))
    
    // 更新所有非背景音乐的音量
    for (const [name, audio] of this.audioContext) {
      if (name !== 'bgMusic' && audio) {
        const config = this.soundConfig[name]
        if (config) {
          audio.volume = config.volume * this.soundVolume
        }
      }
    }
    
    logger.info(`音效音量设置为: ${this.soundVolume}`)
  }

  /**
   * 设置音乐音量
   * @param {number} volume - 音量（0-1）
   */
  setMusicVolume(volume) {
    this.musicVolume = Math.max(0, Math.min(1, volume))
    
    // 更新背景音乐音量
    if (this.currentBgMusic) {
      this.currentBgMusic.volume = this.musicVolume
    }
    
    logger.info(`音乐音量设置为: ${this.musicVolume}`)
  }

  /**
   * 切换音效开关
   */
  toggleSound() {
    this.isEnabled = !this.isEnabled
    
    if (!this.isEnabled) {
      this.stopBackgroundMusic()
    }
    
    logger.info(`音效${this.isEnabled ? '开启' : '关闭'}`)
    return this.isEnabled
  }

  /**
   * 销毁音频管理器
   */
  destroy() {
    try {
      // 停止所有音频
      this.stopBackgroundMusic()
      
      // 销毁所有音频上下文
      for (const [name, audio] of this.audioContext) {
        if (audio) {
          audio.destroy()
        }
      }
      
      this.audioContext.clear()
      
      logger.info('音频管理器已销毁')
    } catch (error) {
      logger.warn('销毁音频管理器失败:', error)
    }
  }

  /**
   * 设置音效启用状态
   * @param {boolean} enabled - 是否启用音效
   */
  setSoundEnabled(enabled) {
    this.soundEnabled = enabled
    logger.info(`音效${enabled ? '开启' : '关闭'}`)
  }

  /**
   * 设置背景音乐启用状态
   * @param {boolean} enabled - 是否启用背景音乐
   */
  setMusicEnabled(enabled) {
    this.musicEnabled = enabled
    
    if (!enabled) {
      // 关闭背景音乐时停止播放
      this.stopBackgroundMusic()
    } else {
      // 开启背景音乐时尝试播放
      if (!this.currentBgMusic) {
        this.playBackgroundMusic()
      }
    }
    
    logger.info(`背景音乐${enabled ? '开启' : '关闭'}`)
  }

  /**
   * 获取音效启用状态
   * @returns {boolean}
   */
  getSoundEnabled() {
    return this.soundEnabled
  }

  /**
   * 获取背景音乐启用状态
   * @returns {boolean}
   */
  getMusicEnabled() {
    return this.musicEnabled
  }

  /**
   * 保存音频设置到本地存储
   */
  saveSettings() {
    try {
      const settings = {
        soundEnabled: this.soundEnabled,
        musicEnabled: this.musicEnabled,
        soundVolume: this.soundVolume,
        musicVolume: this.musicVolume
      }
      
      wx.setStorageSync('audioSettings', settings)
      logger.info('音频设置已保存')
    } catch (error) {
      logger.warn('保存音频设置失败:', error)
    }
  }

  /**
   * 从本地存储加载音频设置
   */
  loadSettings() {
    try {
      const settings = wx.getStorageSync('audioSettings')
      if (settings) {
        this.soundEnabled = settings.soundEnabled !== undefined ? settings.soundEnabled : true
        this.musicEnabled = settings.musicEnabled !== undefined ? settings.musicEnabled : true
        this.soundVolume = settings.soundVolume || 0.8
        this.musicVolume = settings.musicVolume || 0.7
        
        logger.info('音频设置已加载:', settings)
      }
    } catch (error) {
      logger.warn('加载音频设置失败:', error)
    }
  }

  /**
   * 获取音频状态信息
   */
  getStatus() {
    return {
      enabled: this.isEnabled,
      soundEnabled: this.soundEnabled,
      musicEnabled: this.musicEnabled,
      soundVolume: this.soundVolume,
      musicVolume: this.musicVolume,
      loadedSounds: Array.from(this.audioContext.keys()),
      isPlayingMusic: !!this.currentBgMusic
    }
  }
}

// CommonJS导出
module.exports = AudioManager 