const NetworkManager = require('./NetworkManager.js')
const logger = require('AsyncLogger')


/**
 * 用户管理器
 * 负责处理微信小游戏的用户登录和身份识别
 */
class UserManager {
  constructor() {
    this.userInfo = null
    this.userId = null
    this.serverUserId = null // 后端服务器用户ID
    this.isLoggedIn = false
    this.isServerLoggedIn = false // 是否已登录到后端服务器
    this.loginPromise = null
    this.networkManager = new NetworkManager()
    
    // 用户授权状态
    this.hasUserProfile = false
    this.pendingProfileRequest = false
    
    // 本地存储键名
    this.STORAGE_KEYS = {
      USER_INFO: 'duck_user_info',
      SERVER_USER_ID: 'duck_server_user_id',
      SESSION_KEY: 'duck_session_key',
      LAST_LOGIN: 'duck_last_login'
    }
  }

  /**
   * 初始化并登录用户
   * @returns {Promise<Object>} 用户信息
   */
  async init() {
    if (this.loginPromise) {
      return this.loginPromise
    }

    this.loginPromise = this.performLogin()
    return this.loginPromise
  }

  /**
   * 执行登录流程
   * @returns {Promise<Object>} 用户信息
   */
  async performLogin() {
    try {
      logger.info('👤 开始微信用户登录流程...')
      
      // 1. 检查本地存储的用户信息
      const cachedUserInfo = this.loadCachedUserInfo()
      if (cachedUserInfo && this.isCacheValid(cachedUserInfo)) {
        logger.info('👤 使用缓存的用户信息')
        this.userInfo = cachedUserInfo
        this.userId = cachedUserInfo.userId
        this.serverUserId = cachedUserInfo.serverUserId
        this.isLoggedIn = true
        this.isServerLoggedIn = !!cachedUserInfo.serverUserId
        
        // 尝试刷新服务器登录状态（不阻塞）
        this.refreshServerLogin().catch(err => {
          logger.warn('后台刷新服务器登录失败:', err.message)
        })
        
        return this.userInfo
      }
      
      // 2. 获取微信登录凭证
      const loginResult = await this.wxLogin()
      logger.info('👤 获取微信登录凭证成功')
      
      // 3. 获取设备信息
      const systemInfo = this.getSystemInfo()
      
      // 4. 生成本地用户唯一标识
      this.userId = this.generateUserId(loginResult.code, systemInfo)
      
      // 5. 构建基础用户信息对象
      this.userInfo = {
        userId: this.userId,
        code: loginResult.code,
        systemInfo: systemInfo,
        profile: null,
        loginTime: Date.now(),
        hasUserProfile: false,
        serverUserId: null,
        sessionKey: null
      }
      
      this.isLoggedIn = true
      logger.info('👤 本地用户登录成功, 用户ID:', this.userId)
      
      // 6. 尝试连接后端服务器（不阻塞主流程）
      this.connectToServer().catch(err => {
        logger.warn('连接后端服务器失败，将在后台重试:', err.message)
      })
      
      // 7. 保存用户信息到本地存储
      this.saveUserInfo()
      
      return this.userInfo
      
    } catch (error) {
      logger.error('❌ 用户登录失败:', error)
      
      // 登录失败时生成临时用户ID
      this.userId = this.generateFallbackUserId()
      this.userInfo = {
        userId: this.userId,
        isFallback: true,
        loginTime: Date.now(),
        hasUserProfile: false,
        serverUserId: null
      }
      this.isLoggedIn = true
      
      logger.info('👤 使用临时用户ID:', this.userId)
      return this.userInfo
    }
  }

  /**
   * 连接到后端服务器并登录
   */
  async connectToServer() {
    try {
      logger.info('🌐 尝试连接后端服务器...')
      
      // 检查服务器连接
      const isConnected = await this.networkManager.checkConnection()
      if (!isConnected) {
        throw new Error('无法连接到后端服务器')
      }
      
      logger.info('🔑 尝试匿名登录到服务器（不需要用户详细信息）...')
      
      // 调用后端登录接口 - 支持匿名登录
      // 即使没有用户详细信息，也可以用微信code进行登录
      const serverLoginResult = await this.networkManager.userLogin(
        this.userInfo.code,
        this.userInfo.profile || {} // 如果没有profile就传空对象
      )
      
      // 保存服务器登录信息
      this.serverUserId = serverLoginResult.userId
      this.userInfo.serverUserId = serverLoginResult.userId
      this.userInfo.sessionKey = serverLoginResult.sessionKey
      this.userInfo.serverUserInfo = serverLoginResult.userInfo
      this.isServerLoggedIn = true
      
      logger.info('✅ 后端服务器登录成功, 服务器用户ID:', this.serverUserId)
      logger.info('📊 现在可以保存和读取游戏记录了')
      
      // 更新本地存储
      this.saveUserInfo()
      
    } catch (error) {
      logger.error('❌ 连接后端服务器失败:', error)
      this.isServerLoggedIn = false
      
      // 详细分析连接失败的原因
      if (error.message.includes('getUserProfile')) {
        logger.info('💡 提示: 服务器登录失败可能是因为需要用户详细信息')
        logger.info('💡 建议: 后端应该支持仅用微信code进行匿名登录')
      }
      
      throw error
    }
  }

  /**
   * 刷新服务器登录状态
   */
  async refreshServerLogin() {
    if (!this.isLoggedIn) {
      throw new Error('用户未登录')
    }
    
    try {
      // 重新获取微信登录凭证
      const loginResult = await this.wxLogin()
      this.userInfo.code = loginResult.code
      
      // 重新连接服务器
      await this.connectToServer()
      
    } catch (error) {
      logger.error('刷新服务器登录失败:', error)
      throw error
    }
  }

  /**
   * 请求用户授权（用户主动触发）
   * @returns {Promise<Object>} 用户详细信息
   */
  async requestUserProfile() {
    if (this.pendingProfileRequest) {
      logger.info('正在等待用户授权...')
      return null
    }
    
    if (this.hasUserProfile) {
      logger.info('用户已授权')
      return this.userInfo.profile
    }
    
    try {
      this.pendingProfileRequest = true
      logger.info('👤 请求用户授权获取详细信息...')
      
      const profile = await this.getUserProfile()
      this.userInfo.profile = profile
      this.userInfo.hasUserProfile = true
      this.hasUserProfile = true
      
      logger.info('✅ 用户授权成功:', profile.nickName)
      
      // 如果还没有连接到服务器，现在尝试连接
      if (!this.isServerLoggedIn) {
        await this.connectToServer()
      } else {
        // 如果已经连接，更新服务器上的用户信息
        await this.refreshServerLogin()
      }
      
      // 保存更新的用户信息
      this.saveUserInfo()
      
      return profile
      
    } catch (error) {
      logger.error('❌ 用户授权失败:', error)
      
      // 分析错误类型并提供相应的处理
      const errorInfo = this.analyzeAuthError(error)
      logger.info('📋 错误分析结果:', errorInfo)
      
      // 抛出包含详细信息的错误
      const enhancedError = new Error(errorInfo.userMessage)
      enhancedError.type = errorInfo.type
      enhancedError.canRetry = errorInfo.canRetry
      enhancedError.suggestion = errorInfo.suggestion
      
      throw enhancedError
    } finally {
      this.pendingProfileRequest = false
    }
  }

  /**
   * 分析授权错误类型
   * @param {Error} error - 原始错误
   * @returns {Object} 错误分析结果
   */
  analyzeAuthError(error) {
    const errorMsg = error.message || ''
    
    // 隐私政策未配置错误
    if (errorMsg.includes('please go to mp to announce your privacy usage')) {
      return {
        type: 'PRIVACY_POLICY_NOT_CONFIGURED',
        userMessage: '小程序隐私设置需要完善，请联系开发者',
        canRetry: false,
        suggestion: '开发者需要在微信小程序管理后台配置隐私政策',
        technicalDetails: '需要在mp.weixin.qq.com后台的"设置-基本设置-隐私设置"中声明getUserProfile的使用目的'
      }
    }
    
    // 用户拒绝授权
    if (errorMsg.includes('auth deny') || errorMsg.includes('cancel')) {
      return {
        type: 'USER_DENIED',
        userMessage: '您取消了授权，可以继续游戏或稍后重新授权',
        canRetry: true,
        suggestion: '用户可以稍后重新点击授权按钮'
      }
    }
    
    // 接口不支持（旧版本微信）
    if (errorMsg.includes('不支持getUserProfile接口')) {
      return {
        type: 'API_NOT_SUPPORTED',
        userMessage: '当前微信版本不支持，请更新微信版本',
        canRetry: false,
        suggestion: '需要微信版本7.0.9以上'
      }
    }
    
    // 网络错误
    if (errorMsg.includes('network') || errorMsg.includes('timeout')) {
      return {
        type: 'NETWORK_ERROR',
        userMessage: '网络连接异常，请检查网络后重试',
        canRetry: true,
        suggestion: '检查网络连接'
      }
    }
    
    // 其他未知错误
    return {
      type: 'UNKNOWN_ERROR',
      userMessage: '授权失败，请稍后重试',
      canRetry: true,
      suggestion: '稍后重试或联系开发者',
      originalError: errorMsg
    }
  }

  /**
   * 尝试获取用户信息（不弹窗，静默失败）
   * @returns {Promise<Object|null>} 用户信息或null
   */
  async tryGetUserProfile() {
    try {
      // 这里只是尝试，不显示提示
      return await this.getUserProfile()
    } catch (error) {
      logger.info('未能获取用户详细信息（用户未授权）')
      return null
    }
  }

  /**
   * 微信登录
   * @returns {Promise<Object>} 登录结果
   */
  wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            resolve(res)
          } else {
            reject(new Error('获取登录凭证失败: ' + res.errMsg))
          }
        },
        fail: (err) => {
          reject(new Error('微信登录失败: ' + err.errMsg))
        }
      })
    })
  }

  /**
   * 获取用户信息（需要用户授权）
   * @returns {Promise<Object>} 用户信息
   */
  getUserProfile() {
    return new Promise((resolve, reject) => {
      if (typeof wx.getUserProfile === 'function') {
        wx.getUserProfile({
          desc: '用于保存游戏进度和提供个性化服务',
          success: (res) => {
            logger.info('👤 获取用户信息成功:', res.userInfo.nickName)
            resolve(res.userInfo)
          },
          fail: (err) => {
            reject(new Error('获取用户信息失败: ' + err.errMsg))
          }
        })
      } else {
        reject(new Error('不支持getUserProfile接口'))
      }
    })
  }

  /**
   * 获取系统信息
   * @returns {Object} 系统信息
   */
  getSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      return {
        platform: systemInfo.platform,
        model: systemInfo.model,
        system: systemInfo.system,
        version: systemInfo.version,
        brand: systemInfo.brand
      }
    } catch (error) {
      logger.error('获取系统信息失败:', error)
      return {
        platform: 'unknown',
        model: 'unknown',
        system: 'unknown'
      }
    }
  }

  /**
   * 生成用户唯一标识
   * @param {string} code - 微信登录凭证
   * @param {Object} systemInfo - 系统信息
   * @returns {string} 用户ID
   */
  generateUserId(code, systemInfo) {
    // 使用微信code和设备信息生成相对稳定的用户ID
    const deviceFingerprint = `${systemInfo.platform}_${systemInfo.model}_${systemInfo.brand}`
    const timestamp = Math.floor(Date.now() / (1000 * 60 * 60 * 24)) // 按天计算，增加稳定性
    
    // 简单的哈希算法
    const hash = this.simpleHash(`${code}_${deviceFingerprint}_${timestamp}`)
    return `user_${hash}`
  }

  /**
   * 生成后备用户ID（当登录失败时使用）
   * @returns {string} 后备用户ID
   */
  generateFallbackUserId() {
    const systemInfo = this.getSystemInfo()
    const deviceFingerprint = `${systemInfo.platform}_${systemInfo.model}_${systemInfo.brand}`
    
    try {
      const savedUserId = wx.getStorageSync(this.STORAGE_KEYS.USER_INFO)
      if (savedUserId && savedUserId.userId && savedUserId.isFallback) {
        return savedUserId.userId
      }
    } catch (error) {
      logger.warn('读取缓存用户ID失败:', error)
    }
    
    // 生成新的后备用户ID
    const timestamp = Date.now()
    const hash = this.simpleHash(`fallback_${deviceFingerprint}_${timestamp}`)
    const fallbackId = `fallback_${hash}`
    
    logger.info('生成新的后备用户ID:', fallbackId)
    return fallbackId
  }

  /**
   * 简单哈希函数
   * @param {string} str - 要哈希的字符串
   * @returns {string} 哈希值
   */
  simpleHash(str) {
    let hash = 0
    if (str.length === 0) return hash.toString()
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString(36)
  }

  /**
   * 保存用户信息到本地存储
   */
  saveUserInfo() {
    try {
      wx.setStorageSync(this.STORAGE_KEYS.USER_INFO, this.userInfo)
      wx.setStorageSync(this.STORAGE_KEYS.LAST_LOGIN, Date.now())
      logger.info('💾 用户信息已保存到本地存储')
    } catch (error) {
      logger.error('保存用户信息失败:', error)
    }
  }

  /**
   * 从本地存储加载用户信息
   * @returns {Object|null} 用户信息
   */
  loadCachedUserInfo() {
    try {
      const userInfo = wx.getStorageSync(this.STORAGE_KEYS.USER_INFO)
      return userInfo || null
    } catch (error) {
      logger.error('读取缓存用户信息失败:', error)
      return null
    }
  }

  /**
   * 检查缓存是否有效
   * @param {Object} cachedUserInfo - 缓存的用户信息
   * @returns {boolean} 是否有效
   */
  isCacheValid(cachedUserInfo) {
    if (!cachedUserInfo || !cachedUserInfo.loginTime) {
      return false
    }
    
    // 缓存有效期：24小时
    const CACHE_DURATION = 24 * 60 * 60 * 1000
    const now = Date.now()
    
    return (now - cachedUserInfo.loginTime) < CACHE_DURATION
  }

  /**
   * 获取用户ID
   * @returns {string} 用户ID
   */
  getUserId() {
    return this.userId
  }

  /**
   * 获取服务器用户ID
   * @returns {number|null} 服务器用户ID
   */
  getServerUserId() {
    return this.serverUserId
  }

  /**
   * 获取用户信息
   * @returns {Object} 用户信息
   */
  getUserInfo() {
    return this.userInfo
  }

  /**
   * 检查用户是否已登录
   * @returns {boolean} 是否已登录
   */
  isUserLoggedIn() {
    return this.isLoggedIn
  }

  /**
   * 检查是否已连接到服务器
   * @returns {boolean} 是否已连接到服务器
   */
  isConnectedToServer() {
    return this.isServerLoggedIn
  }

  /**
   * 检查用户是否已授权详细信息
   * @returns {boolean} 是否已授权
   */
  hasDetailedProfile() {
    return this.hasUserProfile
  }

  /**
   * 刷新登录状态
   * @returns {Promise<Object>} 用户信息
   */
  async refreshLogin() {
    logger.info('🔄 刷新用户登录状态...')
    this.loginPromise = null
    return this.performLogin()
  }

  /**
   * 获取用户存储前缀
   * @returns {string} 存储前缀
   */
  getUserStoragePrefix() {
    return `duck_${this.userId || 'guest'}_`
  }

  /**
   * 登出用户
   */
  logout() {
    logger.info('👋 用户登出')
    
    // 清理内存状态
    this.userInfo = null
    this.userId = null
    this.serverUserId = null
    this.isLoggedIn = false
    this.isServerLoggedIn = false
    this.hasUserProfile = false
    this.loginPromise = null
    
    // 清理本地存储
    try {
      wx.removeStorageSync(this.STORAGE_KEYS.USER_INFO)
      wx.removeStorageSync(this.STORAGE_KEYS.SERVER_USER_ID)
      wx.removeStorageSync(this.STORAGE_KEYS.SESSION_KEY)
      wx.removeStorageSync(this.STORAGE_KEYS.LAST_LOGIN)
    } catch (error) {
      logger.error('清理本地存储失败:', error)
    }
  }

  /**
   * 获取用户登录历史（如果已连接到服务器）
   * @param {number} limit - 限制返回数量
   * @returns {Promise<Array>} 登录历史
   */
  async getLoginHistory(limit = 10) {
    if (!this.isServerLoggedIn || !this.serverUserId) {
      throw new Error('未连接到服务器')
    }
    
    return this.networkManager.getUserLoginHistory(this.serverUserId, limit)
  }

  /**
   * 保存关卡通关记录到服务器
   * @param {number} level - 关卡编号  
   * @param {number} score - 得分
   * @param {number} moves - 移动步数
   * @param {number} playTime - 游戏时长（秒）
   * @returns {Promise<Object>} 保存结果
   */
  async saveLevelProgress(level, score, moves, playTime) {
    try {
      if (!this.isConnectedToServer()) {
        logger.warn('🚫 未连接到服务器，无法保存通关记录')
        return {
          success: false,
          error: '未连接到服务器'
        }
      }
      
      logger.info(`💾 保存关卡${level}通关记录: 得分${score}, 步数${moves}, 时长${playTime}秒`)
      
      // 调用网络管理器的接口
      const result = await this.networkManager.saveLevelProgress(
        this.serverUserId,
        level,
        score, 
        moves,
        playTime
      )
      
      if (result.success) {
        logger.info(`✅ 关卡${level}通关记录保存成功`, result.data)
        return result
      } else {
        logger.error(`❌ 关卡${level}通关记录保存失败:`, result.error)
        return result
      }
      
    } catch (error) {
      logger.error('保存关卡进度异常:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 获取用户通关进度
   * @returns {Promise<Object>} 用户进度信息
   */
  async getUserProgress() {
    try {
      if (!this.isConnectedToServer()) {
        logger.warn('🚫 未连接到服务器，无法获取用户进度')
        return {
          success: false,
          error: '未连接到服务器'
        }
      }
      
      logger.info('📊 获取用户通关进度...')
      
      // 调用网络管理器的接口
      const result = await this.networkManager.getUserProgress(this.serverUserId)
      
      if (result.success) {
        logger.info('✅ 用户进度获取成功', result.data)
        return result
      } else {
        logger.error('❌ 用户进度获取失败:', result.error)
        return result
      }
      
    } catch (error) {
      logger.error('获取用户进度异常:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 获取指定关卡的最佳记录
   * @param {number} level - 关卡编号
   * @returns {Promise<Object>} 关卡最佳记录
   */
  async getLevelRecord(level) {
    try {
      if (!this.isConnectedToServer()) {
        logger.warn('🚫 未连接到服务器，无法获取关卡记录')
        return {
          success: false,
          error: '未连接到服务器'
        }
      }
      
      logger.info(`📈 获取关卡${level}最佳记录...`)
      
      // 调用网络管理器的接口
      const result = await this.networkManager.getLevelRecord(this.serverUserId, level)
      
      if (result.success) {
        logger.info(`✅ 关卡${level}记录获取成功`, result.data)
        return result
      } else {
        logger.error(`❌ 关卡${level}记录获取失败:`, result.error)
        return result
      }
      
    } catch (error) {
      logger.error('获取关卡记录异常:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 设置网络管理器的服务器地址
   * @param {string} baseURL - 服务器基础URL
   */
  setServerURL(baseURL) {
    this.networkManager.setBaseURL(baseURL)
  }

  /**
   * 诊断当前登录状态和用户ID信息
   * @returns {Object} 诊断结果
   */
  diagnoseLogin() {
    const diagnosis = {
      timestamp: new Date().toLocaleString(),
      localLogin: {
        status: this.isLoggedIn ? '✅ 已登录' : '❌ 未登录',
        userId: this.userId || '未生成',
        userIdType: this.userId ? (this.userId.startsWith('fallback_') ? '后备ID' : '正常ID') : '无',
        hasUserInfo: !!this.userInfo,
        loginTime: this.userInfo?.loginTime ? new Date(this.userInfo.loginTime).toLocaleString() : '未知'
      },
      serverLogin: {
        status: this.isServerLoggedIn ? '✅ 已连接' : '❌ 未连接',
        serverUserId: this.serverUserId || '未获取',
        hasSessionKey: !!this.userInfo?.sessionKey,
        serverUserInfo: !!this.userInfo?.serverUserInfo
      },
      userProfile: {
        status: this.hasUserProfile ? '✅ 已授权' : '❌ 未授权',
        hasNickName: !!this.userInfo?.profile?.nickName,
        hasAvatar: !!this.userInfo?.profile?.avatarUrl,
        profileData: this.userInfo?.profile || null
      },
      systemInfo: this.userInfo?.systemInfo || null,
      cache: {
        hasCache: !!this.loadCachedUserInfo(),
        cacheValid: this.loadCachedUserInfo() ? this.isCacheValid(this.loadCachedUserInfo()) : false
      }
    }

    logger.info('🔍 用户登录诊断结果:')
    logger.info('📱 本地登录状态:', diagnosis.localLogin)
    logger.info('🌐 服务器登录状态:', diagnosis.serverLogin)
    logger.info('👤 用户详细信息:', diagnosis.userProfile)
    logger.info('💾 缓存状态:', diagnosis.cache)
    
    return diagnosis
  }

  /**
   * 获取简化的登录状态信息
   * @returns {Object} 登录状态
   */
  getLoginStatus() {
    return {
      isLoggedIn: this.isLoggedIn,
      isServerLoggedIn: this.isServerLoggedIn,
      hasUserProfile: this.hasUserProfile,
      userId: this.userId,
      serverUserId: this.serverUserId,
      userType: this.userId ? (this.userId.startsWith('fallback_') ? 'fallback' : 'normal') : 'none'
    }
  }
}

module.exports = UserManager 