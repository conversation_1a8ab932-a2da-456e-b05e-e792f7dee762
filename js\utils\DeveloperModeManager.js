/**
 * 全局开发者模式管理器
 * 统一管理开发者模式的所有功能
 */

const logger = require('./AsyncLogger')

// 延迟加载GameConfig，避免循环依赖
let gameConfig = null
function getGameConfig() {
  if (!gameConfig) {
    try {
      gameConfig = require('./GameConfig')
    } catch (e) {
      return null
    }
  }
  return gameConfig
}

class DeveloperModeManager {
  constructor() {
    this.initialized = false
    this.eventListeners = []
    
    // 延迟初始化，确保GameConfig已加载
    setTimeout(() => {
      this.initialize()
    }, 0)
  }
  
  /**
   * 初始化开发者模式管理器
   */
  initialize() {
    if (this.initialized) return
    
    const config = getGameConfig()
    if (config) {
      this.initialized = true
      logger.info('🎮 DeveloperModeManager初始化完成')
      
      // 输出当前状态
      this.logCurrentStatus()
    } else {
      // 如果GameConfig还未加载，稍后重试
      setTimeout(() => {
        this.initialize()
      }, 100)
    }
  }
  
  /**
   * 获取开发者模式状态
   * @returns {boolean}
   */
  isEnabled() {
    const config = getGameConfig()
    if (config && typeof config.isDeveloperMode === 'function') {
      return config.isDeveloperMode()
    }
    return false
  }
  
  /**
   * 设置开发者模式
   * @param {boolean} enabled - 是否启用
   */
  setEnabled(enabled) {
    const config = getGameConfig()
    if (config && typeof config.setDeveloperMode === 'function') {
      const oldState = this.isEnabled()
      config.setDeveloperMode(enabled)
      
      // 触发事件
      if (oldState !== enabled) {
        this.triggerEvent('change', { enabled, oldState })
      }
      
      return true
    }
    
    logger.error('❌ GameConfig不可用，无法设置开发者模式')
    return false
  }
  
  /**
   * 切换开发者模式
   * @returns {boolean} 新的状态
   */
  toggle() {
    const config = getGameConfig()
    if (config && typeof config.toggleDeveloperMode === 'function') {
      const oldState = this.isEnabled()
      const newState = config.toggleDeveloperMode()
      
      // 触发事件
      this.triggerEvent('change', { enabled: newState, oldState })
      
      return newState
    }
    
    logger.error('❌ GameConfig不可用，无法切换开发者模式')
    return false
  }
  
  /**
   * 检查关卡是否已解锁
   * @param {number} level - 关卡编号
   * @param {number} maxUnlockedLevel - 当前最高解锁关卡
   * @returns {boolean}
   */
  isLevelUnlocked(level, maxUnlockedLevel = 1) {
    const config = getGameConfig()
    if (config && typeof config.isLevelUnlocked === 'function') {
      return config.isLevelUnlocked(level, maxUnlockedLevel)
    }
    
    // 降级方案
    return this.isEnabled() ? true : level <= maxUnlockedLevel
  }
  
  /**
   * 获取开发者模式的所有功能状态
   * @returns {Object}
   */
  getFeatureStatus() {
    const enabled = this.isEnabled()
    
    return {
      enabled: enabled,
      features: {
        allLevelsUnlocked: enabled,
        debugLogsEnabled: enabled,
        infoLogsEnabled: enabled,
        serverSyncDisabled: enabled,
        devMenuEnabled: enabled
      }
    }
  }
  
  /**
   * 添加事件监听器
   * @param {string} event - 事件名称 ('change')
   * @param {Function} callback - 回调函数
   */
  addEventListener(event, callback) {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = []
    }
    this.eventListeners[event].push(callback)
  }
  
  /**
   * 移除事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  removeEventListener(event, callback) {
    if (this.eventListeners[event]) {
      const index = this.eventListeners[event].indexOf(callback)
      if (index > -1) {
        this.eventListeners[event].splice(index, 1)
      }
    }
  }
  
  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {Object} data - 事件数据
   */
  triggerEvent(event, data) {
    if (this.eventListeners[event]) {
      this.eventListeners[event].forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          logger.error('❌ 开发者模式事件回调执行失败:', error)
        }
      })
    }
  }
  
  /**
   * 输出当前状态
   */
  logCurrentStatus() {
    const status = this.getFeatureStatus()
    
    logger.info('🎮 开发者模式状态:')
    logger.info(`   启用状态: ${status.enabled}`)
    logger.info(`   所有关卡解锁: ${status.features.allLevelsUnlocked}`)
    logger.info(`   DEBUG日志: ${status.features.debugLogsEnabled}`)
    logger.info(`   INFO日志: ${status.features.infoLogsEnabled}`)
    logger.info(`   服务器同步: ${status.features.serverSyncDisabled ? '已禁用' : '已启用'}`)
    logger.info(`   开发菜单: ${status.features.devMenuEnabled}`)
  }
  
  /**
   * 快速启用开发者模式（调试用）
   */
  quickEnable() {
    logger.info('🚀 快速启用开发者模式...')
    const success = this.setEnabled(true)
    
    if (success) {
      logger.info('✅ 开发者模式已启用')
      logger.info('🔓 所有关卡已解锁')
      logger.info('📝 DEBUG/INFO日志已启用')
      this.logCurrentStatus()
    }
    
    return success
  }
  
  /**
   * 快速禁用开发者模式（生产用）
   */
  quickDisable() {
    logger.info('🔒 快速禁用开发者模式...')
    const success = this.setEnabled(false)
    
    if (success) {
      logger.info('✅ 开发者模式已禁用')
      logger.info('🔐 关卡按顺序解锁')
      logger.info('📝 仅显示WARN/ERROR日志')
      this.logCurrentStatus()
    }
    
    return success
  }
}

// 创建全局实例
const developerModeManager = new DeveloperModeManager()

// 添加全局快捷方法
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.enableDevMode = () => developerModeManager.quickEnable()
  window.disableDevMode = () => developerModeManager.quickDisable()
  window.toggleDevMode = () => developerModeManager.toggle()
  window.devModeStatus = () => developerModeManager.logCurrentStatus()
} else if (typeof global !== 'undefined') {
  // Node.js环境
  global.enableDevMode = () => developerModeManager.quickEnable()
  global.disableDevMode = () => developerModeManager.quickDisable()
  global.toggleDevMode = () => developerModeManager.toggle()
  global.devModeStatus = () => developerModeManager.logCurrentStatus()
}

// CommonJS导出
module.exports = developerModeManager
