const logger = require('AsyncLogger')

/**
 * 工具类
 * 提供常用的工具函数
 */
class Utils {
  /**
   * 获取画布适配后的尺寸
   * @returns {{width: number, height: number, pixelRatio: number, screenWidth: number, screenHeight: number}}
   */
  static getCanvasSize() {
    const info = wx.getSystemInfoSync()
    const pixelRatio = info.pixelRatio
    const screenWidth = info.screenWidth
    const screenHeight = info.screenHeight
    
    // 注意：这个方法已经被废弃，建议在setupCanvas中直接获取系统信息
    logger.warn('Utils.getCanvasSize已废弃，建议直接使用wx.getSystemInfoSync()')
    
    return {
      width: screenWidth * pixelRatio,      // Canvas物理宽度
      height: screenHeight * pixelRatio,    // Canvas物理高度
      pixelRatio,                          // 设备像素比
      screenWidth,                         // 逻辑宽度
      screenHeight                         // 逻辑高度
    }
  }

  /**
   * 检查点是否在矩形内
   * @param {Object} point - 点坐标 {x, y}
   * @param {Object} rect - 矩形 {x, y, width, height}
   * @returns {boolean}
   */
  static isPointInRect(point, rect) {
    return point.x >= rect.x && 
           point.x <= rect.x + rect.width &&
           point.y >= rect.y && 
           point.y <= rect.y + rect.height
  }

  /**
   * 计算两点之间的距离
   * @param {Object} p1 - 点1 {x, y}
   * @param {Object} p2 - 点2 {x, y}
   * @returns {number}
   */
  static distance(p1, p2) {
    const dx = p1.x - p2.x
    const dy = p1.y - p2.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * 线性插值
   * @param {number} start - 起始值
   * @param {number} end - 结束值
   * @param {number} t - 插值因子 (0-1)
   * @returns {number}
   */
  static lerp(start, end, t) {
    return start + (end - start) * t
  }

  /**
   * 限制数值在指定范围内
   * @param {number} value - 数值
   * @param {number} min - 最小值
   * @param {number} max - 最大值
   * @returns {number}
   */
  static clamp(value, min, max) {
    return Math.min(Math.max(value, min), max)
  }

  /**
   * 随机整数
   * @param {number} min - 最小值
   * @param {number} max - 最大值
   * @returns {number}
   */
  static randomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min
  }

  /**
   * 洗牌算法
   * @param {Array} array - 要洗牌的数组
   * @returns {Array}
   */
  static shuffle(array) {
    const result = [...array]
    for (let i = result.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [result[i], result[j]] = [result[j], result[i]]
    }
    return result
  }

  /**
   * 防抖函数
   * @param {function} func - 要防抖的函数
   * @param {number} delay - 延迟时间
   * @returns {function}
   */
  static debounce(func, delay) {
    let timeoutId
    return function(...args) {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => func.apply(this, args), delay)
    }
  }

  /**
   * 格式化数字显示
   * @param {number} num - 数字
   * @returns {string}
   */
  static formatNumber(num) {
    if (num < 1000) return num.toString()
    if (num < 1000000) return (num / 1000).toFixed(1) + 'K'
    return (num / 1000000).toFixed(1) + 'M'
  }
}

// CommonJS导出
module.exports = Utils 