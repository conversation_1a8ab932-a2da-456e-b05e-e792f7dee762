/**
 * 可折叠面板组件
 * 用于创建可以展开/折叠的信息面板
 */
class CollapsiblePanel {
  /**
   * 构造函数
   * @param {number} x - 面板X坐标
   * @param {number} y - 面板Y坐标
   * @param {number} width - 面板宽度
   * @param {string} title - 面板标题
   * @param {Array} content - 面板内容数组，每个元素是一行文本
   * @param {boolean} collapsed - 初始是否折叠状态
   */
  constructor(x, y, width, title, content = [], collapsed = true) {
    this.x = x
    this.y = y
    this.width = width
    this.title = title
    this.content = content
    this.collapsed = collapsed
    
    // 样式配置
    this.headerHeight = 40
    this.lineHeight = 20
    this.padding = 10
    this.backgroundColor = 'rgba(255, 255, 255, 0.9)'
    this.headerColor = '#4CAF50'
    this.borderColor = '#CCCCCC'
    this.textColor = '#333333'
    this.titleColor = '#FFFFFF'
    this.borderRadius = 8
    this.borderWidth = 1
    
    // 动画属性
    this.animationDuration = 300 // 毫秒
    this.currentHeight = this.headerHeight
    this.targetHeight = this.headerHeight
    this.animationStartTime = 0
    this.isAnimating = false
    
    // 计算内容高度
    this.contentHeight = this.content.length * this.lineHeight + this.padding * 2
    this.fullHeight = this.headerHeight + this.contentHeight
    
    // 如果初始状态是展开的，设置对应高度
    if (!this.collapsed) {
      this.currentHeight = this.fullHeight
      this.targetHeight = this.fullHeight
    }
  }

  /**
   * 设置内容
   * @param {Array} content - 新的内容数组
   */
  setContent(content) {
    this.content = content
    this.contentHeight = this.content.length * this.lineHeight + this.padding * 2
    this.fullHeight = this.headerHeight + this.contentHeight
    
    // 如果当前是展开状态，更新目标高度
    if (!this.collapsed) {
      this.targetHeight = this.fullHeight
    }
  }

  /**
   * 添加内容行
   * @param {string} line - 要添加的文本行
   */
  addContentLine(line) {
    this.content.push(line)
    this.setContent(this.content)
  }

  /**
   * 清空内容
   */
  clearContent() {
    this.setContent([])
  }

  /**
   * 切换折叠状态
   */
  toggle() {
    this.collapsed = !this.collapsed
    this.startAnimation()
  }

  /**
   * 展开面板
   */
  expand() {
    if (this.collapsed) {
      this.collapsed = false
      this.startAnimation()
    }
  }

  /**
   * 折叠面板
   */
  collapse() {
    if (!this.collapsed) {
      this.collapsed = true
      this.startAnimation()
    }
  }

  /**
   * 开始动画
   */
  startAnimation() {
    this.targetHeight = this.collapsed ? this.headerHeight : this.fullHeight
    this.animationStartTime = Date.now()
    this.isAnimating = true
  }

  /**
   * 检查点击是否在面板内
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {boolean} 是否在面板内
   */
  containsPoint(x, y) {
    return x >= this.x && x <= this.x + this.width &&
           y >= this.y && y <= this.y + this.currentHeight
  }

  /**
   * 检查点击是否在标题栏内
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {boolean} 是否在标题栏内
   */
  isHeaderClick(x, y) {
    return x >= this.x && x <= this.x + this.width &&
           y >= this.y && y <= this.y + this.headerHeight
  }

  /**
   * 处理点击事件
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {boolean} 是否处理了点击
   */
  handleClick(x, y) {
    if (this.isHeaderClick(x, y)) {
      this.toggle()
      return true
    }
    return false
  }

  /**
   * 更新面板状态
   * @param {number} deltaTime - 时间差
   */
  update(deltaTime) {
    // 更新动画
    if (this.isAnimating) {
      const elapsed = Date.now() - this.animationStartTime
      const progress = Math.min(elapsed / this.animationDuration, 1)
      
      // 使用缓动函数
      const easedProgress = this.easeInOutCubic(progress)
      
      const startHeight = this.collapsed ? this.fullHeight : this.headerHeight
      this.currentHeight = startHeight + (this.targetHeight - startHeight) * easedProgress
      
      if (progress >= 1) {
        this.currentHeight = this.targetHeight
        this.isAnimating = false
      }
    }
  }

  /**
   * 缓动函数 - 三次方缓入缓出
   * @param {number} t - 进度值 (0-1)
   * @returns {number} 缓动后的值
   */
  easeInOutCubic(t) {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2
  }

  /**
   * 渲染面板
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    ctx.save()

    // 绘制面板背景
    this.drawRoundedRect(ctx, this.x, this.y, this.width, this.currentHeight, this.borderRadius)
    ctx.fillStyle = this.backgroundColor
    ctx.fill()

    // 绘制边框
    ctx.strokeStyle = this.borderColor
    ctx.lineWidth = this.borderWidth
    ctx.stroke()

    // 绘制标题栏
    this.drawRoundedRect(ctx, this.x, this.y, this.width, this.headerHeight, this.borderRadius)
    ctx.fillStyle = this.headerColor
    ctx.fill()

    // 绘制标题文字
    ctx.fillStyle = this.titleColor
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'left'
    ctx.textBaseline = 'middle'
    
    // 绘制折叠/展开图标
    const iconX = this.x + this.padding
    const iconY = this.y + this.headerHeight / 2
    const icon = this.collapsed ? '▶' : '▼'
    ctx.fillText(icon, iconX, iconY)
    
    // 绘制标题
    ctx.fillText(this.title, iconX + 20, iconY)

    // 如果面板展开或正在动画，绘制内容
    if (!this.collapsed || this.isAnimating) {
      // 设置裁剪区域，防止内容溢出
      ctx.save()
      ctx.beginPath()
      ctx.rect(this.x, this.y + this.headerHeight, this.width, this.currentHeight - this.headerHeight)
      ctx.clip()

      // 绘制内容
      ctx.fillStyle = this.textColor
      ctx.font = '14px Arial'
      ctx.textAlign = 'left'
      ctx.textBaseline = 'top'

      const contentStartY = this.y + this.headerHeight + this.padding
      this.content.forEach((line, index) => {
        const lineY = contentStartY + index * this.lineHeight
        // 只绘制可见的行
        if (lineY < this.y + this.currentHeight) {
          ctx.fillText(line, this.x + this.padding, lineY)
        }
      })

      ctx.restore()
    }

    ctx.restore()
  }

  /**
   * 绘制圆角矩形路径
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} radius - 圆角半径
   */
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
  }

  /**
   * 获取面板当前高度
   * @returns {number} 当前高度
   */
  getCurrentHeight() {
    return this.currentHeight
  }

  /**
   * 设置位置
   * @param {number} x - 新的X坐标
   * @param {number} y - 新的Y坐标
   */
  setPosition(x, y) {
    this.x = x
    this.y = y
  }

  /**
   * 设置主题颜色
   * @param {string} theme - 主题类型 ('blue', 'green', 'orange', 'red')
   */
  setTheme(theme) {
    switch (theme) {
      case 'blue':
        this.headerColor = '#2196F3'
        break
      case 'green':
        this.headerColor = '#4CAF50'
        break
      case 'orange':
        this.headerColor = '#FF9800'
        break
      case 'red':
        this.headerColor = '#f44336'
        break
    }
  }
}

// CommonJS导出
module.exports = CollapsiblePanel 