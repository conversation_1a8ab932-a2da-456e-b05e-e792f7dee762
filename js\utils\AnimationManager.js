/**
 * 动画管理器
 * 管理游戏中的各种动画效果
 */
class AnimationManager {
  constructor() {
    this.activeAnimations = []
    this.easingFunctions = {
      linear: t => t,
      easeInOut: t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
      easeOut: t => 1 - Math.pow(1 - t, 3),
      bounce: t => {
        const n1 = 7.5625
        const d1 = 2.75
        if (t < 1 / d1) {
          return n1 * t * t
        } else if (t < 2 / d1) {
          return n1 * (t -= 1.5 / d1) * t + 0.75
        } else if (t < 2.5 / d1) {
          return n1 * (t -= 2.25 / d1) * t + 0.9375
        } else {
          return n1 * (t -= 2.625 / d1) * t + 0.984375
        }
      },
      elastic: t => {
        if (t === 0) return 0
        if (t === 1) return 1
        return Math.pow(2, -10 * t) * Math.sin((t - 0.1) * 5 * Math.PI) + 1
          }
  }
}

  /**
   * 创建移动动画
   * @param {Object} obj - 要移动的对象
   * @param {number} targetX - 目标X坐标
   * @param {number} targetY - 目标Y坐标
   * @param {number} duration - 动画持续时间(ms)
   * @param {string} easing - 缓动函数名称
   * @param {function} onComplete - 完成回调
   */
  animateMove(obj, targetX, targetY, duration = 500, easing = 'easeOut', onComplete = null) {
    const startX = obj.x
    const startY = obj.y
    const startTime = Date.now()

    const animation = {
      update: (currentTime) => {
        const elapsed = currentTime - startTime
        const progress = Math.min(elapsed / duration, 1)
        const easedProgress = this.easingFunctions[easing](progress)

        obj.x = startX + (targetX - startX) * easedProgress
        obj.y = startY + (targetY - startY) * easedProgress

        if (progress >= 1) {
          obj.x = targetX
          obj.y = targetY
          onComplete && onComplete()
          return false // 动画完成
        }
        return true // 动画继续
      }
    }

    this.activeAnimations.push(animation)
    return animation
  }

  /**
   * 创建缩放动画
   * @param {Object} obj - 要缩放的对象
   * @param {number} targetScale - 目标缩放值
   * @param {number} duration - 动画持续时间(ms)
   * @param {string} easing - 缓动函数名称
   * @param {function} onComplete - 完成回调
   */
  animateScale(obj, targetScale, duration = 300, easing = 'bounce', onComplete = null) {
    const startScale = obj.scale || 1
    const startTime = Date.now()

    const animation = {
      update: (currentTime) => {
        const elapsed = currentTime - startTime
        const progress = Math.min(elapsed / duration, 1)
        const easedProgress = this.easingFunctions[easing](progress)

        obj.scale = startScale + (targetScale - startScale) * easedProgress

        if (progress >= 1) {
          obj.scale = targetScale
          onComplete && onComplete()
          return false
        }
        return true
      }
    }

    this.activeAnimations.push(animation)
    return animation
  }

  /**
   * 创建旋转动画
   * @param {Object} obj - 要旋转的对象
   * @param {number} targetRotation - 目标旋转角度(弧度)
   * @param {number} duration - 动画持续时间(ms)
   * @param {string} easing - 缓动函数名称
   * @param {function} onComplete - 完成回调
   */
  animateRotation(obj, targetRotation, duration = 400, easing = 'easeOut', onComplete = null) {
    const startRotation = obj.rotation || 0
    const startTime = Date.now()

    const animation = {
      update: (currentTime) => {
        const elapsed = currentTime - startTime
        const progress = Math.min(elapsed / duration, 1)
        const easedProgress = this.easingFunctions[easing](progress)

        obj.rotation = startRotation + (targetRotation - startRotation) * easedProgress

        if (progress >= 1) {
          obj.rotation = targetRotation
          onComplete && onComplete()
          return false
        }
        return true
      }
    }

    this.activeAnimations.push(animation)
    return animation
  }

  /**
   * 创建淡入淡出动画
   * @param {Object} obj - 要淡化的对象
   * @param {number} targetAlpha - 目标透明度(0-1)
   * @param {number} duration - 动画持续时间(ms)
   * @param {string} easing - 缓动函数名称
   * @param {function} onComplete - 完成回调
   */
  animateFade(obj, targetAlpha, duration = 400, easing = 'linear', onComplete = null) {
    const startAlpha = obj.alpha || 1
    const startTime = Date.now()

    const animation = {
      update: (currentTime) => {
        const elapsed = currentTime - startTime
        const progress = Math.min(elapsed / duration, 1)
        const easedProgress = this.easingFunctions[easing](progress)

        obj.alpha = startAlpha + (targetAlpha - startAlpha) * easedProgress

        if (progress >= 1) {
          obj.alpha = targetAlpha
          onComplete && onComplete()
          return false
        }
        return true
      }
    }

    this.activeAnimations.push(animation)
    return animation
  }

  /**
   * 创建弹跳点击动画
   * @param {Object} obj - 要动画的对象
   * @param {function} onComplete - 完成回调
   */
  animateClickBounce(obj, onComplete = null) {
    // 先缩小到0.8倍，然后弹回到1.2倍，最后回到1倍
    this.animateScale(obj, 0.8, 100, 'easeOut', () => {
      this.animateScale(obj, 1.2, 150, 'bounce', () => {
        this.animateScale(obj, 1, 100, 'easeOut', onComplete)
      })
    })
  }

  /**
   * 创建消除动画
   * @param {Object} obj - 要消除的对象
   * @param {function} onComplete - 完成回调
   */
  animateElimination(obj, onComplete = null) {
    // 组合动画：旋转 + 缩放 + 淡出
    this.animateRotation(obj, Math.PI * 2, 400, 'easeOut')
    this.animateScale(obj, 0, 400, 'easeOut')
    this.animateFade(obj, 0, 400, 'easeOut', () => {
      obj.isVisible = false
      onComplete && onComplete()
    })
  }

  /**
   * 创建洗牌动画
   * @param {Array} blocks - 方块数组
   * @param {function} onComplete - 完成回调
   * @param {Object} bounds - 游戏板边界 {x, y, width, height}
   */
  animateShuffle(blocks, onComplete = null, bounds = null) {
    let completedAnimations = 0
    const totalAnimations = blocks.length

    blocks.forEach((block, index) => {
      // 计算安全的随机位置，确保不超出边界
      let randomX, randomY
      
      if (bounds) {
        // 如果提供了边界信息，限制随机移动范围
        const maxOffsetX = Math.min(100, (bounds.width - block.width) / 4)
        const maxOffsetY = Math.min(100, (bounds.height - block.height) / 4)
        
        randomX = block.x + (Math.random() - 0.5) * maxOffsetX * 2
        randomY = block.y + (Math.random() - 0.5) * maxOffsetY * 2
        
        // 确保随机位置在边界内
        randomX = Math.max(bounds.x, Math.min(randomX, bounds.x + bounds.width - block.width))
        randomY = Math.max(bounds.y, Math.min(randomY, bounds.y + bounds.height - block.height))
      } else {
        // 降级方案：使用较小的随机偏移
        const safeOffset = 50
        randomX = block.x + (Math.random() - 0.5) * safeOffset * 2
        randomY = block.y + (Math.random() - 0.5) * safeOffset * 2
      }
      
      // 先移动到随机位置
      this.animateMove(block, randomX, randomY, 200, 'easeOut', () => {
        // 然后移动到最终位置
        this.animateMove(block, block.targetX, block.targetY, 300, 'bounce', () => {
          completedAnimations++
          if (completedAnimations === totalAnimations) {
            onComplete && onComplete()
          }
        })
      })

      // 同时添加旋转效果
      this.animateRotation(block, Math.PI * 2, 500, 'linear', () => {
        block.rotation = 0 // 重置旋转
      })
    })
  }

  /**
   * 创建UI按钮悬停动画
   * @param {Object} button - 按钮对象
   * @param {boolean} isHover - 是否悬停状态
   */
  animateButtonHover(button, isHover) {
    const targetScale = isHover ? 1.1 : 1
    this.animateScale(button, targetScale, 200, 'easeOut')
  }

  /**
   * 创建分数增加动画
   * @param {Object} scoreObj - 分数对象，包含{value, displayValue}
   * @param {number} targetScore - 目标分数
   * @param {function} onComplete - 完成回调
   */
  animateScoreIncrease(scoreObj, targetScore, onComplete = null) {
    const startScore = scoreObj.value
    const startTime = Date.now()
    const duration = 800

    const animation = {
      update: (currentTime) => {
        const elapsed = currentTime - startTime
        const progress = Math.min(elapsed / duration, 1)
        const easedProgress = this.easingFunctions.easeOut(progress)

        scoreObj.displayValue = Math.floor(startScore + (targetScore - startScore) * easedProgress)

        if (progress >= 1) {
          scoreObj.value = targetScore
          scoreObj.displayValue = targetScore
          onComplete && onComplete()
          return false
        }
        return true
      }
    }

    this.activeAnimations.push(animation)
    return animation
  }

  /**
   * 创建胜利庆祝动画
   * @param {Array} elements - 要动画的UI元素数组
   * @param {function} onComplete - 完成回调
   */
  animateVictoryCelebration(elements, onComplete = null) {
    let completedAnimations = 0
    const totalAnimations = elements.length

    elements.forEach((element, index) => {
      // 延迟启动，创建波浪效果
      setTimeout(() => {
        this.animateScale(element, 1.3, 300, 'bounce', () => {
          this.animateScale(element, 1, 200, 'easeOut', () => {
            completedAnimations++
            if (completedAnimations === totalAnimations) {
              onComplete && onComplete()
            }
          })
        })
      }, index * 100)
    })
  }

  /**
   * 更新所有活动动画
   * @param {number} currentTime - 当前时间戳
   */
  update(currentTime) {
    this.activeAnimations = this.activeAnimations.filter(animation => {
      return animation.update(currentTime)
    })
  }

  /**
   * 清除所有动画
   */
  clear() {
    this.activeAnimations = []
  }

  /**
   * 获取活动动画数量
   * @returns {number}
   */
  getActiveAnimationCount() {
    return this.activeAnimations.length
  }
}

// CommonJS导出
module.exports = AnimationManager 