{"setting": {"es6": true, "postcss": false, "compileWorklet": false, "minified": false, "uglifyFileName": false, "uploadWithSourceMap": true, "enhance": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true, "checkInvalidKey": true, "checkSiteMap": false, "useStaticServer": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "game", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "isGameTourist": false, "appid": "wx64c47f2f10566938", "editorSetting": {}, "libVersion": "3.8.6"}