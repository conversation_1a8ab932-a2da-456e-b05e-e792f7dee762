const Block = require('./Block.js')
const Utils = require('../utils/Utils.js')
const logger = require('../utils/AsyncLogger')


/**
 * 游戏板类
 * 管理游戏区域的方块布局和层级关系
 */
class Board {
  /**
   * 构造函数
   * @param {number} x - 游戏板X坐标
   * @param {number} y - 游戏板Y坐标
   * @param {number} width - 游戏板宽度
   * @param {number} height - 游戏板高度
   */
  constructor(x, y, width, height) {
    this.x = x
    this.y = y
    this.width = width
    this.height = height
    this.blocks = []              // 所有方块
    this.layers = new Map()       // 按层级组织的方块
    this.maxLayers = 3            // 羊了个羊模式：固定三层
    this.baseBlockSize = 55       // 基础方块大小
    this.blockSize = 55           // 当前方块大小 - 根据关卡动态调整
    this.gridCols = 8             // 网格列数
    this.gridRows = 8             // 网格行数
    this.layerOffset = 10         // 层级偏移量
    this.currentLevel = 1         // 当前关卡等级
    
    // 羊了个羊三层系统
    this.LAYER_BOTTOM = 0         // 下层（最底层）
    this.LAYER_MIDDLE = 1         // 中层
    this.LAYER_TOP = 2            // 上层（最上层）
    this.currentActiveLayer = this.LAYER_TOP  // 当前可操作的层级，默认从上层开始
    
    logger.info(`Board初始化: 区域(${x}, ${y}, ${width}x${height}), 方块大小${this.blockSize}`)
    logger.info(`羊了个羊模式: 三层系统 (下层:${this.LAYER_BOTTOM}, 中层:${this.LAYER_MIDDLE}, 上层:${this.LAYER_TOP})`)
  }

  /**
   * 根据关卡和方块数量动态计算方块大小
   * @param {number} level - 关卡等级
   * @param {number} totalBlocks - 总方块数量
   */
  calculateDynamicBlockSize(level, totalBlocks) {
    // 计算可用区域（保留边距）
    const availableWidth = this.width * 0.9  // 留10%边距
    const availableHeight = this.height * 0.8  // 留20%边距，避免底部遮挡
    
    // 根据方块数量估算合适的大小
    const approximateBlocksPerRow = Math.ceil(Math.sqrt(totalBlocks * availableWidth / availableHeight))
    const maxSizeByWidth = availableWidth / approximateBlocksPerRow
    const maxSizeByHeight = availableHeight / Math.ceil(totalBlocks / approximateBlocksPerRow)
    
    // 取较小值，确保不超出边界
    let calculatedSize = Math.min(maxSizeByWidth, maxSizeByHeight)
    
    // 限制大小范围，不要太小或太大
    calculatedSize = Math.max(30, Math.min(this.baseBlockSize, calculatedSize))
    
    this.blockSize = Math.floor(calculatedSize)
    logger.info(`🎯 关卡${level}: 总方块${totalBlocks}个, 可用区域${availableWidth.toFixed(1)}x${availableHeight.toFixed(1)}, 方块大小${this.blockSize}`)
  }

  /**
   * 创建方块的辅助方法
   * @param {number} type - 方块类型
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} layer - 层级
   * @returns {Block}
   */
  createBlock(type, x, y, layer = 0) {
    const block = new Block(type, x, y, layer)
    // 设置游戏板方块的正确大小
    block.width = this.blockSize
    block.height = this.blockSize
    return block
  }

  /**
   * 初始化游戏板 - 生成关卡布局（羊了个羊模式）
   * @param {number} level - 关卡等级
   */
  initLevel(level) {
    this.clear()
    
    // 设置当前关卡
    this.currentLevel = level
    
    // 重置层级系统为初始状态
    this.currentActiveLayer = this.LAYER_TOP
    
    // 根据关卡选择不同的布局生成方式
    if (level <= 4) {
      // 前4关：使用羊了个羊三层布局，根据关卡调整难度
      this.generateThreeLayerLayout(level)
    } else {
      // 第5关及以后：使用特定的关卡布局
      this.generateSpecificLevel(level)
    }

    // 更新层级关系
    this.updateLayers()
    
    // 更新可点击状态
    this.updateClickableStates()
    
    logger.info(`🎮 第${level}关初始化完成，当前活跃层: ${this.currentActiveLayer}`)
  }

  /**
   * 生成特定关卡布局
   * @param {number} level - 关卡等级
   */
  generateSpecificLevel(level) {
    if (level >= 7) {
      // 第7关及以后统一使用钻石布局系统
      this.generateDiamondLevel(level)
      return
    }
    
    // 保持前6关的原有布局
    switch(level) {
      case 5:
        this.generateLevel5()
        break
      case 6:
        this.generateLevel6()
        break
      default:
        // 超过第20关的，使用随机布局
        this.generateRandomLevel(level)
        break
    }
  }

  /**
   * 生成钻石布局关卡 - 通用方法（第7-20关）
   * @param {number} level - 关卡等级
   */
  generateDiamondLevel(level) {
    logger.info(`🎮 第${level}关: 钻石布局难度系统`)
    
    // 获取关卡难度配置
    const config = this.getDiamondLevelConfig(level)
    const { blockTypes, blocksPerType, totalBlocks } = config
    
    logger.info(`第${level}关配置: ${blockTypes.length}种类型 × ${blocksPerType}个 = ${totalBlocks}个方块`)
    
    // 第7关及以后保持固定方块大小
    this.blockSize = this.baseBlockSize
    logger.info(`🎯 第${level}关使用固定方块大小: ${this.blockSize}像素`)
    
    // 创建方块数组
    const allBlocks = []
    blockTypes.forEach(type => {
      for (let i = 0; i < blocksPerType; i++) {
        allBlocks.push(type)
      }
    })
    
    // 打乱方块顺序
    this.shuffleArray(allBlocks)
    
    // 生成钻石布局
    const layouts = this.createAdaptiveDiamondLayouts(level, totalBlocks)
    this.placeBlocksInLayoutsWithBounds(allBlocks, layouts)
    
    logger.info(`第${level}关共创建 ${this.blocks.length} 个方块`)
    this.validateBlockCounts()
  }

  /**
   * 获取钻石布局关卡配置
   * @param {number} level - 关卡等级
   * @returns {Object} 配置对象 {blockTypes, blocksPerType, totalBlocks}
   */
  getDiamondLevelConfig(level) {
    // 定义关卡难度配置表
    const levelConfigs = {
      7:  { typeCount: 8,  blocksPerType: 12 }, // 96个方块
      8:  { typeCount: 8,  blocksPerType: 12 }, // 96个方块 - 钻石布局基础版
      9:  { typeCount: 9,  blocksPerType: 12 }, // 108个方块 - 增加方块类型
      10: { typeCount: 10, blocksPerType: 12 }, // 120个方块 - 经典难度
      
      // 🎯 第11关及以后优化：2层布局+减少方块类型+大幅增加间距，让玩家更容易凑齐
      11: { typeCount: 6,  blocksPerType: 21 }, // 126个方块 - 进一步减少类型，增加可匹配性
      12: { typeCount: 6,  blocksPerType: 24 }, // 144个方块 - 保持类型，增加数量
      13: { typeCount: 7,  blocksPerType: 24 }, // 168个方块 - 微增类型，保持平衡
      14: { typeCount: 7,  blocksPerType: 27 }, // 189个方块 - 保持类型，增加数量
      15: { typeCount: 8,  blocksPerType: 27 }, // 216个方块 - 适度增加类型
      16: { typeCount: 8,  blocksPerType: 30 }, // 240个方块 - 保持类型，增加数量
      
      // 🚀 第17-20关重新优化：降低方块数量，增加分散程度，确保足够的初始可点击方块
      17: { typeCount: 7,  blocksPerType: 24 }, // 168个方块 - 减少数量和类型，提高可玩性
      18: { typeCount: 7,  blocksPerType: 27 }, // 189个方块 - 适度增加，保持平衡
      19: { typeCount: 8,  blocksPerType: 27 }, // 216个方块 - 微增类型，控制总量
      20: { typeCount: 8,  blocksPerType: 30 }  // 240个方块 - 最终boss关，但仍保持合理难度
    }
    
    // 获取配置，超过20关使用20关的配置
    const config = levelConfigs[level] || levelConfigs[20]
    
    // 生成方块类型数组 - 支持最多14种类型
    const blockTypes = []
    for (let i = 0; i < config.typeCount; i++) {
      blockTypes.push(i)
    }
    
    const totalBlocks = config.typeCount * config.blocksPerType
    
    logger.info(`🎯 第${level}关难度配置:`, {
      typeCount: config.typeCount,
      blocksPerType: config.blocksPerType,
      totalBlocks,
      description: this.getLevelDescription(level),
      layoutType: level >= 11 ? '2层布局(优化)' : '3层布局'
    })
    
    return {
      blockTypes,
      blocksPerType: config.blocksPerType,
      totalBlocks
    }
  }

  /**
   * 获取关卡描述
   * @param {number} level - 关卡等级
   * @returns {string} 关卡描述
   */
  getLevelDescription(level) {
    const descriptions = {
      7: '钻石布局原版',
      8: '钻石布局基础版',
      9: '增加方块类型',
      10: '经典难度',
      11: '2层优化-75%可点击',
      12: '2层优化-稳定递增', 
      13: '2层优化-微增挑战',
      14: '2层优化-平衡提升',
      15: '2层优化-适度挑战',
      16: '2层优化-增强密度',
      17: '2层优化-分散分布',
      18: '2层优化-平衡递增',
      19: '2层优化-合理挑战',
      20: '2层优化-最终boss'
    }
    return descriptions[level] || '2层优化-超级挑战'
  }

  /**
   * 创建自适应钻石布局
   * @param {number} level - 关卡等级
   * @param {number} totalBlocks - 总方块数
   * @returns {Array} 布局数组
   */
  createAdaptiveDiamondLayouts(level, totalBlocks) {
    const centerX = this.width / 2
    const centerY = this.height * 0.45 // 将中心点上移，避免底部遮挡
    
    // 根据方块数量动态调整间距和密度
    let baseSpacing = this.blockSize * 0.9
    let densityFactor = 1.0
    
    // 根据方块数量调整布局密度
    if (totalBlocks > 200) {
      densityFactor = 0.8 // 高密度时缩小间距
      baseSpacing = this.blockSize * 0.75
    } else if (totalBlocks > 150) {
      densityFactor = 0.9
      baseSpacing = this.blockSize * 0.8
    }
    
    logger.info(`🎨 第${level}关自适应钻石布局: 总方块${totalBlocks}, 基础间距${baseSpacing.toFixed(1)}, 密度因子${densityFactor}`)
    
    // 🎯 针对第11关及以后的关卡优化：改为2层布局，大幅增加初始可点击方块数量
    if (level >= 11) {
      logger.info(`🚀 第${level}关优化：使用2层布局，重点减少重叠，增加可激活方块`)
      
      // 🔧 针对第17-20关特别优化：更大的间距和更高的上层比例
      let difficultyFactor, spacingReduction, topLayerRatio, bottomLayerRatio
      
      if (level >= 17 && level <= 20) {
        // 第17-20关：重新设计难度曲线，优先保证分散分布
        difficultyFactor = 0.9 + (level - 17) * 0.05    // 从0.9递增到1.05，更温和
        spacingReduction = Math.max(0.1 - (level - 17) * 0.02, 0.02)  // 从0.1递减到0.04，保持较大间距
        
        // 大幅增加上层比例，确保更多可点击方块
        topLayerRatio = 0.88 - (level - 17) * 0.01      // 从88%递减到85%，保持高可点击率
        bottomLayerRatio = 1 - topLayerRatio             // 相应减少底层比例
        
        logger.info(`  🎯 第${level}关特殊优化: 分散布局模式，上层比例${(topLayerRatio*100).toFixed(0)}%`)
      } else {
        // 第11-16关：原有的难度递增逻辑
        difficultyFactor = Math.min(1 + (level - 11) * 0.08, 1.8)  // 从1.0递增到1.8
        spacingReduction = Math.min((level - 11) * 0.05, 0.5)       // 从0递增到0.5，减少间距
        
        // 计算两层分配：根据关卡调整层级比例
        topLayerRatio = 0.85 - (level - 11) * 0.02    // 从85%递减到67%
        bottomLayerRatio = 1 - topLayerRatio           // 相应增加底层比例
        
        // 确保比例合理
        topLayerRatio = Math.max(topLayerRatio, 0.67)
        bottomLayerRatio = Math.min(bottomLayerRatio, 0.33)
      }
      
      const topLayerCount = Math.floor(totalBlocks * topLayerRatio)
      const bottomLayerCount = totalBlocks - topLayerCount
      
      // 🔧 关键优化：根据关卡等级调整间距
      let baseWideSpacing, baseExtraWideSpacing
      
      if (level >= 17 && level <= 20) {
        // 第17-20关：使用更大的间距，确保分散分布
        baseWideSpacing = baseSpacing * (2.0 + spacingReduction)        // 从2.1倍递增到2.06倍，保持大间距
        baseExtraWideSpacing = baseSpacing * (2.8 + spacingReduction)   // 从2.9倍递增到2.86倍，超大间距
        logger.info(`  🎯 第${level}关使用加大间距: 宽间距${baseWideSpacing.toFixed(1)}, 超宽间距${baseExtraWideSpacing.toFixed(1)}`)
      } else {
        // 第11-16关：原有的间距逻辑
        baseWideSpacing = baseSpacing * (1.5 - spacingReduction)      // 逐步减少宽间距
        baseExtraWideSpacing = baseSpacing * (2.0 - spacingReduction) // 逐步减少超宽间距
      }
      
      logger.info(`  → 第${level}关难度调整: 难度因子${difficultyFactor.toFixed(2)}, 间距减少${(spacingReduction*100).toFixed(0)}%`)
      logger.info(`  → 2层分配: 上层${topLayerCount}个方块(${(topLayerRatio*100).toFixed(0)}%), 下层${bottomLayerCount}个方块(${(bottomLayerRatio*100).toFixed(0)}%)`)
      logger.info(`  → 间距配置: 宽间距${baseWideSpacing.toFixed(1)}, 超宽间距${baseExtraWideSpacing.toFixed(1)}`)
      logger.info(`  → 预期激活方块: 目标${Math.floor(topLayerCount * (0.8 - (level-11)*0.02))}+ 个`)
      
      return [
        // 第0层（底层）：根据关卡等级调整遮挡方块密度
        {
          positions: level >= 17 && level <= 20 ? [
            // 第17-20关：大幅减少底层遮挡，使用更分散的布局
            ...this.generateCircularPositions(centerX, centerY, baseWideSpacing * 1.8, 
              Math.floor(bottomLayerCount * 0.5)), // 只使用50%的底层方块作为遮挡
            ...this.generateCenterGridPositions(centerX, centerY, baseWideSpacing * 1.2, 2, 2) // 使用更小的中心网格
          ] : [
            // 第11-16关：原有的底层布局
            ...this.generateCircularPositions(centerX, centerY, baseWideSpacing * (1.2 - spacingReduction*0.5), 
              Math.floor(bottomLayerCount * (0.7 + spacingReduction))), // 后续关卡增加底层密度
            ...this.generateCenterGridPositions(centerX, centerY, baseWideSpacing * (0.8 - spacingReduction*0.3), 3, 3) // 逐步减少网格间距
          ]
        },
        // 第1层（顶层）：根据关卡等级调整可点击方块分布
        {
          positions: level >= 17 && level <= 20 ? [
            // 第17-20关：最大化分散分布，确保大量可点击方块
            // 外围超大圆：最大间距
            ...this.generateWideSpacedCircularPositions(centerX, centerY, 
              baseExtraWideSpacing * 3.0, Math.floor(topLayerCount * 0.35)),
            // 中层大圆：大间距
            ...this.generateWideSpacedCircularPositions(centerX, centerY, 
              baseExtraWideSpacing * 2.2, Math.floor(topLayerCount * 0.3)),
            // 内层散布：填补空隙
            ...this.generateWideSpacedRandomPositions(centerX, centerY, 
              baseWideSpacing * 2.0, Math.floor(topLayerCount * 0.25)),
            // 边角补充：利用边缘空间
            ...this.generateCornerPositions(centerX, centerY, 
              baseExtraWideSpacing * 1.5, Math.floor(topLayerCount * 0.1))
          ] : [
            // 第11-16关：原有的顶层布局
            // 外围大圆：逐步减少间距
            ...this.generateWideSpacedCircularPositions(centerX, centerY, 
              baseExtraWideSpacing * (2.5 - spacingReduction), Math.floor(topLayerCount * 0.3)),
            // 中层圆：适中分布，逐步密集
            ...this.generateWideSpacedCircularPositions(centerX, centerY, 
              baseExtraWideSpacing * (1.8 - spacingReduction*0.7), Math.floor(topLayerCount * 0.25)),
            // 内层散布：填补空隙，逐步增加
            ...this.generateWideSpacedRandomPositions(centerX, centerY, 
              baseWideSpacing * (1.5 - spacingReduction*0.6), Math.floor(topLayerCount * (0.25 + spacingReduction*0.1))),
            // 边角补充：利用边缘空间，逐步增加
            ...this.generateCornerPositions(centerX, centerY, 
              baseExtraWideSpacing * (1.0 - spacingReduction*0.5), Math.floor(topLayerCount * (0.2 + spacingReduction*0.1)))
          ]
        }
      ]
    }
    
    // 第7-10关保持原来的3层布局
    return [
      // 第0层（底层）：外层钻石 + 额外的散布点
      {
        positions: [
          ...this.generateAdvancedDiamondPositions(centerX, centerY, baseSpacing * 3.5 * densityFactor, 3, level), // 外层钻石
          ...this.generateRandomPositions(centerX, centerY, baseSpacing * 2 * densityFactor, Math.floor(totalBlocks * 0.15), 1.5) // 外围散布点
        ]
      },
      // 第1层（中层）：中层钻石 + 圆形分布
      {
        positions: [
          ...this.generateAdvancedDiamondPositions(centerX, centerY, baseSpacing * 2.5 * densityFactor, 3, level), // 中层钻石
          ...this.generateCircularPositions(centerX, centerY, baseSpacing * 2 * densityFactor, Math.floor(totalBlocks * 0.12)) // 圆形分布
        ]
      },
      // 第2层（顶层）：内层钻石 + 中心区域
      {
        positions: [
          ...this.generateAdvancedDiamondPositions(centerX, centerY, baseSpacing * 1.5 * densityFactor, 2, level), // 内层钻石
          ...this.generateCenterGridPositions(centerX, centerY, baseSpacing * 0.6 * densityFactor, 
            Math.ceil(Math.sqrt(totalBlocks * 0.2)), Math.ceil(Math.sqrt(totalBlocks * 0.2))) // 中心网格
        ]
      }
    ]
  }

  /**
   * 生成高级钻石位置（改进版）
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标
   * @param {number} size - 钻石大小
   * @param {number} layers - 层数
   * @param {number} level - 关卡等级（用于调整密度）
   * @returns {Array} 位置数组
   */
  generateAdvancedDiamondPositions(centerX, centerY, size, layers, level) {
    const positions = []
    
    // 根据关卡等级调整点密度
    const baseDensity = Math.min(8 + level, 20) // 基础密度随关卡增加
    
    for (let layer = 0; layer < layers; layer++) {
      const layerSize = size * (1 + layer * 0.8) // 逐层增大
      const basePoints = baseDensity + layer * 4 // 基础点数逐层增加
      
      // 主要的方向点
      for (let i = 0; i < basePoints; i++) {
        const angle = (i / basePoints) * Math.PI * 2 + Math.PI / 4
        const x = centerX + Math.cos(angle) * layerSize
        const y = centerY + Math.sin(angle) * layerSize
        positions.push({ x, y })
      }
      
      // 在每层之间添加中间点，增加密度
      if (layer > 0) {
        const midLayerSize = size * (0.5 + layer * 0.8)
        const midPoints = Math.floor(baseDensity * 0.75) + layer * 2
        for (let i = 0; i < midPoints; i++) {
          const angle = (i / midPoints) * Math.PI * 2 + Math.PI / 8
          const x = centerX + Math.cos(angle) * midLayerSize
          const y = centerY + Math.sin(angle) * midLayerSize
          positions.push({ x, y })
        }
      }
    }
    
    return positions
  }

  /**
   * 生成羊了个羊三层布局
   * @param {number} level - 关卡等级，影响方块数量和难度
   */
  generateThreeLayerLayout(level) {
    logger.info(`🎮 生成羊了个羊第${level}关三层布局（严格遮挡规则）`)
    
    // 前4关保持原来的固定方块大小（55像素）
    this.blockSize = this.baseBlockSize
    logger.info(`🎯 第${level}关使用固定方块大小: ${this.blockSize}像素`)
    
    // 根据关卡等级决定方块配置
    const difficulty = this.calculateDifficulty(level)
    const { blockTypes, blocksPerType } = difficulty
    
    logger.info(`难度配置: ${blockTypes.length}种类型, 每种${blocksPerType}个方块`)
    
    // 创建方块数组
    const allBlocks = []
    blockTypes.forEach(type => {
      for (let i = 0; i < blocksPerType; i++) {
        allBlocks.push(type)
      }
    })
    
    // 打乱方块顺序
    this.shuffleArray(allBlocks)
    
    // 使用严格遮挡规则生成布局
    this.generateStrictOverlapLayout(allBlocks)
    
    logger.info(`羊了个羊三层布局完成: 总共${this.blocks.length}个方块`)
    this.validateBlockCounts()
  }

  /**
   * 计算关卡难度配置
   * @param {number} level - 关卡等级
   * @returns {Object} 包含方块类型和数量的配置
   */
  calculateDifficulty(level) {
    // 基础配置
    let typeCount = Math.min(8, Math.max(4, level + 2))  // 方块类型数量 4-8
    let baseGroups = Math.max(2, Math.floor(level * 1.2))  // 每种类型的组数 2-12
    
    // 确保每种类型的方块数量是3的倍数
    const blocksPerType = baseGroups * 3
    
    return {
      blockTypes: Array.from({length: typeCount}, (_, i) => i),
      blocksPerType: blocksPerType
    }
  }

  /**
   * 计算三层的方块分配
   * @param {number} totalBlocks - 总方块数量
   * @returns {Object} 三层的方块分配
   */
  calculateLayerDistribution(totalBlocks) {
    // 羊了个羊典型分配：下层40%，中层35%，上层25%
    const bottom = Math.floor(totalBlocks * 0.4)
    const middle = Math.floor(totalBlocks * 0.35)
    const top = totalBlocks - bottom - middle  // 剩余的都给上层
    
    return { bottom, middle, top }
  }

  /**
   * 生成下层方块（最密集分布）
   * @param {Array} allBlocks - 所有方块数组
   * @param {number} startIndex - 开始索引
   * @param {number} count - 方块数量
   * @returns {number} 下一个索引
   */
  generateBottomLayer(allBlocks, startIndex, count) {
    logger.info(`🔸 生成下层(Layer ${this.LAYER_BOTTOM}): ${count}个方块，密集分布`)
    
    const centerX = this.width / 2
    const centerY = this.height / 2
    const spacing = this.blockSize * 0.7  // 密集间距
    
    // 网格布局，密集排列
    const cols = Math.ceil(Math.sqrt(count * 1.2))  // 稍微宽一点的布局
    const rows = Math.ceil(count / cols)
    
    const startX = centerX - (cols - 1) * spacing / 2
    const startY = centerY - (rows - 1) * spacing / 2
    
    let blockIndex = startIndex
    for (let i = 0; i < count && blockIndex < allBlocks.length; i++) {
      const row = Math.floor(i / cols)
      const col = i % cols
      
      const x = this.x + startX + col * spacing - this.blockSize / 2
      const y = this.y + startY + row * spacing - this.blockSize / 2
      
      const type = allBlocks[blockIndex]
      const block = this.createBlock(type, x, y, this.LAYER_BOTTOM)
      this.blocks.push(block)
      
      logger.info(`  下层方块${blockIndex}: 类型${type}, 位置(${x.toFixed(1)}, ${y.toFixed(1)})`)
      blockIndex++
    }
    
    return blockIndex
  }

  /**
   * 生成中层方块（中等密度分布）
   * @param {Array} allBlocks - 所有方块数组
   * @param {number} startIndex - 开始索引
   * @param {number} count - 方块数量
   * @returns {number} 下一个索引
   */
  generateMiddleLayer(allBlocks, startIndex, count) {
    logger.info(`🔹 生成中层(Layer ${this.LAYER_MIDDLE}): ${count}个方块，中等密度`)
    
    const centerX = this.width / 2
    const centerY = this.height / 2
    const spacing = this.blockSize * 0.9  // 中等间距
    
    // 随机散布，但保持一定的结构
    const positions = this.generateRandomPositions(centerX, centerY, spacing, count, 0.6)
    
    let blockIndex = startIndex
    for (let i = 0; i < count && blockIndex < allBlocks.length; i++) {
      const pos = positions[i]
      const x = this.x + pos.x - this.blockSize / 2
      const y = this.y + pos.y - this.blockSize / 2
      
      const type = allBlocks[blockIndex]
      const block = this.createBlock(type, x, y, this.LAYER_MIDDLE)
      this.blocks.push(block)
      
      logger.info(`  中层方块${blockIndex}: 类型${type}, 位置(${x.toFixed(1)}, ${y.toFixed(1)})`)
      blockIndex++
    }
    
    return blockIndex
  }

  /**
   * 生成上层方块（稀疏分布）
   * @param {Array} allBlocks - 所有方块数组
   * @param {number} startIndex - 开始索引
   * @param {number} count - 方块数量
   * @returns {number} 下一个索引
   */
  generateTopLayer(allBlocks, startIndex, count) {
    logger.info(`🔺 生成上层(Layer ${this.LAYER_TOP}): ${count}个方块，稀疏分布`)
    
    const centerX = this.width / 2
    const centerY = this.height / 2
    const spacing = this.blockSize * 1.2  // 稀疏间距
    
    // 随机散布，覆盖面更广
    const positions = this.generateRandomPositions(centerX, centerY, spacing, count, 0.8)
    
    let blockIndex = startIndex
    for (let i = 0; i < count && blockIndex < allBlocks.length; i++) {
      const pos = positions[i]
      const x = this.x + pos.x - this.blockSize / 2
      const y = this.y + pos.y - this.blockSize / 2
      
      const type = allBlocks[blockIndex]
      const block = this.createBlock(type, x, y, this.LAYER_TOP)
      this.blocks.push(block)
      
      logger.info(`  上层方块${blockIndex}: 类型${type}, 位置(${x.toFixed(1)}, ${y.toFixed(1)})`)
      blockIndex++
    }
    
    return blockIndex
  }

  /**
   * 生成随机位置数组
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标
   * @param {number} spacing - 基础间距
   * @param {number} count - 位置数量
   * @param {number} spread - 散布范围系数 (0-1)
   * @returns {Array} 位置数组
   */
  generateRandomPositions(centerX, centerY, spacing, count, spread) {
    const positions = []
    const maxRadius = Math.min(this.width, this.height) * spread / 2
    
    for (let i = 0; i < count; i++) {
      let x, y
      let attempts = 0
      
      do {
        // 生成随机位置
        const angle = Math.random() * Math.PI * 2
        const radius = Math.random() * maxRadius
        x = centerX + Math.cos(angle) * radius
        y = centerY + Math.sin(angle) * radius
        
        // 确保位置在有效范围内
        x = Math.max(this.blockSize, Math.min(x, this.width - this.blockSize))
        y = Math.max(this.blockSize, Math.min(y, this.height - this.blockSize))
        
        attempts++
      } while (attempts < 10 && this.isPositionTooClose(x, y, positions, spacing))
      
      positions.push({ x, y })
    }
    
    return positions
  }

  /**
   * 检查位置是否与已有位置太近
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {Array} existingPositions - 已有位置
   * @param {number} minDistance - 最小距离
   * @returns {boolean}
   */
  isPositionTooClose(x, y, existingPositions, minDistance) {
    return existingPositions.some(pos => {
      const dx = x - pos.x
      const dy = y - pos.y
      return Math.sqrt(dx * dx + dy * dy) < minDistance
    })
  }

  /**
   * 生成严格遮挡规则的方块布局
   * 只有3种情况：完全不遮挡、完全遮挡、1/4角部遮挡
   * @param {Array} allBlocks - 所有方块类型数组
   */
  generateStrictOverlapLayout(allBlocks) {
    logger.info('=== 生成严格遮挡规则布局 ===')
    
    const centerX = this.width / 2
    const centerY = this.height / 2
    const spacing = this.blockSize + 15  // 增加间距，确保底层方块不重叠
    
    let blockIndex = 0
    // 使用 calculateLayerDistribution 获取三层布局数量
    const { bottom: baseCount, middle: quarterCount, top: fullCount } = this.calculateLayerDistribution(allBlocks.length)

    // 第0层：底层方块，完全不重叠
    const basePositions = this.generateNonOverlapPositions(centerX, centerY, spacing, baseCount)
    
    logger.info(`第0层：${baseCount}个底层方块（完全不重叠）`)
    basePositions.forEach((pos, index) => {
      if (blockIndex < allBlocks.length) {
        // 修复：generateNonOverlapPositions返回的坐标已经是绝对坐标，不需要再加this.x和this.y
        const x = pos.x
        const y = pos.y
        const type = allBlocks[blockIndex]
        const block = this.createBlock(type, x, y, 0)
        this.blocks.push(block)
        logger.info(`  底层方块${blockIndex}: 类型${type}, 位置(${x.toFixed(1)}, ${y.toFixed(1)})`)
        blockIndex++
      }
    })
    
    // 第1层：1/4遮挡方块
    logger.info(`第1层：${quarterCount}个1/4遮挡方块`)
    for (let i = 0; i < quarterCount && blockIndex < allBlocks.length; i++) {
      const baseBlockIndex = i % baseCount  // 循环选择底层方块
      const baseBlock = this.blocks[baseBlockIndex]
      const cornerPos = this.generateQuarterOverlapPosition(baseBlock)
      
      const x = cornerPos.x
      const y = cornerPos.y
      const type = allBlocks[blockIndex]
      const block = this.createBlock(type, x, y, 1) // Layer 1 for quarter overlap
      this.blocks.push(block)
      logger.info(`  生成1/4遮挡方块 (idx ${blockIndex}, type ${type}, layer 1) at (${x.toFixed(1)}, ${y.toFixed(1)}) over baseBlock (idx ${baseBlockIndex}, type ${baseBlock.type}, layer ${baseBlock.layer})`);
      blockIndex++
    }
    
    // 第2层：完全遮挡方块
    logger.info(`第2层：${fullCount}个完全遮挡方块`)
    
    const layer1StartIndex = baseCount  // 第1层方块的起始索引
    
    for (let i = 0; i < fullCount && blockIndex < allBlocks.length; i++) {
      let targetBlock
      
      if (i < quarterCount) {
        // 优先遮挡第1层方块
        const layer1Index = layer1StartIndex + (i % quarterCount)
        targetBlock = this.blocks[layer1Index]
        logger.info(`  → 第2层方块${i}将遮挡第1层方块${layer1Index}`)
      } else {
        // 剩余的完全遮挡方块遮挡第0层方块
        const layer0Index = i % baseCount
        targetBlock = this.blocks[layer0Index]
        logger.info(`  → 第2层方块${i}将遮挡第0层方块${layer0Index}`)
      }
      
      const fullPos = this.generateFullOverlapPosition(targetBlock)
      
      const x = fullPos.x
      const y = fullPos.y
      const type = allBlocks[blockIndex]
      const block = this.createBlock(type, x, y, 2) // Layer 2 for full overlap
      this.blocks.push(block)
      logger.info(`  生成完全遮挡方块 (idx ${blockIndex}, type ${type}, layer 2) at (${x.toFixed(1)}, ${y.toFixed(1)}) over targetBlock (idx ${this.blocks.indexOf(targetBlock)}, type ${targetBlock.type}, layer ${targetBlock.layer})`);
      blockIndex++
    }
    
    logger.info('=== 严格遮挡布局生成完成 ===')
  }

  /**
   * 生成完全不重叠的位置
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标  
   * @param {number} spacing - 间距
   * @param {number} count - 数量
   * @returns {Array} 位置数组
   */
  generateNonOverlapPositions(centerX, centerY, spacing, count) {
    const positions = []
    const cols = Math.ceil(Math.sqrt(count))
    const rows = Math.ceil(count / cols)
    
    // 计算初始网格起始位置
    let startX = centerX - (cols - 1) * spacing / 2
    let startY = centerY - (rows - 1) * spacing / 2
    
    // 边界检查：确保网格不会超出游戏区域
    const gridWidth = (cols - 1) * spacing + this.blockSize
    const gridHeight = (rows - 1) * spacing + this.blockSize
    
    // 调整网格起始位置，确保整个网格在边界内
    const minX = this.x
    const maxX = this.x + this.width - this.blockSize
    const minY = this.y  
    const maxY = this.y + this.height - this.blockSize
    
    // 如果网格太大，调整spacing
    if (gridWidth > this.width || gridHeight > this.height) {
      const newSpacingX = Math.max(this.blockSize, (this.width - this.blockSize) / (cols - 1))
      const newSpacingY = Math.max(this.blockSize, (this.height - this.blockSize) / (rows - 1))
      const newSpacing = Math.min(newSpacingX, newSpacingY, spacing)
      
      logger.warn(`    ⚠️ 原spacing ${spacing}太大，调整为 ${newSpacing.toFixed(1)}`)
      spacing = newSpacing
      
      // 重新计算起始位置
      startX = centerX - (cols - 1) * spacing / 2
      startY = centerY - (rows - 1) * spacing / 2
    }
    
    // 限制起始位置在边界内
    startX = Math.max(minX, Math.min(startX, maxX - (cols - 1) * spacing))
    startY = Math.max(minY, Math.min(startY, maxY - (rows - 1) * spacing))
    
    logger.info(`    → 网格布局: ${rows}行×${cols}列, 间距${spacing.toFixed(1)}, 起始位置(${startX.toFixed(1)}, ${startY.toFixed(1)})`)
    
    for (let i = 0; i < count; i++) {
      const row = Math.floor(i / cols)
      const col = i % cols
      
      let x = startX + col * spacing
      let y = startY + row * spacing
      
      // 最后的边界保护：强制限制在游戏区域内
      x = Math.max(minX, Math.min(x, maxX))
      y = Math.max(minY, Math.min(y, maxY))
      
      positions.push({ x, y })
    }
    
    return positions
  }

  /**
   * 生成1/4角部遮挡位置
   * @param {Block} baseBlock - 被遮挡的底层方块
   * @returns {Object} 位置坐标 {x, y}
   */
  generateQuarterOverlapPosition(baseBlock) {
    // 精确计算1/4遮挡位置，确保重叠面积在20%-30%之间
    const targetOverlapRatio = 0.25  // 目标25%重叠
    // 修正偏移计算：确保重叠区域约为目标25%
    const offset = this.blockSize * (1 - Math.sqrt(targetOverlapRatio))  // 计算偏移距离
    
    // 定义四个角的候选位置
    const corners = [
      { name: '左上角', offsetX: -offset, offsetY: -offset },
      { name: '右上角', offsetX: offset, offsetY: -offset },
      { name: '左下角', offsetX: -offset, offsetY: offset },
      { name: '右下角', offsetX: offset, offsetY: offset }
    ]
    
    // 筛选出不会超出边界的角落选项
    const validCorners = corners.filter(corner => {
      const x = baseBlock.x + corner.offsetX
      const y = baseBlock.y + corner.offsetY
      
      // 检查边界：左边界、右边界、上边界、下边界
      const withinLeftBound = x >= this.x
      const withinRightBound = x + this.blockSize <= this.x + this.width
      const withinTopBound = y >= this.y
      const withinBottomBound = y + this.blockSize <= this.y + this.height
      
      return withinLeftBound && withinRightBound && withinTopBound && withinBottomBound
    })
    
    // 如果没有有效的角落，降级为中心偏移生成
    let selectedCorner
    if (validCorners.length === 0) {
      logger.warn(`    ⚠️ 所有角落都会超出边界，使用安全偏移`)
      // 使用较小的偏移量，确保在边界内
      const safeOffset = Math.min(
        offset,
        (baseBlock.x - this.x),                          // 距离左边界的距离
        (this.x + this.width - baseBlock.x - this.blockSize),   // 距离右边界的距离
        (baseBlock.y - this.y),                          // 距离上边界的距离
        (this.y + this.height - baseBlock.y - this.blockSize)   // 距离下边界的距离
      ) * 0.5  // 使用一半作为安全余量
      
      selectedCorner = { name: '安全偏移', offsetX: safeOffset, offsetY: safeOffset }
    } else {
      // 从有效的角落中随机选择一个
      selectedCorner = validCorners[Math.floor(Math.random() * validCorners.length)]
    }
    
    const x = baseBlock.x + selectedCorner.offsetX
    const y = baseBlock.y + selectedCorner.offsetY
    
    // 最后的边界保护：强制限制在游戏区域内
    const clampedX = Math.max(this.x, Math.min(x, this.x + this.width - this.blockSize))
    const clampedY = Math.max(this.y, Math.min(y, this.y + this.height - this.blockSize))
    
    logger.info(`    → 在${selectedCorner.name}生成1/4遮挡，偏移(${selectedCorner.offsetX.toFixed(1)}, ${selectedCorner.offsetY.toFixed(1)})`)
    
    if (clampedX !== x || clampedY !== y) {
      logger.info(`    → 位置被调整: (${x.toFixed(1)}, ${y.toFixed(1)}) -> (${clampedX.toFixed(1)}, ${clampedY.toFixed(1)})`)
    }
    
    // 验证计算的重叠面积
    const tempBlock = { x: clampedX, y: clampedY, width: this.blockSize, height: this.blockSize }
    const actualOverlapArea = this.calculateOverlapArea(baseBlock, tempBlock)
    const actualOverlapRatio = actualOverlapArea / (baseBlock.width * baseBlock.height)
    logger.info(`    → 实际重叠比例: ${(actualOverlapRatio * 100).toFixed(1)}%`)
    
    return { x: clampedX, y: clampedY }
  }

  /**
   * 生成完全遮挡位置  
   * @param {Block} targetBlock - 被完全遮挡的方块
   * @returns {Object} 位置坐标 {x, y}
   */
  generateFullOverlapPosition(targetBlock) {
    // 完全遮挡：位置与目标方块完全一致，移除随机偏移
    const x = targetBlock.x;
    const y = targetBlock.y;
    
    logger.info(`    → 生成完全遮挡位置 for targetBlock (idx ${this.blocks.indexOf(targetBlock)}, type ${targetBlock.type}) at (${x.toFixed(1)}, ${y.toFixed(1)})`);
    
    return { x, y };
  }

  /**
   * 创建分散的布局区域 - 参考羊了个羊
   */
  createScatteredLayouts() {
    const centerX = this.width / 2
    const centerY = this.height / 2
    const spacing = this.blockSize * 0.9  // 方块间距
    
    return [
      // 第0层 - 外围分散区域
      {
        positions: [
          // 左上角区域
          { x: centerX - spacing * 3, y: centerY - spacing * 2.5 },
          { x: centerX - spacing * 2.5, y: centerY - spacing * 3 },
          { x: centerX - spacing * 4, y: centerY - spacing * 1.5 },
          
          // 右上角区域
          { x: centerX + spacing * 3, y: centerY - spacing * 2.5 },
          { x: centerX + spacing * 2.5, y: centerY - spacing * 3 },
          { x: centerX + spacing * 4, y: centerY - spacing * 1.5 },
          
          // 左下角区域
          { x: centerX - spacing * 3, y: centerY + spacing * 2.5 },
          { x: centerX - spacing * 2.5, y: centerY + spacing * 3 },
          { x: centerX - spacing * 4, y: centerY + spacing * 1.5 },
          
          // 右下角区域
          { x: centerX + spacing * 3, y: centerY + spacing * 2.5 },
          { x: centerX + spacing * 2.5, y: centerY + spacing * 3 },
          { x: centerX + spacing * 4, y: centerY + spacing * 1.5 }
        ]
      },
      
      // 第1层 - 中间环形区域
      {
        positions: [
          { x: centerX - spacing * 2, y: centerY - spacing },
          { x: centerX - spacing, y: centerY - spacing * 2 },
          { x: centerX, y: centerY - spacing * 2 },
          { x: centerX + spacing, y: centerY - spacing * 2 },
          { x: centerX + spacing * 2, y: centerY - spacing },
          { x: centerX + spacing * 2, y: centerY },
          { x: centerX + spacing * 2, y: centerY + spacing },
          { x: centerX + spacing, y: centerY + spacing * 2 },
          { x: centerX, y: centerY + spacing * 2 },
          { x: centerX - spacing, y: centerY + spacing * 2 },
          { x: centerX - spacing * 2, y: centerY + spacing },
          { x: centerX - spacing * 2, y: centerY }
        ]
      },
      
      // 第2层 - 中心区域
      {
        positions: [
          { x: centerX - spacing * 0.7, y: centerY - spacing * 0.7 },
          { x: centerX + spacing * 0.7, y: centerY - spacing * 0.7 },
          { x: centerX, y: centerY },
          { x: centerX - spacing * 0.7, y: centerY + spacing * 0.7 },
          { x: centerX + spacing * 0.7, y: centerY + spacing * 0.7 },
          { x: centerX, y: centerY - spacing * 0.5 }
        ]
      }
    ]
  }

  /**
   * 打乱数组顺序
   * @param {Array} array - 要打乱的数组
   */
  shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]]
    }
  }

  /**
   * 验证方块数量是否为3的倍数
   */
  validateBlockCounts() {
    const typeCounts = new Map()
    
    // 统计每种类型的方块数量
    this.blocks.forEach(block => {
      const count = typeCounts.get(block.type) || 0
      typeCounts.set(block.type, count + 1)
    })
    
    logger.info(`=== 方块数量验证 ===`)
    logger.info(`总方块数: ${this.blocks.length}`)
    logger.info(`方块类型数: ${typeCounts.size}`)
    
    // 检查是否都是3的倍数
    let isValid = true
    let totalGroups = 0
    
    typeCounts.forEach((count, type) => {
      const groups = Math.floor(count / 3)
      const remainder = count % 3
      totalGroups += groups
      
      if (remainder !== 0) {
        logger.error(`❌ 方块类型 ${type}: ${count} 个 (${groups} 组 + ${remainder} 个剩余) - 不是3的倍数！`)
        isValid = false
      } else {
        logger.info(`✓ 方块类型 ${type}: ${count} 个 (${groups} 组)`)
      }
    })
    
    logger.info(`总可消除组数: ${totalGroups}`)
    
    if (isValid) {
      logger.info('🎉 所有方块类型数量都是3的倍数！游戏可以完全消除。')
    } else {
      logger.warn('⚠️ 存在方块数量不是3的倍数的情况！可能无法完全消除。')
    }
    
    logger.info(`===============`)
    
    return isValid
  }

  /**
   * 生成第二关布局 - 更多方块的分散布局
   */
  generateLevel2() {
    logger.info(`第二关布局: 游戏板区域 ${this.x}, ${this.y}, ${this.width}x${this.height}`)
    
    // 增加方块类型数量，确保数量是3的倍数
    const blockTypes = [0, 1, 2, 3, 4, 5]
    const blocksPerType = 9  // 每种类型9个方块(3的倍数)
    
    // 创建方块数组
    const allBlocks = []
    blockTypes.forEach(type => {
      for (let i = 0; i < blocksPerType; i++) {
        allBlocks.push(type)
      }
    })
    
    // 打乱方块顺序
    this.shuffleArray(allBlocks)
    
    // 创建更复杂的分散布局
    const layouts = this.createLevel2ScatteredLayouts()
    
    let blockIndex = 0
    
    // 在不同区域分散放置方块
    layouts.forEach((layout, layerIndex) => {
      layout.positions.forEach(pos => {
        if (blockIndex < allBlocks.length) {
          // 调整位置计算，确保方块在游戏板范围内
          let x = this.x + pos.x - this.blockSize / 2
          let y = this.y + pos.y - this.blockSize / 2
          
          // 如果位置超出范围，调整到范围内
          x = Math.max(this.x, Math.min(x, this.x + this.width - this.blockSize))
          y = Math.max(this.y, Math.min(y, this.y + this.height - this.blockSize))
          
          const type = allBlocks[blockIndex]
          const block = this.createBlock(type, x, y, layerIndex)
          this.blocks.push(block)
          logger.info(`创建第二关方块 ${blockIndex}: 类型${type}, 位置(${x.toFixed(1)}, ${y.toFixed(1)}), 层级${layerIndex}`)
          blockIndex++
        }
      })
    })
    
    // 如果还有方块没有放置，强制放置在安全区域
    this.forceCreateRemainingBlocks(allBlocks, blockIndex)
    
    logger.info(`第二关共创建 ${this.blocks.length} 个方块`)
    this.validateBlockCounts()
  }

  /**
   * 生成第三关布局 - 最复杂的分散布局
   */
  generateLevel3() {
    logger.info(`第三关布局: 游戏板区域 ${this.x}, ${this.y}, ${this.width}x${this.height}`)
    
    // 更多方块类型，确保数量是3的倍数
    const blockTypes = [0, 1, 2, 3, 4, 5, 6, 7]
    const blocksPerType = 12  // 每种类型12个方块(3的倍数)
    const totalBlocks = blockTypes.length * blocksPerType
    
    // 动态计算方块大小
    this.calculateDynamicBlockSize(3, totalBlocks)
    
    logger.info(`第三关预定: ${blockTypes.length}种类型 × ${blocksPerType}个 = ${totalBlocks}个方块`)
    
    // 创建方块数组
    const allBlocks = []
    blockTypes.forEach(type => {
      for (let i = 0; i < blocksPerType; i++) {
        allBlocks.push(type)
      }
    })
    
    // 打乱方块顺序
    this.shuffleArray(allBlocks)
    
    // 创建最复杂的分散布局
    const layouts = this.createLevel3ScatteredLayouts()
    
    // 验证布局位置数量
    let totalPositions = 0
    layouts.forEach((layout, layerIndex) => {
      logger.info(`第三关第${layerIndex}层: ${layout.positions.length}个位置`)
      totalPositions += layout.positions.length
    })
    
    logger.info(`第三关总位置数: ${totalPositions}, 需要: ${totalBlocks}`)
    
    if (totalPositions < totalBlocks) {
      logger.error(`❌ 位置不足！总位置${totalPositions} < 需要${totalBlocks}`)
    } else {
      logger.info(`✓ 位置充足！总位置${totalPositions} >= 需要${totalBlocks}`)
    }
    
    let blockIndex = 0
    
    // 在不同区域分散放置方块
    layouts.forEach((layout, layerIndex) => {
      layout.positions.forEach(pos => {
        if (blockIndex < allBlocks.length) {
          // 调整位置计算，确保方块在游戏板范围内
          let x = this.x + pos.x - this.blockSize / 2
          let y = this.y + pos.y - this.blockSize / 2
          
          // 如果位置超出范围，调整到范围内
          x = Math.max(this.x, Math.min(x, this.x + this.width - this.blockSize))
          y = Math.max(this.y, Math.min(y, this.y + this.height - this.blockSize))
          
          const type = allBlocks[blockIndex]
          const block = this.createBlock(type, x, y, layerIndex)
          this.blocks.push(block)
          logger.info(`创建第三关方块 ${blockIndex}: 类型${type}, 位置(${x.toFixed(1)}, ${y.toFixed(1)}), 层级${layerIndex}`)
          blockIndex++
        }
      })
    })
    
    // 如果还有方块没有放置，强制放置在安全区域
    if (blockIndex < allBlocks.length) {
      logger.warn(`⚠️ 还有${allBlocks.length - blockIndex}个方块需要强制放置`)
      this.forceCreateRemainingBlocks(allBlocks, blockIndex)
    }
    
    logger.info(`第三关共创建 ${this.blocks.length} 个方块`)
    this.validateBlockCounts()
  }

  /**
   * 创建第二关的分散布局
   */
  createLevel2ScatteredLayouts() {
    const centerX = this.width / 2
    const centerY = this.height / 2
    const spacing = this.blockSize * 0.85
    
    return [
      // 第0层 - 最外围
      {
        positions: [
          // 顶部横排
          { x: centerX - spacing * 2.5, y: centerY - spacing * 3.5 },
          { x: centerX - spacing * 1.5, y: centerY - spacing * 3.5 },
          { x: centerX - spacing * 0.5, y: centerY - spacing * 3.5 },
          { x: centerX + spacing * 0.5, y: centerY - spacing * 3.5 },
          { x: centerX + spacing * 1.5, y: centerY - spacing * 3.5 },
          { x: centerX + spacing * 2.5, y: centerY - spacing * 3.5 },
          
          // 底部横排
          { x: centerX - spacing * 2.5, y: centerY + spacing * 3.5 },
          { x: centerX - spacing * 1.5, y: centerY + spacing * 3.5 },
          { x: centerX - spacing * 0.5, y: centerY + spacing * 3.5 },
          { x: centerX + spacing * 0.5, y: centerY + spacing * 3.5 },
          { x: centerX + spacing * 1.5, y: centerY + spacing * 3.5 },
          { x: centerX + spacing * 2.5, y: centerY + spacing * 3.5 },
          
          // 左侧竖排
          { x: centerX - spacing * 3.5, y: centerY - spacing * 2 },
          { x: centerX - spacing * 3.5, y: centerY - spacing },
          { x: centerX - spacing * 3.5, y: centerY },
          { x: centerX - spacing * 3.5, y: centerY + spacing },
          { x: centerX - spacing * 3.5, y: centerY + spacing * 2 },
          
          // 右侧竖排
          { x: centerX + spacing * 3.5, y: centerY - spacing * 2 },
          { x: centerX + spacing * 3.5, y: centerY - spacing },
          { x: centerX + spacing * 3.5, y: centerY },
          { x: centerX + spacing * 3.5, y: centerY + spacing },
          { x: centerX + spacing * 3.5, y: centerY + spacing * 2 }
        ]
      },
      
      // 第1层 - 中间层
      {
        positions: [
          { x: centerX - spacing * 2, y: centerY - spacing * 2 },
          { x: centerX - spacing, y: centerY - spacing * 2.5 },
          { x: centerX, y: centerY - spacing * 2.5 },
          { x: centerX + spacing, y: centerY - spacing * 2.5 },
          { x: centerX + spacing * 2, y: centerY - spacing * 2 },
          { x: centerX + spacing * 2.5, y: centerY - spacing },
          { x: centerX + spacing * 2.5, y: centerY },
          { x: centerX + spacing * 2.5, y: centerY + spacing },
          { x: centerX + spacing * 2, y: centerY + spacing * 2 },
          { x: centerX + spacing, y: centerY + spacing * 2.5 },
          { x: centerX, y: centerY + spacing * 2.5 },
          { x: centerX - spacing, y: centerY + spacing * 2.5 },
          { x: centerX - spacing * 2, y: centerY + spacing * 2 },
          { x: centerX - spacing * 2.5, y: centerY + spacing },
          { x: centerX - spacing * 2.5, y: centerY }
        ]
      },
      
      // 第2层 - 内层
      {
        positions: [
          { x: centerX - spacing * 1.5, y: centerY - spacing * 1.5 },
          { x: centerX - spacing * 0.5, y: centerY - spacing * 1.8 },
          { x: centerX + spacing * 0.5, y: centerY - spacing * 1.8 },
          { x: centerX + spacing * 1.5, y: centerY - spacing * 1.5 },
          { x: centerX + spacing * 1.8, y: centerY - spacing * 0.5 },
          { x: centerX + spacing * 1.8, y: centerY + spacing * 0.5 },
          { x: centerX + spacing * 1.5, y: centerY + spacing * 1.5 },
          { x: centerX + spacing * 0.5, y: centerY + spacing * 1.8 },
          { x: centerX - spacing * 0.5, y: centerY + spacing * 1.8 },
          { x: centerX - spacing * 1.5, y: centerY + spacing * 1.5 },
          { x: centerX - spacing * 1.8, y: centerY + spacing * 0.5 },
          { x: centerX - spacing * 1.8, y: centerY - spacing * 0.5 }
        ]
      },
      
      // 第3层 - 中心
      {
        positions: [
          { x: centerX, y: centerY },
          { x: centerX - spacing * 0.8, y: centerY },
          { x: centerX + spacing * 0.8, y: centerY },
          { x: centerX, y: centerY - spacing * 0.8 },
          { x: centerX, y: centerY + spacing * 0.8 },
          { x: centerX - spacing * 0.5, y: centerY - spacing * 0.5 }
        ]
      }
    ]
  }

  /**
   * 创建第三关的分散布局 - 修复：确保总位置数 = 96个
   */
  createLevel3ScatteredLayouts() {
    const centerX = this.width / 2
    const centerY = this.height / 2
    const spacing = this.blockSize * 0.8
    
    // 需要总共96个位置：5×6 + 4×5 + 3×4 + 2×3 + 中心部分
    // 计算：30 + 20 + 12 + 6 + 28 = 96
    
    return [
      // 第0层 - 最外围：5×6=30个
      {
        positions: this.generateGridPositions(centerX, centerY, spacing, 5, 6, -4)
      },
      
      // 第1层 - 外层：4×5=20个
      {
        positions: this.generateGridPositions(centerX, centerY, spacing, 4, 5, -3)
      },
      
      // 第2层 - 中层：3×4=12个
      {
        positions: this.generateGridPositions(centerX, centerY, spacing, 3, 4, -2.5)
      },
      
      // 第3层 - 内层：2×3=6个
      {
        positions: this.generateGridPositions(centerX, centerY, spacing, 2, 3, -1.5)
      },
      
      // 第4层 - 中心：7×4=28个，补足到96个
      {
        positions: this.generateGridPositions(centerX, centerY, spacing * 0.6, 7, 4, -1)
      }
    ]
  }

  /**
   * 生成网格状位置（用于第三关）- 修复：移除随机跳过逻辑，确保位置数量确定
   */
  generateGridPositions(centerX, centerY, spacing, rows, cols, startOffset) {
    const positions = []
    const startX = centerX + startOffset * spacing
    const startY = centerY + startOffset * spacing
    
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        // 修复：移除随机跳过，确保所有位置都被使用
        positions.push({
          x: startX + col * spacing,
          y: startY + row * spacing
        })
      }
    }
    
    logger.info(`生成网格位置: ${rows}x${cols} = ${positions.length}个位置`)
    return positions
  }

  /**
   * 生成随机关卡
   * @param {number} level - 关卡等级
   */
  generateRandomLevel(level) {
    logger.info(`随机关卡 ${level}: 游戏板区域 ${this.x}, ${this.y}, ${this.width}x${this.height}`)
    
    // 随机关卡也保持固定方块大小
    this.blockSize = this.baseBlockSize
    logger.info(`🎯 随机关卡${level}使用固定方块大小: ${this.blockSize}像素`)
    
    // 根据关卡等级决定方块类型数量，确保数量是3的倍数
    const typeCount = Math.min(10, Math.max(4, level + 2))
    const blockTypes = Array.from({length: typeCount}, (_, i) => i)
    
    // 修复：确保blocksPerType是3的倍数
    let baseBlocks = Math.max(2, Math.floor(level * 1.5) + 1)  // 基础组数
    const blocksPerType = baseBlocks * 3  // 确保是3的倍数
    
    logger.info(`随机关卡 ${level}: ${typeCount}种类型, 每种${blocksPerType}个(${baseBlocks}组)`)
    
    // 创建方块数组，确保每种类型都是3的倍数
    const allBlocks = []
    blockTypes.forEach(type => {
      for (let i = 0; i < blocksPerType; i++) {
        allBlocks.push(type)
      }
    })
    
    // 打乱方块顺序
    this.shuffleArray(allBlocks)
    
    // 生成随机分散布局
    const layouts = this.createRandomScatteredLayouts(level, allBlocks.length)
    
    let blockIndex = 0
    
    // 在不同区域分散放置方块 - 改进：确保所有方块都能被放置
    layouts.forEach((layout, layerIndex) => {
      layout.positions.forEach(pos => {
        if (blockIndex < allBlocks.length) {
          // 调整位置计算，确保方块在游戏板范围内
          let x = this.x + pos.x - this.blockSize / 2
          let y = this.y + pos.y - this.blockSize / 2
          
          // 如果位置超出范围，调整到范围内
          x = Math.max(this.x, Math.min(x, this.x + this.width - this.blockSize))
          y = Math.max(this.y, Math.min(y, this.y + this.height - this.blockSize))
          
          const type = allBlocks[blockIndex]
          const block = this.createBlock(type, x, y, layerIndex)
          this.blocks.push(block)
          logger.info(`创建随机方块 ${blockIndex}: 类型${type}, 位置(${x.toFixed(1)}, ${y.toFixed(1)}), 层级${layerIndex}`)
          blockIndex++
        }
      })
    })
    
    // 如果还有方块没有放置，强制放置在安全区域
    this.forceCreateRemainingBlocks(allBlocks, blockIndex)
    
    logger.info(`随机关卡 ${level} 共创建 ${this.blocks.length} 个方块`)
    this.validateBlockCounts()
  }

  /**
   * 创建随机分散布局
   * @param {number} level - 关卡等级
   * @param {number} totalBlocks - 总方块数量
   */
  createRandomScatteredLayouts(level, totalBlocks) {
    const centerX = this.width / 2
    const centerY = this.height / 2
    const spacing = this.blockSize * 0.8
    const layerCount = Math.min(6, Math.floor(level / 2) + 3)
    
    const layouts = []
    let blocksPlaced = 0
    
    for (let layer = 0; layer < layerCount && blocksPlaced < totalBlocks; layer++) {
      const positions = []
      const radius = (layerCount - layer) * spacing * 0.8
      const blocksInLayer = Math.min(
        totalBlocks - blocksPlaced,
        Math.max(6, Math.floor((layerCount - layer) * level * 2))
      )
      
      // 在每一层创建分散的位置
      for (let i = 0; i < blocksInLayer; i++) {
        const angle = (i / blocksInLayer) * Math.PI * 2 + Math.random() * 0.5
        const r = radius * (0.7 + Math.random() * 0.6)  // 添加随机性
        const x = centerX + Math.cos(angle) * r + (Math.random() - 0.5) * spacing * 0.5
        const y = centerY + Math.sin(angle) * r + (Math.random() - 0.5) * spacing * 0.5
        
        positions.push({ x, y })
      }
      
      layouts.push({ positions })
      blocksPlaced += blocksInLayer
    }
    
    return layouts
  }

  /**
   * 更新层级映射
   */
  updateLayers() {
    this.layers.clear()
    
    this.blocks.forEach(block => {
      if (!this.layers.has(block.layer)) {
        this.layers.set(block.layer, [])
      }
      this.layers.get(block.layer).push(block)
    })
  }

  /**
   * 获取当前可操作的层级
   * @returns {number} 当前活跃层级
   */
  getCurrentActiveLayer() {
    return this.currentActiveLayer
  }

  /**
   * 检查指定层级是否已完全清空
   * @param {number} layer - 要检查的层级
   * @returns {boolean}
   */
  isLayerComplete(layer) {
    const layerBlocks = this.layers.get(layer)
    return !layerBlocks || layerBlocks.length === 0
  }

  /**
   * 激活下一层（当前层清空后调用）
   * @returns {boolean} 是否成功激活下一层
   */
  activateNextLayer() {
    if (this.currentActiveLayer === this.LAYER_TOP && this.isLayerComplete(this.LAYER_TOP)) {
      this.currentActiveLayer = this.LAYER_MIDDLE
      logger.info('🎉 上层清空！激活中层')
      this.updateLayerStates()
      return true
    } else if (this.currentActiveLayer === this.LAYER_MIDDLE && this.isLayerComplete(this.LAYER_MIDDLE)) {
      this.currentActiveLayer = this.LAYER_BOTTOM
      logger.info('🎉 中层清空！激活下层')
      this.updateLayerStates()
      return true
    }
    return false
  }

  /**
   * 更新所有方块的层级状态（可点击性和视觉效果）
   */
  updateLayerStates() {
    // 优化：传递索引和数组给 isBlockCovered
    this.blocks.forEach((block, index) => {
      block.isClickable = !this.isBlockCovered(block, index, this.blocks)
    })
  }

  /**
   * 更新方块的可点击状态（羊了个羊模式）
   */
  updateClickableStates() {
    // 先更新层级映射
    this.updateLayers()
    
    // 使用新的遮挡检测系统
    this.updateLayerStates()
    
    // 统计状态
    let clickableCount = 0
    let blockedCount = 0
    let layerStats = new Map() // 按层级统计
    
    this.blocks.forEach(block => {
      if (!layerStats.has(block.layer)) {
        layerStats.set(block.layer, { total: 0, clickable: 0, blocked: 0 })
      }
      const stats = layerStats.get(block.layer)
      stats.total++
      
      if (block.isClickable) {
        clickableCount++
        stats.clickable++
      } else {
        blockedCount++
        stats.blocked++
      }
    })
    
    // 第七关特别调试信息
    if (this.currentLevel === 7) {
      layerStats.forEach((stats, layer) => {
        const clickablePercent = (stats.clickable / stats.total * 100).toFixed(1)
      })
      
      // 如果可点击方块太少，输出详细的遮挡信息
      if (clickableCount < 20) {
        logger.warn(`⚠️ 第七关可点击方块太少(${clickableCount}个)，详细分析前10个被遮挡的方块:`)
        let count = 0
        for (let block of this.blocks) {
          if (!block.isClickable && count < 10) {
            // 找出遮挡它的方块
            for (let other of this.blocks) {
              if (other !== block && other.isVisible && this.blocksOverlap(block, other)) {
                const overlapArea = this.calculateOverlapArea(block, other)
                const overlapRatio = overlapArea / (block.width * block.height)
                const isOnTop = other.layer > block.layer || (other.layer === block.layer && this.blocks.indexOf(other) > this.blocks.indexOf(block))
              }
            }
            count++
          }
        }
      }
    }
    
    // 第5-8关调试信息（修复后的验证）
    if (this.currentLevel >= 5 && this.currentLevel <= 8) {
      layerStats.forEach((stats, layer) => {
        const clickablePercent = (stats.clickable / stats.total * 100).toFixed(1)
      })
      
      // 统计可点击率是否正常
      const totalClickablePercent = (clickableCount / this.blocks.length * 100).toFixed(1)
      if (totalClickablePercent > 80) {
        logger.warn(`⚠️ 第${this.currentLevel}关可点击率过高(${totalClickablePercent}%)，可能仍有bug需要修复`)
      } else {
      }
    }
  }

  /**
   * 检查两个方块是否重叠
   * @param {Block} block1 - 方块1
   * @param {Block} block2 - 方块2
   * @returns {boolean}
   */
  blocksOverlap(block1, block2) {
    // 增加一个小的容错范围，确保边缘情况也能正确判断
    const tolerance = 1 // 1像素的容错
    
    const overlap = !(block1.x + block1.width <= block2.x + tolerance ||
                     block2.x + block2.width <= block1.x + tolerance ||
                     block1.y + block1.height <= block2.y + tolerance ||
                     block2.y + block2.height <= block1.y + tolerance)
    
    return overlap
  }

  /**
   * 检查方块是否被上层方块遮挡（羊了个羊模式）
   * 只有两种情况才视为遮挡：全遮挡 或 角部1/4遮挡
   * @param {Block} block - 要检查的方块
   * @param {number} blockIndexInArray - 方块在数组中的索引 (用于优化)
   * @param {Array<Block>} allBlocksArray - 所有方块的数组 (用于优化)
   * @returns {boolean}
   */
  isBlockCovered(block, blockIndexInArray, allBlocksArray) {
    // 对于第5关及以后，使用简化的遮挡检测
    if (this.currentLevel && this.currentLevel >= 5) {
      return this.isBlockCoveredSimple(block, blockIndexInArray, allBlocksArray)
    }
    
    // 前4关使用复杂的羊了个羊遮挡检测
    for (let i = 0; i < allBlocksArray.length; i++) {
      const other = allBlocksArray[i];
      if (other === block || !other.isVisible) continue;

      // Determine if 'other' is visually on top of 'block'
      const isOtherHigherLayer = other.layer > block.layer;
      // For same layer, a block with a higher index is considered "on top" due to rendering order.
      // However, for click detection, same-layer blocks typically don't cover each other unless specifically designed to.
      // Let's stick to the original logic for same layer: higher index covers.
      const isOtherSameLayerAndRenderedLater = (other.layer === block.layer && i > blockIndexInArray);

      if (!isOtherHigherLayer && !isOtherSameLayerAndRenderedLater) continue;
      
      // Basic overlap check (bounding box)
      if (this.blocksOverlap(block, other)) {
        // If 'other' is on top and overlaps 'block', then 'block' is covered.
        // logger.info(`DEBUG: Block (type ${block.type}, layer ${block.layer}, idx ${blockIndexInArray}) covered by Other (type ${other.type}, layer ${other.layer}, idx ${i}) - direct overlap.`);
        return true; 
      }
    }
    
    // logger.info(`DEBUG: Block (type ${block.type}, layer ${block.layer}, idx ${blockIndexInArray}) NOT covered.`);
    return false;
  }

  /**
   * 简化的方块遮挡检测（用于第5关及以后）
   * 修复bug：任何重叠且上方有牌的情况都应被视为遮挡
   * @param {Block} block - 要检查的方块
   * @param {number} blockIndexInArray - 方块在数组中的索引
   * @param {Array<Block>} allBlocksArray - 所有方块的数组
   * @returns {boolean}
   */
  isBlockCoveredSimple(block, blockIndexInArray, allBlocksArray) {
    // 对于第5关及以后，采用和前4关相同的严格遮挡检测：
    // 只要有重叠且上方有牌，就认为被遮挡
    
    for (let i = 0; i < allBlocksArray.length; i++) {
      const other = allBlocksArray[i];
      if (other === block || !other.isVisible) continue;

      // 检查是否有重叠
      if (this.blocksOverlap(block, other)) {
        // 检查 other 是否在视觉上"遮挡"了 block
        const isOtherHigherLayer = other.layer > block.layer;
        const isOtherSameLayerAndRenderedLater = (other.layer === block.layer && i > blockIndexInArray);
        
        if (isOtherHigherLayer || isOtherSameLayerAndRenderedLater) {
          return true;
        }
      }
    }
    
    return false;
  }

  /**
   * 检查两个方块的重叠是否构成有效遮挡（羊了个羊简化版）
   * 只有三种情况：无遮挡、全遮挡、1/4遮挡
   * @param {Block} bottomBlock - 下层方块（被遮挡的方块）
   * @param {Block} topBlock - 上层方块（遮挡的方块）
   * @param {boolean} debug - 是否输出调试信息
   * @returns {boolean}
   */
  isValidOverlap(bottomBlock, topBlock, debug = false) {
    // 任何非角部的重叠均视为遮挡
    const overlapArea = this.calculateOverlapArea(bottomBlock, topBlock)
    // 无重叠视为无遮挡
    if (overlapArea <= 0) {
      return false
    }
    // 角部小重叠（允许peek）视为无遮挡
    if (this.isCornerOverlap(bottomBlock, topBlock, overlapArea, debug)) {
      return false
    }
    // 其他所有重叠均视为有效遮挡
    return true
  }

  /**
   * 检查重叠区域是否位于下层方块的角部
   * @param {Block} bottomBlock - 下层方块
   * @param {Block} topBlock - 上层方块  
   * @param {number} overlapArea - 重叠面积
   * @param {boolean} debug - 是否输出调试信息
   * @returns {boolean}
   */
  isCornerOverlap(bottomBlock, topBlock, overlapArea, debug = false) { // overlapArea is total overlap between bottomBlock & topBlock
    // 条件: topBlock的中心必须非常靠近bottomBlock的一个角点。
    // overlapRatio (20-30%) 已经在调用者 isValidOverlap 中检查过了。

    const topCenterX = topBlock.x + topBlock.width / 2;
    const topCenterY = topBlock.y + topBlock.height / 2;

    const bottomCorners = [
      { x: bottomBlock.x, y: bottomBlock.y, name: '左上角' },
      { x: bottomBlock.x + bottomBlock.width, y: bottomBlock.y, name: '右上角' },
      { x: bottomBlock.x, y: bottomBlock.y + bottomBlock.height, name: '左下角' },
      { x: bottomBlock.x + bottomBlock.width, y: bottomBlock.y + bottomBlock.height, name: '右下角' }
    ];

    // 允许的误差范围，例如方块宽度的10%
    const tolerance = topBlock.width * 0.15; // 稍微增加容差以提高鲁棒性

    for (const corner of bottomCorners) {
      const dist = Math.sqrt(Math.pow(topCenterX - corner.x, 2) + Math.pow(topCenterY - corner.y, 2));
      if (dist < tolerance) {
        if (debug) logger.info(`    → 1/4角部: topBlock center (${topCenterX.toFixed(1)},${topCenterY.toFixed(1)}) near bottomBlock corner ${corner.name} (${corner.x.toFixed(1)},${corner.y.toFixed(1)}). Dist: ${dist.toFixed(1)}, Tol: ${tolerance.toFixed(1)}`);
        return true;
      }
    }

    if (debug) logger.info(`    → 1/4角部: topBlock center (${topCenterX.toFixed(1)},${topCenterY.toFixed(1)}) not close enough to any bottomBlock corner. Min tolerance: ${tolerance.toFixed(1)}`);
    return false;
  }

  /**
   * 计算两个方块的重叠面积
   * @param {Block} block1 - 方块1
   * @param {Block} block2 - 方块2
   * @returns {number} 重叠面积
   */
  calculateOverlapArea(block1, block2) {
    const left = Math.max(block1.x, block2.x)
    const right = Math.min(block1.x + block1.width, block2.x + block2.width)
    const top = Math.max(block1.y, block2.y)
    const bottom = Math.min(block1.y + block1.height, block2.y + block2.height)
    
    if (left < right && top < bottom) {
      return (right - left) * (bottom - top)
    }
    return 0
  }

  /**
   * 获取点击位置的最上层方块（改进版）
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {Block|null}
   */
  getClickedBlock(x, y) {
    let candidateBlocks = []

    // 找出所有包含点击位置的方块（包括被遮挡的）
    this.blocks.forEach(block => {
      if (block.isVisible && block.containsPoint(x, y)) {
        candidateBlocks.push(block)
      }
    })

    if (candidateBlocks.length === 0) {
      return null
    }

    // 按层级排序，然后在同层级中选择最靠近点击点中心的方块
    candidateBlocks.sort((a, b) => {
      if (a.layer !== b.layer) {
        return b.layer - a.layer // 高层级优先
      }
      // 同层级，比较与点击点的距离
      const distA = Math.sqrt(Math.pow(a.x + a.width / 2 - x, 2) + Math.pow(a.y + a.height / 2 - y, 2))
      const distB = Math.sqrt(Math.pow(b.x + b.width / 2 - x, 2) + Math.pow(b.y + b.height / 2 - y, 2))
      if (Math.abs(distA - distB) > 0.1) { // 避免浮点数精度问题，增加一个小的阈值
        return distA - distB // 距离近的优先
      }
      // 如果距离也几乎相同，比较原始索引 (this.blocks中的索引，越大表示越后绘制，越可能在上面)
      return this.blocks.indexOf(b) - this.blocks.indexOf(a); // 索引大的优先（更"上层"）
    })

    // 从排序后的候选方块中，选出第一个可点击的方块
    const clickedBlock = candidateBlocks.find(block => block.isClickable);

    if (clickedBlock) {
      return clickedBlock;
    } else {
      return null;
    }
  }

  /**
   * 移除方块（羊了个羊模式）
   * @param {Block} block - 要移除的方块
   */
  removeBlock(block) {
    // 从blocks数组中移除
    const index = this.blocks.indexOf(block)
    if (index > -1) {
      this.blocks.splice(index, 1)
    }

    // 从layers映射中移除
    if (this.layers.has(block.layer)) {
      const layerBlocks = this.layers.get(block.layer)
      const layerIndex = layerBlocks.indexOf(block)
      if (layerIndex > -1) {
        layerBlocks.splice(layerIndex, 1)
      }
    }

    // 重新更新可点击状态（移除方块后，之前被遮挡的方块可能变为可点击）
    this.updateClickableStates()
  }

  /**
   * 检查游戏是否胜利（所有方块都被移除）
   * @returns {boolean}
   */
  isWin() {
    return this.blocks.filter(block => block.isVisible).length === 0
  }

  /**
   * 检查是否还有可点击的方块
   * @returns {boolean}
   */
  hasClickableBlocks() {
    return this.blocks.some(block => block.isVisible && block.isClickable)
  }

  /**
   * 洗牌功能 - 重新排列方块位置
   * @param {AnimationManager} animationManager - 动画管理器
   */
  shuffle(animationManager = null) {
    logger.info('=== 开始洗牌 ===')
    
    // 获取所有可见方块的类型和位置
    const visibleBlocks = this.blocks.filter(block => block.isVisible)
    logger.info(`洗牌前可见方块数: ${visibleBlocks.length}`)
    
    // 统计洗牌前的方块类型分布
    const typeCounts = new Map()
    visibleBlocks.forEach(block => {
      const count = typeCounts.get(block.type) || 0
      typeCounts.set(block.type, count + 1)
    })
    
    logger.info(`洗牌前方块类型分布:`)
    typeCounts.forEach((count, type) => {
      logger.info(`  类型${type}: ${count}个`)
    })
    
    const positions = visibleBlocks.map(block => ({x: block.x, y: block.y, layer: block.layer}))
    const types = visibleBlocks.map(block => block.type)
    
    // 打乱类型数组
    const shuffledTypes = Utils.shuffle(types)
    
    // 重新分配类型
    visibleBlocks.forEach((block, index) => {
      block.type = shuffledTypes[index]
      // 设置目标位置（保持原位置，但类型已改变）
      block.targetX = block.x
      block.targetY = block.y
    })
    
    logger.info(`洗牌完成，重新分配了${visibleBlocks.length}个方块的类型`)
    
    // 即时更新可点击状态，确保动画过程中状态与视觉同步
    logger.info('🔄 【即时】重新计算可点击状态')
    this.updateClickableStates()
    
    // 如果有动画管理器，播放洗牌动画
    if (animationManager) {
      // 传递游戏板边界信息给动画管理器
      const bounds = this.getBounds()
      animationManager.animateShuffle(visibleBlocks, () => {
        logger.info('🔄 洗牌动画完成，重新计算可点击状态')
        this.updateClickableStates() // <--- 添加调用
      }, bounds)
    } else {
      // 没有动画管理器时，直接更新可点击状态
      logger.info('🔄 洗牌完成，重新计算可点击状态')
      this.updateClickableStates() // <--- 添加调用
    }
    
    logger.info('==============_')
  }

  /**
   * 清空游戏板
   */
  clear() {
    this.blocks = []
    this.layers.clear()
  }

  /**
   * 更新游戏板状态
   * @param {number} deltaTime - 时间差
   */
  update(deltaTime) {
    this.blocks.forEach(block => {
      block.update(deltaTime)
    })
  }

  /**
   * 渲染游戏板
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {ResourceLoader} resources - 资源管理器
   */
  render(ctx, resources) {
    ctx.save()

    // 按层级从低到高渲染方块
    const sortedLayers = Array.from(this.layers.keys()).sort((a, b) => a - b)
    
    sortedLayers.forEach(layerIndex => {
      const layerBlocks = this.layers.get(layerIndex)
      
      // 为每一层添加轻微的视觉偏移，营造层次感
      ctx.save()
      ctx.translate(layerIndex * 2, layerIndex * 2)
      
      layerBlocks.forEach(block => {
        if (block.isVisible) {
          block.render(ctx, resources)
        }
      })
      
      ctx.restore()
    })

    ctx.restore()
  }

  /**
   * 获取游戏板的边界框
   * @returns {Object} {x, y, width, height}
   */
  getBounds() {
    return {
      x: this.x,
      y: this.y,
      width: this.width,
      height: this.height
    }
  }

  /**
   * 获取剩余方块数量
   * @returns {number}
   */
  getRemainingBlockCount() {
    return this.blocks.filter(block => block.isVisible).length
  }

  /**
   * 获取可点击方块数量
   * @returns {number}
   */
  getClickableBlockCount() {
    return this.blocks.filter(block => block.isVisible && block.isClickable).length
  }

  /**
   * 如果还有方块没有放置，强制放置在安全区域
   * @param {Array} allBlocks - 所有方块类型数组
   * @param {number} blockIndex - 当前放置的方块索引
   */
  forceCreateRemainingBlocks(allBlocks, blockIndex) {
    if (blockIndex < allBlocks.length) {
      logger.warn(`还有 ${allBlocks.length - blockIndex} 个方块未放置，强制放置...`)
      
      const safeAreaWidth = this.width - this.blockSize * 2
      const safeAreaHeight = this.height - this.blockSize * 2
      
      while (blockIndex < allBlocks.length) {
        let x = this.x + this.blockSize + (blockIndex % 5) * (this.blockSize + 10)
        let y = this.y + this.blockSize + Math.floor((blockIndex - blockIndex % 5) / 5) * (this.blockSize + 10)
        // 限制在游戏板范围内
        x = Math.max(this.x, Math.min(x, this.x + this.width - this.blockSize))
        y = Math.max(this.y, Math.min(y, this.y + this.height - this.blockSize))
        const type = allBlocks[blockIndex]
        const block = this.createBlock(type, x, y, 0)  // 放在底层
        this.blocks.push(block)
        logger.info(`强制放置方块 ${blockIndex}: 类型${type}, 位置(${x.toFixed(1)}, ${y.toFixed(1)})`)
        blockIndex++
      }
    }
  }

  /**
   * 在中心区域强制创建剩余方块 - 改进版本
   * @param {Array} allBlocks - 所有方块类型数组
   * @param {number} blockIndex - 开始索引
   */
  forceCreateRemainingBlocksInCenter(allBlocks, blockIndex) {
    if (blockIndex >= allBlocks.length) return
    
    const remainingBlocks = allBlocks.length - blockIndex
    const centerX = this.x + this.width / 2
    const centerY = this.y + this.height / 2
    const safeMargin = 20
    const maxRadius = Math.min(
      (this.width - 2 * safeMargin - this.blockSize) / 2,
      (this.height - 2 * safeMargin - this.blockSize) / 2
    )
    
    // 螺旋分布剩余方块
    for (let i = blockIndex; i < allBlocks.length; i++) {
      const index = i - blockIndex
      const angle = (index / remainingBlocks) * Math.PI * 6  // 3圈螺旋
      const radius = (index / remainingBlocks) * maxRadius * 0.8
      
      let x = centerX + Math.cos(angle) * radius - this.blockSize / 2
      let y = centerY + Math.sin(angle) * radius - this.blockSize / 2
      
      // 确保在安全边界内
      x = Math.max(this.x + safeMargin, Math.min(x, this.x + this.width - this.blockSize - safeMargin))
      y = Math.max(this.y + safeMargin, Math.min(y, this.y + this.height - this.blockSize - safeMargin))
      
      const type = allBlocks[i]
      const block = this.createBlock(type, x, y, 0)
      this.blocks.push(block)
      
      logger.info(`  中心创建方块${i}: 类型${type}, 位置(${x.toFixed(1)}, ${y.toFixed(1)})`)
    }
  }

  /**
   * 生成第四关布局 - 星形布局
   */
  generateLevel4() {
    logger.info(`第四关布局: 星形分散`)
    
    const blockTypes = [0, 1, 2, 3, 4, 5]
    const blocksPerType = 9
    const totalBlocks = blockTypes.length * blocksPerType
    
    // 根据方块数量动态调整方块大小
    this.calculateDynamicBlockSize(4, totalBlocks)
    
    const allBlocks = []
    blockTypes.forEach(type => {
      for (let i = 0; i < blocksPerType; i++) {
        allBlocks.push(type)
      }
    })
    
    this.shuffleArray(allBlocks)
    
    const layouts = this.createStarLayouts()
    this.placeBlocksInLayoutsWithBounds(allBlocks, layouts)
    
    logger.info(`第四关共创建 ${this.blocks.length} 个方块`)
    this.validateBlockCounts()
  }

  /**
   * 生成第五关布局 - 新的分散层级布局
   */
  generateLevel5() {
    logger.info(`第五关布局: 新的分散层级布局`);
    logger.info(`游戏板尺寸: ${this.width}x${this.height}, 方块大小: ${this.blockSize}`);
    
    const blockTypes = [0, 1, 2, 3, 4, 5, 6];
    const blocksPerType = 9;
    const totalBlocks = blockTypes.length * blocksPerType;
    
    this.blockSize = this.baseBlockSize;
    logger.info(`🎯 第5关使用固定方块大小: ${this.blockSize}像素`);
    
    const allBlocks = [];
    blockTypes.forEach(type => {
      for (let i = 0; i < blocksPerType; i++) {
        allBlocks.push(type);
      }
    });
    
    this.shuffleArray(allBlocks);
    
    // 调用新的布局方法
    this.generateLevel5Layout(allBlocks);
    
    logger.info(`第五关共创建 ${this.blocks.length} 个方块`);
    this.validateBlockCounts();
  }

  /**
   * 生成第六关布局 - 花瓣布局
   */
  generateLevel6() {
    logger.info(`第六关布局: 花瓣分散`)
    
    const blockTypes = [0, 1, 2, 3, 4, 5, 6, 7]
    const blocksPerType = 9
    const totalBlocks = blockTypes.length * blocksPerType
    
    // 第6关保持固定方块大小
    this.blockSize = this.baseBlockSize
    logger.info(`🎯 第6关使用固定方块大小: ${this.blockSize}像素`)
    
    const allBlocks = []
    blockTypes.forEach(type => {
      for (let i = 0; i < blocksPerType; i++) {
        allBlocks.push(type)
      }
    })
    
    this.shuffleArray(allBlocks)
    
    const layouts = this.createPetalLayouts()
    this.placeBlocksInLayoutsWithBounds(allBlocks, layouts)
    
    logger.info(`第六关共创建 ${this.blocks.length} 个方块`)
    this.validateBlockCounts()
  }

  /**
   * 生成第七关布局 - 钻石布局
   */
  generateLevel7() {
    logger.info(`第七关布局: 钻石分散`)
    
    const blockTypes = [0, 1, 2, 3, 4, 5, 6, 7]
    const blocksPerType = 12
    const totalBlocks = blockTypes.length * blocksPerType
    
    // 第7关保持固定方块大小
    this.blockSize = this.baseBlockSize
    logger.info(`🎯 第7关使用固定方块大小: ${this.blockSize}像素`)
    
    const allBlocks = []
    blockTypes.forEach(type => {
      for (let i = 0; i < blocksPerType; i++) {
        allBlocks.push(type)
      }
    })
    
    this.shuffleArray(allBlocks)
    
    const layouts = this.createDiamondLayouts()
    this.placeBlocksInLayoutsWithBounds(allBlocks, layouts)
    
    logger.info(`第七关共创建 ${this.blocks.length} 个方块`)
    this.validateBlockCounts()
  }

  /**
   * 生成第八关布局 - 蜂巢布局
   */
  generateLevel8() {
    logger.info(`第八关布局: 蜂巢分散`)
    
    const blockTypes = [0, 1, 2, 3, 4, 5, 6, 7, 8]
    const blocksPerType = 12
    const totalBlocks = blockTypes.length * blocksPerType
    
    // 第8关保持固定方块大小
    this.blockSize = this.baseBlockSize
    logger.info(`🎯 第8关使用固定方块大小: ${this.blockSize}像素`)
    
    const allBlocks = []
    blockTypes.forEach(type => {
      for (let i = 0; i < blocksPerType; i++) {
        allBlocks.push(type)
      }
    })
    
    this.shuffleArray(allBlocks)
    
    const layouts = this.createHexagonLayouts()
    this.placeBlocksInLayoutsWithBounds(allBlocks, layouts)
    
    logger.info(`第八关共创建 ${this.blocks.length} 个方块`)
    this.validateBlockCounts()
  }

  /**
   * 生成第九关布局 - 迷宫布局
   */
  generateLevel9() {
    logger.info(`第九关布局: 迷宫分散`)
    
    const blockTypes = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    const blocksPerType = 12
    const totalBlocks = blockTypes.length * blocksPerType
    
    // 第9关保持固定方块大小
    this.blockSize = this.baseBlockSize
    logger.info(`🎯 第9关使用固定方块大小: ${this.blockSize}像素`)
    
    const allBlocks = []
    blockTypes.forEach(type => {
      for (let i = 0; i < blocksPerType; i++) {
        allBlocks.push(type)
      }
    })
    
    this.shuffleArray(allBlocks)
    
    const layouts = this.createMazeLayouts()
    this.placeBlocksInLayoutsWithBounds(allBlocks, layouts)
    
    logger.info(`第九关共创建 ${this.blocks.length} 个方块`)
    this.validateBlockCounts()
  }

  /**
   * 生成第十关布局 - 终极挑战
   */
  generateLevel10() {
    logger.info(`第十关布局: 终极挑战`)
    
    const blockTypes = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    const blocksPerType = 15
    const totalBlocks = blockTypes.length * blocksPerType
    
    // 第10关保持固定方块大小
    this.blockSize = this.baseBlockSize
    logger.info(`🎯 第10关使用固定方块大小: ${this.blockSize}像素`)
    
    const allBlocks = []
    blockTypes.forEach(type => {
      for (let i = 0; i < blocksPerType; i++) {
        allBlocks.push(type)
      }
    })
    
    this.shuffleArray(allBlocks)
    
    const layouts = this.createUltimateLayouts()
    this.placeBlocksInLayoutsWithBounds(allBlocks, layouts)
    
    logger.info(`第十关共创建 ${this.blocks.length} 个方块`)
    this.validateBlockCounts()
  }

  /**
   * 在布局中放置方块的通用方法
   * @param {Array} allBlocks - 所有方块类型数组
   * @param {Array} layouts - 布局数组
   */
  placeBlocksInLayouts(allBlocks, layouts) {
    let blockIndex = 0
    
    layouts.forEach((layout, layerIndex) => {
      layout.positions.forEach(pos => {
        if (blockIndex < allBlocks.length) {
          let x = this.x + pos.x - this.blockSize / 2
          let y = this.y + pos.y - this.blockSize / 2
          
          x = Math.max(this.x, Math.min(x, this.x + this.width - this.blockSize))
          y = Math.max(this.y, Math.min(y, this.y + this.height - this.blockSize))
          
          const type = allBlocks[blockIndex]
          const block = this.createBlock(type, x, y, layerIndex)
          this.blocks.push(block)
          blockIndex++
        }
      })
    })
    
    this.forceCreateRemainingBlocks(allBlocks, blockIndex)
  }

  /**
   * 在布局中放置方块的改进方法 - 增强边界检查
   * @param {Array} allBlocks - 所有方块类型数组
   * @param {Array} layouts - 布局数组
   */
  placeBlocksInLayoutsWithBounds(allBlocks, layouts) {
    let blockIndex = 0;
    const safeMargin = 10;  // 安全边距
    const validPositions = [];
    
    logger.info(`📦 placeBlocksInLayoutsWithBounds: 开始处理 ${layouts.length} 个布局层`);
    
    // 第一步：收集所有有效位置
    layouts.forEach((layout, layerIndex) => {
      logger.info(`  处理布局层 ${layerIndex}, 包含 ${layout.positions.length} 个原始位置`);
      layout.positions.forEach((pos, posIndex) => {
        let x = this.x + pos.x - this.blockSize / 2;
        let y = this.y + pos.y - this.blockSize / 2;
        
        // logger.info(`    原始计算位置 (pos ${posIndex}): (${x.toFixed(1)}, ${y.toFixed(1)}) from layoutPos (${pos.x.toFixed(1)}, ${pos.y.toFixed(1)})`);

        // 严格的边界检查
        const minX = this.x + safeMargin;
        const maxX = this.x + this.width - this.blockSize - safeMargin;
        const minY = this.y + safeMargin;
        const maxY = this.y + this.height - this.blockSize - safeMargin;
        
        // 如果超出边界，尝试调整到安全区域
        if (x < minX || x > maxX || y < minY || y > maxY) {
          const originalX = x;
          const originalY = y;
          // 重新计算位置，向中心收缩
          const centerX = this.x + this.width / 2;
          const centerY = this.y + this.height / 2;
          const ratio = Math.min(
            (this.width - this.blockSize - 2 * safeMargin) / (this.width - this.blockSize),
            (this.height - this.blockSize - 2 * safeMargin) / (this.height - this.blockSize)
          ) * 0.9;  // 再缩小10%确保安全
          
          x = centerX + (pos.x - this.width / 2) * ratio - this.blockSize / 2;
          y = centerY + (pos.y - this.height / 2) * ratio - this.blockSize / 2;
          
          // 最终边界夹紧
          x = Math.max(minX, Math.min(x, maxX));
          y = Math.max(minY, Math.min(y, maxY));
        }
        
        validPositions.push({ x, y, layer: layerIndex });
      });
    });
    
    logger.info(`🎯 生成了 ${validPositions.length} 个有效位置，需要放置 ${allBlocks.length} 个方块`);
    
    // 第二步：在有效位置放置方块
    validPositions.forEach((pos, index) => {
      if (blockIndex < allBlocks.length) {
        const type = allBlocks[blockIndex];
        const block = this.createBlock(type, pos.x, pos.y, pos.layer);
        this.blocks.push(block);
        
        // logger.info(`  方块${blockIndex}: 类型${type}, 位置(${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}), 层级${pos.layer}`);
        blockIndex++;
      }
    });
    
    // 第三步：如果还有剩余方块，在游戏板中心区域创建
    if (blockIndex < allBlocks.length) {
      logger.info(`⚠️ 还有 ${allBlocks.length - blockIndex} 个方块需要放置，将在中心区域创建`);
      this.forceCreateRemainingBlocksInCenter(allBlocks, blockIndex);
    }
    logger.info('📦 placeBlocksInLayoutsWithBounds: 处理完成');
  }

  /**
   * 创建星形布局
   */
  createStarLayouts() {
    const centerX = this.width / 2
    const centerY = this.height * 0.45; // 将中心点上移，避免底部遮挡
    const spacing = this.blockSize * 0.8
    
    // 计算最大安全半径，确保星形不会超出边界
    const safeMargin = 20
    const maxRadius = Math.min(
      (this.width - 2 * safeMargin - this.blockSize) / 2,
      (this.height - 2 * safeMargin - this.blockSize) / 2
    ) * 0.8  // 再缩小20%确保安全
    
    // 调整星形半径，确保不超出边界
    const radius1 = Math.min(spacing * 3, maxRadius)
    const radius2 = Math.min(spacing * 2, maxRadius * 0.7)
    const radius3 = Math.min(spacing, maxRadius * 0.4)
    
    logger.info(`🌟 星形布局: 中心(${centerX.toFixed(1)}, ${centerY.toFixed(1)}), 最大半径${maxRadius.toFixed(1)}, 实际半径[${radius1.toFixed(1)}, ${radius2.toFixed(1)}, ${radius3.toFixed(1)}]`)
    
    return [
      {
        positions: this.generateStarPositions(centerX, centerY, radius1, 8)
      },
      {
        positions: this.generateStarPositions(centerX, centerY, radius2, 6)
      },
      {
        positions: this.generateStarPositions(centerX, centerY, radius3, 4)
      }
    ]
  }

  /**
   * 创建螺旋布局
   */
  createSpiralLayouts() {
    const centerX = this.width / 2
    const centerY = this.height * 0.45; // 将中心点上移，避免底部遮挡
    const spacing = this.blockSize * 1.2  // 增大间距，让方块分布更开
    
    // 计算最大安全半径，确保螺旋不会超出边界
    const safeMargin = 30
    const maxRadius = Math.min(
      (this.width - 2 * safeMargin - this.blockSize) / 2,
      (this.height - 2 * safeMargin - this.blockSize) / 2
    ) * 0.9  // 再缩小10%确保安全
    
    logger.info(`🌀 螺旋布局: 中心(${centerX.toFixed(1)}, ${centerY.toFixed(1)}), 最大半径${maxRadius.toFixed(1)}, 间距${spacing.toFixed(1)}`)
    
    return [
      {
        // 外层螺旋：更大的半径范围，更多的方块
        positions: this.generateSpiralPositions(centerX, centerY, spacing, maxRadius, 25, 0)
      },
      {
        // 中层螺旋：中等半径，偏移角度避免重叠
        positions: this.generateSpiralPositions(centerX, centerY, spacing, maxRadius * 0.7, 20, Math.PI / 3)
      },
      {
        // 内层螺旋：较小半径，再次偏移角度
        positions: this.generateSpiralPositions(centerX, centerY, spacing, maxRadius * 0.4, 18, Math.PI / 6)
      }
    ]
  }

  /**
   * 创建花瓣布局
   */
  createPetalLayouts() {
    const centerX = this.width / 2
    const centerY = this.height * 0.45; // 将中心点上移，避免底部遮挡
    const spacing = this.blockSize * 1.2 // 增加了 spacing 的值

    return [
      {
        positions: this.generatePetalPositions(centerX, centerY, spacing * 3.0, 6) // 调整了半径
      },
      {
        positions: this.generatePetalPositions(centerX, centerY, spacing * 2.2, 5) // 调整了半径
      },
      {
        positions: this.generatePetalPositions(centerX, centerY, spacing * 1.5, 4) // 调整了半径
      }
    ]
  }

  /**
   * 创建钻石布局
   */
  createDiamondLayouts() {
    const centerX = this.width / 2
    const centerY = this.height * 0.45; // 将中心点上移，避免底部遮挡
    const spacing = this.blockSize * 0.9
    
    // 为第七关特别优化
    if (this.currentLevel === 7) {
      logger.info(`🎮 第七关钻石布局优化：需要容纳96个方块`)
      
      return [
        // 第0层（底层）：外层钻石 + 额外的散布点
        {
          positions: [
            ...this.generateDiamondPositions(centerX, centerY, spacing * 3.5, 2), // 外层钻石
            ...this.generateRandomPositions(centerX, centerY, spacing * 2, 12, 1.5) // 额外散布点
          ]
        },
        // 第1层（中层）：中层钻石 + 圆形分布
        {
          positions: [
            ...this.generateDiamondPositions(centerX, centerY, spacing * 2.5, 2), // 中层钻石
            ...this.generateCircularPositions(centerX, centerY, spacing * 2, 14) // 圆形分布
          ]
        },
        // 第2层（顶层）：内层钻石 + 中心区域
        {
          positions: [
            ...this.generateDiamondPositions(centerX, centerY, spacing * 1.5, 2), // 内层钻石
            ...this.generateCenterGridPositions(centerX, centerY, spacing * 0.6, 4, 4) // 中心网格
          ]
        }
      ]
    }
    
    // 其他关卡使用原来的布局
    return [
      {
        positions: this.generateDiamondPositions(centerX, centerY, spacing * 3, 4)
      },
      {
        positions: this.generateDiamondPositions(centerX, centerY, spacing * 2, 3)
      },
      {
        positions: this.generateDiamondPositions(centerX, centerY, spacing, 2)
      }
    ]
  }

  /**
   * 创建蜂巢布局
   */
  createHexagonLayouts() {
    const centerX = this.width / 2
    const centerY = this.height * 0.45; // 将中心点上移，避免底部遮挡
    const spacing = this.blockSize * 0.8
    
    return [
      {
        positions: this.generateHexagonPositions(centerX, centerY, spacing * 2.5, 3)
      },
      {
        positions: this.generateHexagonPositions(centerX, centerY, spacing * 1.8, 2)
      },
      {
        positions: this.generateHexagonPositions(centerX, centerY, spacing, 1)
      }
    ]
  }

  /**
   * 创建迷宫布局
   */
  createMazeLayouts() {
    const centerX = this.width / 2
    const centerY = this.height * 0.45; // 将中心点上移，避免底部遮挡
    const spacing = this.blockSize * 0.9
    
    return [
      {
        positions: this.generateMazePositions(centerX, centerY, spacing, 6, 5)
      },
      {
        positions: this.generateMazePositions(centerX, centerY, spacing, 4, 4)
      },
      {
        positions: this.generateMazePositions(centerX, centerY, spacing, 3, 3)
      }
    ]
  }

  /**
   * 创建终极布局
   */
  createUltimateLayouts() {
    const centerX = this.width / 2
    const centerY = this.height * 0.45; // 将中心点上移，避免底部遮挡
    const spacing = this.blockSize * 0.7
    
    return [
      {
        positions: this.generateComplexPattern(centerX, centerY, spacing, 'outer')
      },
      {
        positions: this.generateComplexPattern(centerX, centerY, spacing, 'middle')
      },
      {
        positions: this.generateComplexPattern(centerX, centerY, spacing, 'inner')
      }
    ]
  }

  /**
   * 生成星形位置
   */
  generateStarPositions(centerX, centerY, radius, points) {
    const positions = []
    for (let i = 0; i < points; i++) {
      const angle = (i / points) * Math.PI * 2
      const x = centerX + Math.cos(angle) * radius
      const y = centerY + Math.sin(angle) * radius
      positions.push({ x, y })
      
      // 添加内部点
      const innerAngle = angle + Math.PI / points
      const innerRadius = radius * 0.6
      const innerX = centerX + Math.cos(innerAngle) * innerRadius
      const innerY = centerY + Math.sin(innerAngle) * innerRadius
      positions.push({ x: innerX, y: innerY })
    }
    return positions
  }

  /**
   * 生成螺旋位置
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标
   * @param {number} spacing - 基础间距
   * @param {number} maxRadius - 最大半径
   * @param {number} count - 方块数量
   * @param {number} offset - 角度偏移
   */
  generateSpiralPositions(centerX, centerY, spacing, maxRadius, count, offset) {
    const positions = []
    const spiralTurns = 3  // 螺旋圈数
    
    for (let i = 0; i < count; i++) {
      // 增加螺旋圈数，让分布更广
      const angle = (i / count) * Math.PI * 2 * spiralTurns + offset
      // 使用最大半径参数，让螺旋能够充分利用空间
      const radius = (i / count) * maxRadius
      
      const x = centerX + Math.cos(angle) * radius
      const y = centerY + Math.sin(angle) * radius
      
      // 添加一些随机偏移，让布局更自然
      const randomOffset = spacing * 0.3
      const finalX = x + (Math.random() - 0.5) * randomOffset
      const finalY = y + (Math.random() - 0.5) * randomOffset
      
      positions.push({ x: finalX, y: finalY })
    }
    
    logger.info(`  → 生成螺旋: ${count}个位置, 最大半径${maxRadius.toFixed(1)}, 角度偏移${(offset * 180 / Math.PI).toFixed(1)}°`)
    return positions
  }

  /**
   * 生成花瓣位置
   */
  generatePetalPositions(centerX, centerY, radius, petals) {
    const positions = []
    for (let i = 0; i < petals; i++) {
      const angle = (i / petals) * Math.PI * 2
      for (let j = 0; j < 5; j++) {
        const r = radius * (0.3 + j * 0.2)
        const x = centerX + Math.cos(angle) * r
        const y = centerY + Math.sin(angle) * r
        positions.push({ x, y })
      }
    }
    return positions
  }

  /**
   * 生成钻石位置
   */
  generateDiamondPositions(centerX, centerY, size, layers) {
    const positions = []
    
    // 为第七关特别优化：确保生成足够的位置点
    if (this.currentLevel === 7) {
      // 第七关需要96个位置（8种类型×12个）
      // 使用多层同心钻石布局，每层都有足够的点
      for (let layer = 0; layer < layers; layer++) {
        const layerSize = size * (1 + layer * 0.8) // 逐层增大
        const basePoints = 8 + layer * 4 // 基础点数逐层增加：8, 12, 16
        
        // 主要的4个方向的点
        for (let i = 0; i < basePoints; i++) {
          const angle = (i / basePoints) * Math.PI * 2 + Math.PI / 4
          const x = centerX + Math.cos(angle) * layerSize
          const y = centerY + Math.sin(angle) * layerSize
          positions.push({ x, y })
        }
        
        // 在每层之间添加中间点，增加密度
        if (layer > 0) {
          const midLayerSize = size * (0.5 + layer * 0.8)
          const midPoints = 6 + layer * 2
          for (let i = 0; i < midPoints; i++) {
            const angle = (i / midPoints) * Math.PI * 2 + Math.PI / 8
            const x = centerX + Math.cos(angle) * midLayerSize
            const y = centerY + Math.sin(angle) * midLayerSize
            positions.push({ x, y })
          }
        }
      }
      
      // 如果位置还不够，在中心区域添加额外的点
      const currentCount = positions.length
      const neededCount = 96 // 第七关需要的总点数
      if (currentCount < neededCount) {
        logger.info(`🔧 第七关钻石布局：当前${currentCount}个位置，需要${neededCount}个，补充${neededCount - currentCount}个中心点`)
        
        // 在中心区域以格子状添加额外的点
        const gridSize = Math.ceil(Math.sqrt(neededCount - currentCount))
        const gridSpacing = size * 0.3
        const startX = centerX - (gridSize * gridSpacing) / 2
        const startY = centerY - (gridSize * gridSpacing) / 2
        
        for (let row = 0; row < gridSize; row++) {
          for (let col = 0; col < gridSize; col++) {
            if (positions.length >= neededCount) break
            const x = startX + col * gridSpacing
            const y = startY + row * gridSpacing
            positions.push({ x, y })
          }
          if (positions.length >= neededCount) break
        }
      }
      
      logger.info(`🎯 第七关钻石布局生成完成：${positions.length}个位置点`)
      return positions
    }
    
    // 其他关卡使用原来的简单钻石布局
    for (let layer = 0; layer < layers; layer++) {
      const layerSize = size * (layer + 1) / layers
      const points = (layer + 1) * 4
      
      for (let i = 0; i < points; i++) {
        const angle = (i / points) * Math.PI * 2 + Math.PI / 4
        const x = centerX + Math.cos(angle) * layerSize
        const y = centerY + Math.sin(angle) * layerSize
        positions.push({ x, y })
      }
    }
    return positions
  }

  /**
   * 生成蜂巢位置
   */
  generateHexagonPositions(centerX, centerY, radius, rings) {
    const positions = []
    
    // 中心点
    positions.push({ x: centerX, y: centerY })
    
    for (let ring = 1; ring <= rings; ring++) {
      const hexRadius = radius * ring
      for (let i = 0; i < 6; i++) {
        const angle = (i / 6) * Math.PI * 2
        const x = centerX + Math.cos(angle) * hexRadius
        const y = centerY + Math.sin(angle) * hexRadius
        positions.push({ x, y })
        
        // 在边上添加更多点
        if (ring > 1) {
          for (let j = 1; j < ring; j++) {
            const nextAngle = ((i + 1) / 6) * Math.PI * 2
            const t = j / ring
            const edgeX = x + (Math.cos(nextAngle) * hexRadius - x) * t
            const edgeY = y + (Math.sin(nextAngle) * hexRadius - y) * t
            positions.push({ x: edgeX, y: edgeY })
          }
        }
      }
    }
    
    return positions
  }

  /**
   * 生成迷宫位置
   */
  generateMazePositions(centerX, centerY, spacing, rows, cols) {
    const positions = []
    const startX = centerX - (cols * spacing) / 2
    const startY = centerY - (rows * spacing) / 2
    
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        // 创建迷宫样式的间隔
        if ((row + col) % 2 === 0 || Math.random() > 0.3) {
          const x = startX + col * spacing
          const y = startY + row * spacing
          positions.push({ x, y })
        }
      }
    }
    
    return positions
  }

  /**
   * 生成复杂图案
   */
  generateComplexPattern(centerX, centerY, spacing, type) {
    const positions = []
    
    // 计算最大半径用于螺旋
    const maxRadius = spacing * 4
    
    switch (type) {
      case 'outer':
        // 外层：混合星形和螺旋
        positions.push(...this.generateStarPositions(centerX, centerY, spacing * 3, 8))
        positions.push(...this.generateSpiralPositions(centerX, centerY, spacing, maxRadius, 15, 0))
        break
        
      case 'middle':
        // 中层：花瓣和蜂巢
        positions.push(...this.generatePetalPositions(centerX, centerY, spacing * 2, 6))
        positions.push(...this.generateHexagonPositions(centerX, centerY, spacing * 1.5, 2))
        break
        
      case 'inner':
        // 内层：钻石和迷宫
        positions.push(...this.generateDiamondPositions(centerX, centerY, spacing * 1.5, 3))
        positions.push(...this.generateMazePositions(centerX, centerY, spacing * 0.8, 3, 3))
        break
    }
    
    return positions
  }

  /**
   * 生成第五关布局 - 参考generateStrictOverlapLayout思路，但更分散
   * @param {Array} allBlocks - 所有方块类型数组
   */
  generateLevel5Layout(allBlocks) {
    logger.info('🚧 开始生成第5关自定义布局...');
    const centerX = this.width / 2;
    const centerY = this.height / 2;
    // 增大底层方块的间距，使其更分散
    const bottomSpacing = this.blockSize * 1.5; 
    const middleTopSpacing = this.blockSize * 1.1; // 中层和顶层使用稍小的间距

    let blockIndex = 0;
    const totalBlocksToPlace = allBlocks.length;

    const bottomCount = Math.floor(totalBlocksToPlace * 0.4);
    const middleCount = Math.floor(totalBlocksToPlace * 0.35);
    const topCount = totalBlocksToPlace - bottomCount - middleCount;

    logger.info(`  层级分配: 底层-${bottomCount}, 中层-${middleCount}, 顶层-${topCount}`);

    // --- 底层方块 (Layer 0) ---
    logger.info(`  生成底层 (${this.LAYER_BOTTOM}): ${bottomCount}个方块, 使用更大的间距: ${bottomSpacing.toFixed(1)}`);
    const bottomPositions = this.generateNonOverlapPositions(centerX, centerY, bottomSpacing, bottomCount);

    bottomPositions.forEach((pos) => {
      if (blockIndex < totalBlocksToPlace) {
        const type = allBlocks[blockIndex];
        const block = this.createBlock(type, pos.x, pos.y, this.LAYER_BOTTOM);
        this.blocks.push(block);
        blockIndex++;
      }
    });

    // --- 中层方块 (Layer 1) ---
    logger.info(`  生成中层 (${this.LAYER_MIDDLE}): ${middleCount}个方块, 间距: ${middleTopSpacing.toFixed(1)}`);
    const layer0Blocks = this.blocks.filter(b => b.layer === this.LAYER_BOTTOM);
    for (let i = 0; i < middleCount && blockIndex < totalBlocksToPlace; i++) {
      if (layer0Blocks.length === 0) break; 
      const baseBlock = layer0Blocks[i % layer0Blocks.length]; 
      
      const offsetX = (Math.random() - 0.5) * this.blockSize * 0.7; // 增大偏移范围
      const offsetY = (Math.random() - 0.5) * this.blockSize * 0.7; 
      let x = baseBlock.x + offsetX;
      let y = baseBlock.y + offsetY;

      x = Math.max(this.x + 5, Math.min(x, this.x + this.width - this.blockSize - 5));
      y = Math.max(this.y + 5, Math.min(y, this.y + this.height - this.blockSize - 5));

      const type = allBlocks[blockIndex];
      const block = this.createBlock(type, x, y, this.LAYER_MIDDLE);
      this.blocks.push(block);
      blockIndex++;
    }

    // --- 顶层方块 (Layer 2) ---
    logger.info(`  生成顶层 (${this.LAYER_TOP}): ${topCount}个方块, 间距: ${middleTopSpacing.toFixed(1)}`);
    const layer1And0Blocks = this.blocks.filter(b => b.layer === this.LAYER_MIDDLE || b.layer === this.LAYER_BOTTOM);
    for (let i = 0; i < topCount && blockIndex < totalBlocksToPlace; i++) {
      if (layer1And0Blocks.length === 0) break; 
      const targetBlock = layer1And0Blocks[i % layer1And0Blocks.length]; 
      
      const offsetX = (Math.random() - 0.5) * this.blockSize * 0.5; // 调整偏移
      const offsetY = (Math.random() - 0.5) * this.blockSize * 0.5;
      let x = targetBlock.x + offsetX;
      let y = targetBlock.y + offsetY;

      x = Math.max(this.x + 5, Math.min(x, this.x + this.width - this.blockSize - 5));
      y = Math.max(this.y + 5, Math.min(y, this.y + this.height - this.blockSize - 5));

      const type = allBlocks[blockIndex];
      const block = this.createBlock(type, x, y, this.LAYER_TOP);
      this.blocks.push(block);
      blockIndex++;
    }
    
    if (blockIndex < totalBlocksToPlace) {
        logger.warn(`⚠️ 第5关布局后还有 ${totalBlocksToPlace - blockIndex} 个方块未放置，将强制放置`);
        this.forceCreateRemainingBlocksInCenter(allBlocks, blockIndex);
    }

    logger.info('🚧 第5关自定义布局生成完成.');
  }

  /**
   * 生成圆形分布的位置点
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标  
   * @param {number} radius - 半径
   * @param {number} count - 点的数量
   * @returns {Array} 位置点数组
   */
  generateCircularPositions(centerX, centerY, radius, count) {
    const positions = []
    for (let i = 0; i < count; i++) {
      const angle = (i / count) * Math.PI * 2
      const x = centerX + Math.cos(angle) * radius
      const y = centerY + Math.sin(angle) * radius
      positions.push({ x, y })
    }
    return positions
  }

  /**
   * 生成中心网格位置点
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标
   * @param {number} spacing - 网格间距
   * @param {number} rows - 行数
   * @param {number} cols - 列数
   * @returns {Array} 位置点数组
   */
  generateCenterGridPositions(centerX, centerY, spacing, rows, cols) {
    const positions = []
    const startX = centerX - (cols * spacing) / 2
    const startY = centerY - (rows * spacing) / 2
    
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const x = startX + col * spacing
        const y = startY + row * spacing
        positions.push({ x, y })
      }
    }
    return positions
  }

  /**
   * 生成宽间距圆形分布位置（第11关及以后优化）
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标
   * @param {number} radius - 半径
   * @param {number} count - 点的数量
   * @returns {Array} 位置点数组
   */
  generateWideSpacedCircularPositions(centerX, centerY, radius, count) {
    const positions = []
    const minAngleStep = (Math.PI * 2) / Math.max(count, 8) // 确保最小角度间隔
    
    for (let i = 0; i < count; i++) {
      const angle = i * minAngleStep + (Math.random() - 0.5) * 0.3 // 加入小随机偏移
      const actualRadius = radius * (0.85 + Math.random() * 0.3) // 半径随机化，避免完美圆形
      const x = centerX + Math.cos(angle) * actualRadius
      const y = centerY + Math.sin(angle) * actualRadius
      positions.push({ x, y })
    }
    return positions
  }

  /**
   * 生成宽间距随机分布位置（第11关及以后优化）
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标
   * @param {number} minDistance - 最小间距
   * @param {number} count - 点的数量
   * @returns {Array} 位置点数组
   */
  generateWideSpacedRandomPositions(centerX, centerY, minDistance, count) {
    const positions = []
    const maxAttempts = count * 10 // 最大尝试次数
    
    for (let i = 0; i < count && positions.length < count; i++) {
      let placed = false
      
      for (let attempt = 0; attempt < maxAttempts / count; attempt++) {
        // 在适中范围内生成随机位置
        const angle = Math.random() * Math.PI * 2
        const radius = minDistance * (0.5 + Math.random() * 1.5)
        const x = centerX + Math.cos(angle) * radius
        const y = centerY + Math.sin(angle) * radius
        
        // 检查与已有位置的距离
        let tooClose = false
        for (const pos of positions) {
          const dist = Math.sqrt((x - pos.x) ** 2 + (y - pos.y) ** 2)
          if (dist < minDistance) {
            tooClose = true
            break
          }
        }
        
        if (!tooClose) {
          positions.push({ x, y })
          placed = true
          break
        }
      }
      
      if (!placed) {
        // 如果无法找到合适位置，放宽条件
        const angle = Math.random() * Math.PI * 2
        const radius = minDistance * (0.3 + Math.random() * 2.0)
        const x = centerX + Math.cos(angle) * radius
        const y = centerY + Math.sin(angle) * radius
        positions.push({ x, y })
      }
    }
    
    return positions
  }

  /**
   * 生成边角位置（第11关及以后优化）
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标
   * @param {number} spacing - 间距
   * @param {number} count - 点的数量
   * @returns {Array} 位置点数组
   */
  generateCornerPositions(centerX, centerY, spacing, count) {
    const positions = []
    const cornerRegions = [
      { x: centerX - spacing * 2, y: centerY - spacing * 2 }, // 左上
      { x: centerX + spacing * 2, y: centerY - spacing * 2 }, // 右上
      { x: centerX - spacing * 2, y: centerY + spacing * 2 }, // 左下
      { x: centerX + spacing * 2, y: centerY + spacing * 2 }  // 右下
    ]
    
    const pointsPerCorner = Math.ceil(count / 4)
    
    cornerRegions.forEach((corner, cornerIndex) => {
      for (let i = 0; i < pointsPerCorner && positions.length < count; i++) {
        const offsetX = (Math.random() - 0.5) * spacing * 1.5
        const offsetY = (Math.random() - 0.5) * spacing * 1.5
        
        positions.push({
          x: corner.x + offsetX,
          y: corner.y + offsetY
        })
      }
    })
    
    return positions
  }
}

// CommonJS导出
module.exports = Board 