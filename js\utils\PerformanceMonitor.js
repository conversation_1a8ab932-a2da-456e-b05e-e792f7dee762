const logger = require('./AsyncLogger')

/**
 * 性能监控器
 * 监控游戏性能，包括FPS、内存使用、日志性能等
 */
class PerformanceMonitor {
  constructor() {
    // 初始化性能测量工具
    this.initPerformanceUtils()

    // FPS监控
    this.fps = 0
    this.frameCount = 0
    this.lastTime = this.getTime()
    this.fpsHistory = []
    this.maxFpsHistory = 60 // 保留最近60秒的FPS数据
    
    // 内存监控
    this.memoryUsage = {
      used: 0,
      total: 0,
      percentage: 0
    }
    
    // 日志性能监控
    this.logPerformance = {
      totalLogs: 0,
      avgLogTime: 0,
      maxLogTime: 0,
      logTimeHistory: []
    }
    
    // 游戏性能指标
    this.gameMetrics = {
      renderTime: 0,
      updateTime: 0,
      totalFrameTime: 0
    }
    
    // 监控配置
    this.isEnabled = false
    this.updateInterval = 1000 // 1秒更新一次统计
    this.lastUpdateTime = 0
    
    // 性能警告阈值
    this.thresholds = {
      lowFps: 30,        // FPS低于30时警告
      highMemory: 80,    // 内存使用超过80%时警告
      slowLog: 5,        // 单次日志耗时超过5ms时警告
      slowFrame: 16.67   // 单帧耗时超过16.67ms时警告
    }
    
    // 警告回调
    this.onWarning = null
  }

  /**
   * 初始化性能测量工具
   * 提供跨平台的时间测量功能
   */
  initPerformanceUtils() {
    // 检查performance对象是否可用
    if (typeof performance !== 'undefined' && performance.now) {
      this.getTime = () => performance.now()
      this.hasPerformanceAPI = true
    } else {
      // 降级方案：使用Date.now()
      this.getTime = () => Date.now()
      this.hasPerformanceAPI = false
      logger.warn('[PerformanceMonitor] performance对象不可用，使用Date.now()作为降级方案')
    }

    // 检查requestAnimationFrame是否可用
    if (typeof requestAnimationFrame !== 'undefined') {
      this.scheduleFrame = (callback) => requestAnimationFrame(callback)
    } else {
      // 降级方案：使用setTimeout模拟60fps
      this.scheduleFrame = (callback) => setTimeout(callback, 16.67)
      logger.warn('[PerformanceMonitor] requestAnimationFrame不可用，使用setTimeout作为降级方案')
    }
  }

  /**
   * 启用性能监控
   * @param {Function} warningCallback - 性能警告回调函数
   */
  enable(warningCallback = null) {
    this.isEnabled = true
    this.onWarning = warningCallback
    this.startMonitoring()
    logger.info('📊 性能监控已启用')
  }
  
  /**
   * 禁用性能监控
   */
  disable() {
    this.isEnabled = false
    logger.info('📊 性能监控已禁用')
  }
  
  /**
   * 开始监控
   */
  startMonitoring() {
    if (!this.isEnabled) return
    
    // 开始FPS监控
    this.monitorFPS()
    
    // 定期更新统计信息
    setInterval(() => {
      if (this.isEnabled) {
        this.updateStats()
      }
    }, this.updateInterval)
  }
  
  /**
   * FPS监控
   */
  monitorFPS() {
    const now = this.getTime()
    this.frameCount++

    // 每秒计算一次FPS
    if (now - this.lastTime >= 1000) {
      this.fps = Math.round((this.frameCount * 1000) / (now - this.lastTime))
      this.frameCount = 0
      this.lastTime = now
      
      // 记录FPS历史
      this.fpsHistory.push({
        time: now,
        fps: this.fps
      })
      
      // 限制历史记录长度
      if (this.fpsHistory.length > this.maxFpsHistory) {
        this.fpsHistory.shift()
      }
      
      // 检查FPS警告
      if (this.fps < this.thresholds.lowFps) {
        this.triggerWarning('LOW_FPS', `FPS过低: ${this.fps}`)
      }
    }
    
    // 继续监控
    if (this.isEnabled) {
      this.scheduleFrame(() => this.monitorFPS())
    }
  }
  
  /**
   * 记录帧性能
   * @param {number} renderTime - 渲染时间
   * @param {number} updateTime - 更新时间
   */
  recordFramePerformance(renderTime, updateTime) {
    if (!this.isEnabled) return
    
    this.gameMetrics.renderTime = renderTime
    this.gameMetrics.updateTime = updateTime
    this.gameMetrics.totalFrameTime = renderTime + updateTime
    
    // 检查帧时间警告
    if (this.gameMetrics.totalFrameTime > this.thresholds.slowFrame) {
      this.triggerWarning('SLOW_FRAME', `帧时间过长: ${this.gameMetrics.totalFrameTime.toFixed(2)}ms`)
    }
  }
  
  /**
   * 记录日志性能
   * @param {number} logTime - 日志耗时
   */
  recordLogPerformance(logTime) {
    if (!this.isEnabled) return
    
    this.logPerformance.totalLogs++
    this.logPerformance.logTimeHistory.push(logTime)
    
    // 计算平均日志时间
    const totalTime = this.logPerformance.logTimeHistory.reduce((sum, time) => sum + time, 0)
    this.logPerformance.avgLogTime = totalTime / this.logPerformance.logTimeHistory.length
    
    // 更新最大日志时间
    this.logPerformance.maxLogTime = Math.max(this.logPerformance.maxLogTime, logTime)
    
    // 限制历史记录长度
    if (this.logPerformance.logTimeHistory.length > 1000) {
      this.logPerformance.logTimeHistory.shift()
    }
    
    // 检查日志性能警告
    if (logTime > this.thresholds.slowLog) {
      this.triggerWarning('SLOW_LOG', `日志耗时过长: ${logTime.toFixed(2)}ms`)
    }
  }
  
  /**
   * 更新统计信息
   */
  updateStats() {
    // 更新内存使用情况
    this.updateMemoryUsage()
  }
  
  /**
   * 更新内存使用情况
   */
  updateMemoryUsage() {
    if (this.hasPerformanceAPI && typeof performance !== 'undefined' && performance.memory) {
      const memory = performance.memory
      this.memoryUsage.used = Math.round(memory.usedJSHeapSize / 1024 / 1024) // MB
      this.memoryUsage.total = Math.round(memory.totalJSHeapSize / 1024 / 1024) // MB
      this.memoryUsage.percentage = Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100)

      // 检查内存警告
      if (this.memoryUsage.percentage > this.thresholds.highMemory) {
        this.triggerWarning('HIGH_MEMORY', `内存使用过高: ${this.memoryUsage.percentage}%`)
      }
    } else {
      // 降级方案：设置默认值或跳过内存监控
      this.memoryUsage.used = 0
      this.memoryUsage.total = 0
      this.memoryUsage.percentage = 0
    }
  }
  
  /**
   * 触发性能警告
   * @param {string} type - 警告类型
   * @param {string} message - 警告消息
   */
  triggerWarning(type, message) {
    const warning = {
      type: type,
      message: message,
      timestamp: Date.now()
    }
    
    logger.warn(`⚠️ 性能警告 [${type}]: ${message}`)
    
    if (this.onWarning && typeof this.onWarning === 'function') {
      this.onWarning(warning)
    }
  }
  
  /**
   * 获取性能报告
   * @returns {Object}
   */
  getPerformanceReport() {
    const avgFps = this.fpsHistory.length > 0 
      ? Math.round(this.fpsHistory.reduce((sum, item) => sum + item.fps, 0) / this.fpsHistory.length)
      : 0
    
    const minFps = this.fpsHistory.length > 0 
      ? Math.min(...this.fpsHistory.map(item => item.fps))
      : 0
    
    return {
      // FPS信息
      fps: {
        current: this.fps,
        average: avgFps,
        minimum: minFps,
        history: this.fpsHistory.slice(-10) // 最近10秒
      },
      
      // 内存信息
      memory: {
        ...this.memoryUsage
      },
      
      // 日志性能
      logging: {
        ...this.logPerformance,
        avgLogTime: Math.round(this.logPerformance.avgLogTime * 100) / 100,
        maxLogTime: Math.round(this.logPerformance.maxLogTime * 100) / 100
      },
      
      // 游戏性能
      game: {
        ...this.gameMetrics,
        renderTime: Math.round(this.gameMetrics.renderTime * 100) / 100,
        updateTime: Math.round(this.gameMetrics.updateTime * 100) / 100,
        totalFrameTime: Math.round(this.gameMetrics.totalFrameTime * 100) / 100
      },
      
      // 监控状态
      monitoring: {
        enabled: this.isEnabled,
        uptime: Date.now() - this.lastTime
      }
    }
  }
  
  /**
   * 打印性能报告
   */
  printReport() {
    const report = this.getPerformanceReport()
    
    logger.info('📊 性能监控报告:')
    logger.info(`   FPS: 当前${report.fps.current} | 平均${report.fps.average} | 最低${report.fps.minimum}`)
    logger.info(`   内存: ${report.memory.used}MB / ${report.memory.total}MB (${report.memory.percentage}%)`)
    logger.info(`   日志: 总计${report.logging.totalLogs}条 | 平均${report.logging.avgLogTime}ms | 最大${report.logging.maxLogTime}ms`)
    logger.info(`   帧时间: 渲染${report.game.renderTime}ms | 更新${report.game.updateTime}ms | 总计${report.game.totalFrameTime}ms`)
  }
  
  /**
   * 重置统计数据
   */
  reset() {
    this.fpsHistory = []
    this.logPerformance = {
      totalLogs: 0,
      avgLogTime: 0,
      maxLogTime: 0,
      logTimeHistory: []
    }
    logger.info('📊 性能统计数据已重置')
  }
}

// 创建全局性能监控实例
const performanceMonitor = new PerformanceMonitor()

// CommonJS导出
module.exports = performanceMonitor
