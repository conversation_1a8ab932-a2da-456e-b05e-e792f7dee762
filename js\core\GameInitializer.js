const ImageGenerator = require('../utils/ImageGenerator.js')
const CartoonImageLoader = require('../utils/CartoonImageLoader.js')
const Board = require('../game/Board.js')
const Slot = require('../game/Slot.js')
const Button = require('../ui/Button.js')
const ParticleSystem = require('../effects/ParticleSystem.js')
const WinScreen = require('../ui/WinScreen.js')
const CollapsiblePanel = require('../ui/CollapsiblePanel.js')
const SettingsButton = require('../ui/SettingsButton.js')
const SettingsPanel = require('../ui/SettingsPanel.js')
const logger = require('../utils/AsyncLogger')

/**
 * 游戏初始化管理器
 * 负责游戏的各种初始化操作，包括Canvas设置、资源加载、游戏对象初始化等
 */
class GameInitializer {
  constructor(main) {
    this.main = main
  }

  /**
   * 设置Canvas
   */
  setupCanvas() {
    this.main.canvas = wx.createCanvas()
    this.main.ctx = this.main.canvas.getContext('2d')
    
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync()
    const pixelRatio = systemInfo.pixelRatio
    const screenWidth = systemInfo.screenWidth
    const screenHeight = systemInfo.screenHeight
    
    // 设置Canvas物理尺寸（用于实际渲染）
    this.main.canvas.width = screenWidth * pixelRatio
    this.main.canvas.height = screenHeight * pixelRatio
    
    // 设置Canvas逻辑尺寸（用于坐标计算）
    this.main.canvas.style = {
      width: screenWidth + 'px',
      height: screenHeight + 'px'
    }
    
    // 缩放Canvas以适应设备像素比
    this.main.ctx.scale(pixelRatio, pixelRatio)
    
    // 更新canvasSize对象
    this.main.canvasSize = {
      width: this.main.canvas.width,
      height: this.main.canvas.height,
      pixelRatio: pixelRatio,
      screenWidth: screenWidth,
      screenHeight: screenHeight
    }
    
    const DEBUG_MODE = false // 从main.js获取DEBUG_MODE
    if (DEBUG_MODE) {
      logger.info(`Canvas设置完成: 物理尺寸${this.main.canvas.width}x${this.main.canvas.height}, 逻辑尺寸${screenWidth}x${screenHeight}, 像素比${pixelRatio}`)
    }
  }

  /**
   * 加载游戏资源
   */
  async loadResources() {
    const DEBUG_MODE = false // 从main.js获取DEBUG_MODE
    if (DEBUG_MODE) logger.info('开始加载资源...')
    
    try {
      // 加载卡通动物图片
      if (DEBUG_MODE) logger.info('🎨 正在加载卡通动物图片...')
      const cartoonImages = await CartoonImageLoader.loadCartoonImages()
      
      // 检查加载结果
      const loadedCount = Object.keys(cartoonImages).length
      if (loadedCount === 0) {
        throw new Error('没有成功加载任何图片文件，请检查images/cartoon_animals/目录中的图片文件')
      }
      
      if (DEBUG_MODE) logger.info(`🎉 成功加载 ${loadedCount}/8 张卡通动物图片`)
      
      // 生成背景图片（简单的池塘背景）
      const bgCanvas = ImageGenerator.createCanvas(400, 600)
      const bgCtx = bgCanvas.getContext('2d')
      
      // 绘制池塘背景
      const gradient = bgCtx.createLinearGradient(0, 0, 0, 600)
      gradient.addColorStop(0, '#87CEEB')  // 天蓝色
      gradient.addColorStop(0.6, '#87CEEB')
      gradient.addColorStop(0.6, '#20B2AA')  // 海蓝色
      gradient.addColorStop(1, '#008B8B')   // 深青色
      
      bgCtx.fillStyle = gradient
      bgCtx.fillRect(0, 0, 400, 600)
      
      // 添加一些云朵装饰
      bgCtx.fillStyle = 'rgba(255, 255, 255, 0.8)'
      for (let i = 0; i < 5; i++) {
        const x = Math.random() * 350
        const y = Math.random() * 150
        bgCtx.beginPath()
        bgCtx.ellipse(x, y, 30 + Math.random() * 20, 15 + Math.random() * 10, 0, 0, 2 * Math.PI)
        bgCtx.fill()
      }
      
      // 使用Map的set方法正确添加资源
      this.main.resources.images.set('background', bgCanvas)
      
      // 使用从后端API加载的动物图片 - 支持最多14种
      const baseImageMapping = []
      for (let i = 1; i <= 14; i++) {
        baseImageMapping.push({
          key: `animal${i}`,
          blockIndex: i - 1,
          name: `动物${i}`
        })
      }
      
      // 处理基础映射（0-13，最多14种图片）
      let successCount = 0
      const loadedImages = []
      baseImageMapping.forEach(mapping => {
        if (cartoonImages[mapping.key]) {
          logger.info(`✅ 使用API图片: ${mapping.name}`)
          const resizedImage = this.resizeImageToCanvas(cartoonImages[mapping.key], 48)
          this.main.resources.images.set(`block_${mapping.blockIndex}`, resizedImage)
          loadedImages.push({ image: resizedImage, key: mapping.key, blockIndex: mapping.blockIndex })
          successCount++
        } else {
          logger.warn(`❌ 图片缺失: ${mapping.name} (${mapping.key})`)
        }
      })
      
      // 扩展映射：为类型14-20创建循环映射（支持第20关等高难度关卡）
      if (loadedImages.length > 0) {
        logger.info(`🔄 开始创建扩展图片映射，支持方块类型${loadedImages.length}-20...`)
        for (let blockType = loadedImages.length; blockType <= 20; blockType++) {
          // 循环使用已加载的图片
          const sourceIndex = blockType % loadedImages.length
          const sourceImage = loadedImages[sourceIndex]
          
          // 复制图片到新的方块类型
          this.main.resources.images.set(`block_${blockType}`, sourceImage.image)
          logger.info(`🔄 方块类型${blockType} -> 复用${sourceImage.key} (原block_${sourceImage.blockIndex})`)
        }
        
        logger.info(`🎯 扩展映射完成：支持方块类型0-20`)
      }
      
      logger.info(`🎮 游戏图片准备完成：${successCount}/14 张基础图片`)
      logger.info(`🎮 实际加载了：${successCount} 种不同的动物图片`)
      logger.info(`🎮 总计支持方块类型：0-20 (21种类型，循环使用${successCount}种图片)`)
      
      // 调试：输出所有已加载的图片资源
      logger.info(`📋 已加载的图片资源列表:`)
      for (let i = 0; i <= 20; i++) {
        const hasImage = this.main.resources.images.has(`block_${i}`)
        logger.info(`  block_${i}: ${hasImage ? '✅' : '❌'}`)
      }
      
      logger.info('资源加载完成！')
    } catch (error) {
      logger.error('资源加载失败:', error)
      throw error
    }
  }

  /**
   * 将图片调整到指定尺寸的Canvas
   * @param {HTMLImageElement} img - 原始图片
   * @param {number} size - 目标尺寸
   * @returns {HTMLCanvasElement}
   */
  resizeImageToCanvas(img, size) {
    const canvas = ImageGenerator.createCanvas(size, size)
    const ctx = canvas.getContext('2d')
    
    // 清空画布
    ctx.clearRect(0, 0, size, size)
    
    // 绘制调整尺寸后的图片
    ctx.drawImage(img, 0, 0, size, size)
    
    return canvas
  }

  /**
   * 初始化用户系统
   */
  async initUserSystem() {
    logger.info('👤 初始化用户系统...')
    try {
      // 初始化用户管理器
      await this.main.userManager.init()
      
      // 将用户管理器关联到存档管理器
      this.main.saveManager.setUserManager(this.main.userManager)
      
      logger.info('👤 用户系统初始化完成')
    } catch (error) {
      logger.error('❌ 用户系统初始化失败:', error)
      // 即使用户系统失败，游戏仍然可以运行（使用默认存档）
    }
  }

  /**
   * 加载游戏进度
   */
  loadGameProgress() {
    logger.info('📱 加载游戏进度...')
    try {
      const saveData = this.main.saveManager.getSaveData()
      logger.info('📱 存档数据:', saveData)

      // 设置当前关卡为已解锁的关卡
      this.main.currentLevel = saveData.currentLevel

      // 设置全局变量用于调试（Block.js会用到）
      if (typeof window !== 'undefined') {
        window.currentLevel = this.main.currentLevel
      }

      // 记录是否首次游戏
      this.main.isFirstPlay = saveData.firstPlay

      logger.info(`📱 游戏进度加载完成: 当前关卡 ${this.main.currentLevel}, 首次游戏: ${this.main.isFirstPlay}`)
    } catch (error) {
      logger.error('❌ 加载游戏进度失败:', error)
      // 使用默认值
      this.main.currentLevel = 1
      this.main.isFirstPlay = true
    }
  }

  /**
   * 初始化游戏对象
   */
  initGameObjects() {
    const screenWidth = this.main.canvasSize.screenWidth
    const screenHeight = this.main.canvasSize.screenHeight

    logger.info(`屏幕尺寸: ${screenWidth}x${screenHeight}`)

    // 初始化游戏板
    const boardWidth = screenWidth - 40
    const boardHeight = screenHeight * 0.45  // 稍微减小游戏板高度，为更多UI元素留出空间
    const boardX = 20
    const boardY = 150  // 从80增加到130，确保与扩大的标签栏(100px)有足够间距

    logger.info(`游戏板布局: 位置(${boardX}, ${boardY}), 尺寸${boardWidth}x${boardHeight}`)

    this.main.board = new Board(boardX, boardY, boardWidth, boardHeight)

    // 初始化槽位 - 美化版本，稍微增大
    const slotWidth = screenWidth - 30  // 减少左右边距，让槽位更宽
    const slotHeight = 80  // 增加槽位高度，让方块更大更美观
    const slotX = 15  // 减少左边距
    const slotY = screenHeight - slotHeight - 75 // 为道具按钮留出空间，稍微调整位置

    logger.info(`槽位布局: 位置(${slotX}, ${slotY}), 尺寸${slotWidth}x${slotHeight}`)

    this.main.slot = new Slot(slotX, slotY, slotWidth, slotHeight)

    // 设置槽位消除回调
    this.main.slot.setEliminationCallback((type, count) => {
      this.main.onBlockEliminated(type, count)
    })

    // 设置槽位消除完成回调
    this.main.slot.setEliminationCompleteCallback(() => {
      this.main.onEliminationComplete()
    })

    // 设置槽位动画管理器
    this.main.slot.setAnimationManager(this.main.animationManager)

    // 设置槽位音频管理器
    this.main.slot.setAudioManager(this.main.audioManager)

    // 初始化道具按钮
    this.initPowerUpButtons()

    // 初始化文件信息面板
    this.initFileInfoPanel()

    // 初始化设置按钮和设置面板
    this.initSettingsUI()

    // 初始化分享对话框
    this.initShareDialog()

    // 初始化粒子系统和胜利页面
    this.main.particleSystem = new ParticleSystem()
    this.main.winScreen = new WinScreen(this.main.canvasSize, this.main.animationManager, this.main.particleSystem)

    logger.info('游戏对象初始化完成')
  }

  /**
   * 初始化道具按钮
   */
  initPowerUpButtons() {
    const screenWidth = this.main.canvasSize.screenWidth
    const screenHeight = this.main.canvasSize.screenHeight
    const buttonWidth = 80
    const buttonHeight = 40
    const buttonSpacing = 10
    // 只有两个按钮，重新计算居中位置
    const startX = (screenWidth - (buttonWidth * 2 + buttonSpacing)) / 2
    const buttonY = screenHeight - 60

    // 撤回按钮
    const undoButton = new Button(
      startX, buttonY, buttonWidth, buttonHeight,
      `撤回(${this.main.powerUps.undo})`,
      () => this.main.usePowerUp('undo')
    )
    undoButton.setTheme('blue')
    this.main.powerUpButtons.push(undoButton)

    // 洗牌按钮
    const shuffleButton = new Button(
      startX + buttonWidth + buttonSpacing, buttonY, buttonWidth, buttonHeight,
      `洗牌(${this.main.powerUps.shuffle})`,
      () => this.main.usePowerUp('shuffle')
    )
    shuffleButton.setTheme('orange')
    this.main.powerUpButtons.push(shuffleButton)
  }

  /**
   * 初始化文件信息面板
   * 用于显示游戏调试信息和文件状态
   */
  initFileInfoPanel() {
    const screenWidth = this.main.canvasSize.screenWidth
    const panelWidth = Math.min(300, screenWidth - 40)
    const panelX = 20
    const panelY = 80

    // 创建文件信息面板
    this.main.fileInfoPanel = new CollapsiblePanel(
      panelX, panelY, panelWidth,
      '游戏信息', // 面板标题
      [], // 初始内容为空
      true // 初始状态为折叠
    )

    // 设置面板主题
    this.main.fileInfoPanel.setTheme('info')

    // 更新面板内容
    this.main.updateFileInfoPanel()

    logger.info('文件信息面板初始化完成')
  }

  /**
   * 初始化设置UI组件
   */
  initSettingsUI() {
    const screenWidth = this.main.canvasSize.screenWidth
    const screenHeight = this.main.canvasSize.screenHeight

    // 创建设置按钮（左上角，避免与步数显示重叠）
    const buttonSize = 35  // 保持35px尺寸
    this.main.settingsButton = new SettingsButton(
      25, // 左边距15px
      56, // 顶部边距80px，位于步数显示下方，避免重叠
      buttonSize,
      () => this.main.showSettingsPanel()
    )

    // 创建设置面板（居中）
    const panelWidth = Math.min(300, screenWidth - 80)
    const panelHeight = 520 // 增加到520，确保所有按钮都能完整显示
    const panelX = (screenWidth - panelWidth) / 2
    const panelY = (screenHeight - panelHeight) / 2

    this.main.settingsPanel = new SettingsPanel(
      panelX, panelY, panelWidth, panelHeight,
      {
        onShow: () => this.main.audioManager.playSound('click'),
        onHide: () => this.main.audioManager.playSound('click'),
        onSoundToggle: (enabled) => this.main.toggleSound(enabled),
        onMusicToggle: (enabled) => this.main.toggleMusic(enabled),
        onRestartLevel: () => this.main.restartCurrentLevel(),
        onBackToLevels: () => this.main.backToLevelSelect(),
        onFeedbackSuccess: () => this.main.showMessage('感谢您的反馈！我们会认真对待您的建议。', 3000),
        onFeedbackError: (error) => this.main.showMessage(error, 3000),
        onFeedbackInputFocus: (currentText, callback) => this.main.handleFeedbackInput(currentText, callback),
        getUserInfo: () => this.main.userManager.getUserInfo()
      }
    )

    // 同步当前音频设置状态
    this.main.settingsPanel.setSoundEnabled(this.main.audioManager.getSoundEnabled())
    this.main.settingsPanel.setMusicEnabled(this.main.audioManager.getMusicEnabled())

    logger.info('设置UI组件初始化完成')
  }

  /**
   * 初始化分享对话框
   */
  initShareDialog() {
    const screenWidth = this.main.canvasSize.screenWidth
    const screenHeight = this.main.canvasSize.screenHeight

    // 创建分享对话框（居中）
    const dialogWidth = Math.min(300, screenWidth - 80)
    const dialogHeight = 250
    const dialogX = (screenWidth - dialogWidth) / 2
    const dialogY = (screenHeight - dialogHeight) / 2

    // 初始化为null，需要时动态创建
    this.main.shareDialog = null

    logger.info('分享对话框配置初始化完成')
  }
}

module.exports = GameInitializer
