/**
 * 设置按钮类
 * 齿轮形状的设置按钮，用于打开设置面板
 */
class SettingsButton {
  /**
   * 构造函数
   * @param {number} x - 按钮X坐标
   * @param {number} y - 按钮Y坐标
   * @param {number} size - 按钮大小
   * @param {function} onClick - 点击回调函数
   */
  constructor(x, y, size, onClick) {
    this.x = x
    this.y = y
    this.size = size
    this.onClick = onClick
    
    // 按钮状态
    this.isPressed = false
    this.isVisible = true
    this.isEnabled = true
    this.isHovered = false
    
    // 现代化样式配置
    this.backgroundColor = 'rgba(255, 152, 0, 0.95)'  // 橘色背景，对应#FF9800
    this.backgroundHoverColor = 'rgba(245, 124, 0, 1)'  // 悬停时的橘色，对应#F57C00
    this.backgroundPressedColor = 'rgba(230, 81, 0, 0.95)'  // 按下时的橘色，对应#E65100
    this.shadowColor = 'rgba(0, 0, 0, 0.15)'
    this.shadowBlur = 8
    this.shadowOffsetX = 0
    this.shadowOffsetY = 2
    this.borderRadius = this.size / 2
    
    // 齿轮颜色配置 - 改为橘色系
    this.gearBaseColor = '#FF9800'        // 主橘色
    this.gearLightColor = '#FFB74D'       // 亮橘色
    this.gearDarkColor = '#F57C00'        // 暗橘色
    this.gearAccentColor = '#FFE0B2'      // 浅橘色强调
    
    // 动画属性
    this.scale = 1
    this.targetScale = 1
    this.rotation = 0
    this.rotationSpeed = 0
    this.targetRotationSpeed = 0
    this.hoverScale = 1
    this.targetHoverScale = 1
    
    // 悬停检测属性
    this.lastTouchTime = 0
    this.hoverEffect = false
  }

  /**
   * 检查点是否在按钮内
   * @param {number} x - 点的X坐标
   * @param {number} y - 点的Y坐标
   * @returns {boolean}
   */
  containsPoint(x, y) {
    const centerX = this.x + this.size / 2
    const centerY = this.y + this.size / 2
    const dx = x - centerX
    const dy = y - centerY
    const distance = Math.sqrt(dx * dx + dy * dy)
    return distance <= this.size / 2
  }

  /**
   * 处理点击事件
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {boolean} 是否处理了点击
   */
  handleClick(x, y) {
    if (!this.isEnabled || !this.isVisible) {
      return false
    }

    if (this.containsPoint(x, y)) {
      this.playClickAnimation()
      if (this.onClick) {
        this.onClick()
      }
      return true
    }
    return false
  }

  /**
   * 播放点击动画
   */
  playClickAnimation() {
    this.targetScale = 0.85
    this.targetRotationSpeed = 6 // 点击时短暂旋转
    
    setTimeout(() => {
      this.targetScale = 1
      this.targetRotationSpeed = 0 // 旋转后停止
    }, 300)
  }

  /**
   * 设置按钮可见性
   * @param {boolean} visible - 是否可见
   */
  setVisible(visible) {
    this.isVisible = visible
  }

  /**
   * 设置按钮启用状态
   * @param {boolean} enabled - 是否启用
   */
  setEnabled(enabled) {
    this.isEnabled = enabled
  }

  /**
   * 设置按钮位置
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  setPosition(x, y) {
    this.x = x
    this.y = y
  }

  /**
   * 设置悬停状态
   * @param {boolean} hovered - 是否悬停
   */
  setHovered(hovered) {
    this.isHovered = hovered
    this.targetHoverScale = hovered ? 1.1 : 1
  }

  /**
   * 更新按钮状态
   * @param {number} deltaTime - 时间差（毫秒）
   */
  update(deltaTime) {
    // 更新缩放动画
    const scaleDiff = this.targetScale - this.scale
    if (Math.abs(scaleDiff) > 0.01) {
      this.scale += scaleDiff * 0.25
    } else {
      this.scale = this.targetScale
    }

    // 更新悬停缩放动画
    const hoverScaleDiff = this.targetHoverScale - this.hoverScale
    if (Math.abs(hoverScaleDiff) > 0.01) {
      this.hoverScale += hoverScaleDiff * 0.15
    } else {
      this.hoverScale = this.targetHoverScale
    }

    // 更新旋转动画
    const speedDiff = this.targetRotationSpeed - this.rotationSpeed
    if (Math.abs(speedDiff) > 0.1) {
      this.rotationSpeed += speedDiff * 0.2
    } else {
      this.rotationSpeed = this.targetRotationSpeed
    }

    // 应用旋转（只有在有旋转速度时才旋转，去掉自动旋转）
    if (this.rotationSpeed > 0) {
      this.rotation += this.rotationSpeed * deltaTime * 0.001
    }
  }

  /**
   * 渲染按钮
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    if (!this.isVisible) return

    ctx.save()

    const centerX = this.x + this.size / 2
    const centerY = this.y + this.size / 2

    // 应用悬停缩放
    ctx.translate(centerX, centerY)
    ctx.scale(this.hoverScale, this.hoverScale)
    ctx.translate(-centerX, -centerY)

    // 应用点击缩放
    ctx.translate(centerX, centerY)
    ctx.scale(this.scale, this.scale)
    ctx.translate(-centerX, -centerY)

    // 只绘制齿轮图标，不绘制背景和阴影
    this.drawModernGear(ctx, centerX, centerY, this.size * 0.4) // 稍微增大齿轮尺寸

    ctx.restore()
  }

  /**
   * 绘制现代化齿轮图标
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标
   * @param {number} radius - 齿轮半径
   */
  drawModernGear(ctx, centerX, centerY, radius) {
    ctx.save()
    
    // 应用旋转
    ctx.translate(centerX, centerY)
    ctx.rotate(this.rotation)
    ctx.translate(-centerX, -centerY)

    // 齿轮参数
    const teethCount = 8 // 齿数
    const innerRadius = radius * 0.5 // 内圆半径
    const outerRadius = radius // 外圆半径
    const teethHeight = radius * 0.25 // 齿高
    const centerHoleRadius = innerRadius * 0.6 // 中心孔半径

    // 创建齿轮渐变
    const gearGradient = ctx.createRadialGradient(
      centerX, centerY, 0,
      centerX, centerY, outerRadius + teethHeight
    )
    gearGradient.addColorStop(0, this.gearLightColor)
    gearGradient.addColorStop(0.6, this.gearBaseColor)
    gearGradient.addColorStop(1, this.gearDarkColor)

    // 绘制齿轮外轮廓（更平滑的齿形）
    ctx.beginPath()
    
    for (let i = 0; i < teethCount * 4; i++) {
      const angle = (i * Math.PI) / (teethCount * 2)
      let currentRadius
      
      // 创建更平滑的齿形
      const toothPhase = (i % 4) / 4
      if (toothPhase < 0.2) {
        currentRadius = outerRadius + teethHeight * Math.sin(toothPhase * Math.PI / 0.2)
      } else if (toothPhase < 0.8) {
        currentRadius = outerRadius + teethHeight
      } else {
        currentRadius = outerRadius + teethHeight * Math.sin((1 - toothPhase) * Math.PI / 0.2)
      }
      
      const x = centerX + Math.cos(angle) * currentRadius
      const y = centerY + Math.sin(angle) * currentRadius
      
      if (i === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    }
    
    ctx.closePath()
    ctx.fillStyle = gearGradient
    ctx.fill()

    // 添加齿轮高光
    ctx.strokeStyle = this.gearLightColor
    ctx.lineWidth = 1
    ctx.stroke()

    // 绘制内圆环
    const innerGradient = ctx.createRadialGradient(
      centerX, centerY, centerHoleRadius,
      centerX, centerY, innerRadius
    )
    innerGradient.addColorStop(0, this.gearDarkColor)
    innerGradient.addColorStop(0.5, this.gearBaseColor)
    innerGradient.addColorStop(1, this.gearLightColor)

    ctx.beginPath()
    ctx.arc(centerX, centerY, innerRadius, 0, Math.PI * 2)
    ctx.arc(centerX, centerY, centerHoleRadius, 0, Math.PI * 2, true)
    ctx.fillStyle = innerGradient
    ctx.fill()

    // 绘制装饰螺丝钉
    this.drawDecorativeScrews(ctx, centerX, centerY, innerRadius * 0.8)

    // 绘制中心高光点
    const highlightGradient = ctx.createRadialGradient(
      centerX - 2, centerY - 2, 0,
      centerX, centerY, centerHoleRadius
    )
    highlightGradient.addColorStop(0, this.gearAccentColor)
    highlightGradient.addColorStop(0.3, this.gearLightColor)
    highlightGradient.addColorStop(1, 'transparent')

    ctx.beginPath()
    ctx.arc(centerX, centerY, centerHoleRadius, 0, Math.PI * 2)
    ctx.fillStyle = highlightGradient
    ctx.fill()

    ctx.restore()
  }

  /**
   * 绘制装饰螺丝钉
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标
   * @param {number} radius - 螺丝钉分布半径
   */
  drawDecorativeScrews(ctx, centerX, centerY, radius) {
    const screwCount = 4
    const screwSize = 2

    for (let i = 0; i < screwCount; i++) {
      const angle = (i * Math.PI * 2) / screwCount + Math.PI / 4
      const x = centerX + Math.cos(angle) * radius
      const y = centerY + Math.sin(angle) * radius

      // 绘制螺丝钉
      ctx.beginPath()
      ctx.arc(x, y, screwSize, 0, Math.PI * 2)
      ctx.fillStyle = this.gearDarkColor
      ctx.fill()

      // 绘制螺丝钉十字槽
      ctx.strokeStyle = this.gearLightColor
      ctx.lineWidth = 1
      ctx.beginPath()
      ctx.moveTo(x - screwSize * 0.6, y)
      ctx.lineTo(x + screwSize * 0.6, y)
      ctx.moveTo(x, y - screwSize * 0.6)
      ctx.lineTo(x, y + screwSize * 0.6)
      ctx.stroke()
    }
  }

  /**
   * 调整颜色透明度
   * @param {string} color - 颜色字符串
   * @param {number} alpha - 透明度
   * @returns {string} 调整后的颜色
   */
  adjustColorAlpha(color, alpha) {
    if (color.includes('rgba')) {
      return color.replace(/[\d\.]+\)$/g, alpha + ')')
    } else if (color.includes('rgb')) {
      return color.replace('rgb', 'rgba').replace(')', `, ${alpha})`)
    }
    return color
  }

  /**
   * 获取按钮边界
   * @returns {Object} 边界信息
   */
  getBounds() {
    return {
      x: this.x,
      y: this.y,
      width: this.size,
      height: this.size
    }
  }
}

module.exports = SettingsButton 