const logger = require('./AsyncLogger.js')

/**
 * 资源加载器
 * 用于预加载游戏所需的图片和音频资源
 */
class ResourceLoader {
  constructor() {
    this.images = new Map()
    this.audios = new Map()
    this.loadPromises = []
  }

  /**
   * 加载图片资源（带重试机制）
   * @param {string} name - 资源名称
   * @param {string} src - 图片路径
   * @param {number} maxRetries - 最大重试次数，默认3次
   * @returns {Promise}
   */
  loadImage(name, src, maxRetries = 3) {
    return new Promise(async (resolve, reject) => {
      let lastError = null
      
      for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
        try {
          const image = wx.createImage()
          
          if (attempt === 1) {
            logger.info(`🖼️ 开始加载图片资源: ${name} -> ${src}`)
            logger.info(`🔄 最大重试次数: ${maxRetries}`)
          } else {
            logger.info(`🔄 第${attempt-1}次重试加载图片资源: ${name}`)
          }
          
          const result = await new Promise((imgResolve, imgReject) => {
            // 设置超时
            const timeout = setTimeout(() => {
              logger.info(`⏰ 图片加载超时: ${name} (尝试${attempt}/${maxRetries + 1})`)
              imgReject(new Error(`图片加载超时: ${name}`))
            }, 8000) // 8秒超时
            
            image.onload = () => {
              clearTimeout(timeout)
              this.images.set(name, image)
              const successMsg = attempt === 1 
                ? `✅ 图片加载成功: ${name}`
                : `✅ 图片重试第${attempt-1}次成功: ${name}`
              logger.info(successMsg)
              imgResolve(image)
            }
            
            image.onerror = (error) => {
              clearTimeout(timeout)
              const errorMsg = attempt === 1
                ? `❌ 图片加载失败: ${name}`
                : `❌ 图片重试第${attempt-1}次失败: ${name}`
              logger.error(errorMsg, error)
              imgReject(new Error(`图片加载失败: ${name}`))
            }
            
            image.src = src
          })
          
          // 加载成功，返回结果
          resolve(result)
          return
          
        } catch (error) {
          lastError = error
          
          if (attempt <= maxRetries) {
            // 计算重试延迟（指数退避：1秒、2秒、4秒）
            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000)
            logger.warn(`⏳ ${delay}ms后进行第${attempt}次重试加载图片资源: ${name}`)
            
            // 等待重试延迟
            await new Promise(delayResolve => setTimeout(delayResolve, delay))
          } else {
            // 所有重试都失败了
            logger.error(`💥 图片资源加载失败，已达到最大重试次数(${maxRetries}): ${name}`)
            reject(lastError)
            return
          }
        }
      }
    })
  }

  /**
   * 加载音频资源
   * @param {string} name - 资源名称
   * @param {string} src - 音频路径
   * @returns {Promise}
   */
  loadAudio(name, src) {
    return new Promise((resolve, reject) => {
      const audio = wx.createInnerAudioContext()
      
      audio.src = src
      audio.onCanplay(() => {
        this.audios.set(name, audio)
        logger.info(`音频加载成功: ${name}`)
        resolve(audio)
      })
      
      audio.onError((error) => {
        logger.error(`音频加载失败: ${name}`, error)
        reject(error)
      })
    })
  }

  /**
   * 批量加载图片资源
   * @param {Object} imageList - 图片列表 {name: src}
   * @returns {Promise}
   */
  loadImages(imageList) {
    const promises = Object.entries(imageList).map(([name, src]) => {
      return this.loadImage(name, src)
    })
    
    this.loadPromises.push(...promises)
    return Promise.all(promises)
  }

  /**
   * 批量加载音频资源
   * @param {Object} audioList - 音频列表 {name: src}
   * @returns {Promise}
   */
  loadAudios(audioList) {
    const promises = Object.entries(audioList).map(([name, src]) => {
      return this.loadAudio(name, src)
    })
    
    this.loadPromises.push(...promises)
    return Promise.all(promises)
  }

  /**
   * 等待所有资源加载完成
   * @returns {Promise}
   */
  waitForAll() {
    return Promise.all(this.loadPromises)
  }

  /**
   * 获取图片资源
   * @param {string} name - 资源名称
   * @returns {Image|null}
   */
  getImage(name) {
    return this.images.get(name) || null
  }

  /**
   * 获取音频资源
   * @param {string} name - 资源名称
   * @returns {InnerAudioContext|null}
   */
  getAudio(name) {
    return this.audios.get(name) || null
  }

  /**
   * 检查资源是否加载完成
   * @param {string} name - 资源名称
   * @returns {boolean}
   */
  isLoaded(name) {
    return this.images.has(name) || this.audios.has(name)
  }

  /**
   * 获取加载进度
   * @returns {number} 0-1之间的进度值
   */
  getProgress() {
    if (this.loadPromises.length === 0) return 1
    
    const loaded = this.images.size + this.audios.size
    return loaded / this.loadPromises.length
  }

  /**
   * 清理资源
   */
  dispose() {
    // 清理音频资源
    this.audios.forEach(audio => {
      audio.destroy()
    })
    
    this.images.clear()
    this.audios.clear()
    this.loadPromises = []
  }
}

// CommonJS导出
module.exports = ResourceLoader 