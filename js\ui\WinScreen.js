/**
 * 胜利页面组件
 * 负责渲染美化的关卡完成页面和管理庆祝动画
 */
class WinScreen {
  constructor(canvasSize, animationManager, particleSystem) {
    this.canvasSize = canvasSize
    this.animationManager = animationManager
    this.particleSystem = particleSystem
    
    // 动画状态
    this.isVisible = false
    this.animationStartTime = 0
    this.animationDuration = 3000 // 3秒动画
    this.canSkip = false
    this.skipDelay = 1000 // 1秒后可以跳过
    
    // UI元素状态
    this.titleScale = 0
    this.titleAlpha = 0
    this.statsAlpha = 0
    this.buttonAlpha = 0
    this.backgroundAlpha = 0
    
    // 统计信息动画
    this.animatedStats = {
      time: 0,
      score: 0,
      moves: 0
    }
    this.targetStats = {
      time: 0,
      score: 0,
      moves: 0
    }
    
    // 按钮状态
    this.buttonScale = 1
    this.buttonHover = false
    this.buttonPulse = 0
    
    // 颜色配置
    this.colors = {
      background: {
        start: 'rgba(255, 215, 0, 0.1)',
        middle: 'rgba(255, 105, 180, 0.15)',
        end: 'rgba(138, 43, 226, 0.2)'
      },
      title: {
        primary: '#FFD700',
        secondary: '#FFA500',
        glow: '#FFFF00'
      },
      stats: {
        background: 'rgba(255, 255, 255, 0.9)',
        border: '#FFD700',
        text: '#333333',
        icon: '#FF6B6B'
      },
      button: {
        background: '#4CAF50',
        hover: '#45a049',
        text: '#FFFFFF'
      }
    }
  }

  /**
   * 显示胜利页面
   * @param {Object} stats - 游戏统计信息 {time, score, moves}
   */
  show(stats) {
    this.isVisible = true
    this.animationStartTime = Date.now()
    this.canSkip = false
    
    // 设置目标统计数据
    this.targetStats = { ...stats }
    this.animatedStats = { time: 0, score: 0, moves: 0 }
    
    // 重置动画状态
    this.titleScale = 0
    this.titleAlpha = 0
    this.statsAlpha = 0
    this.buttonAlpha = 0
    this.backgroundAlpha = 0
    
    // 启动庆祝效果
    this.startCelebrationEffects()
    
    // 设置跳过延迟
    setTimeout(() => {
      this.canSkip = true
    }, this.skipDelay)
  }

  /**
   * 隐藏胜利页面
   */
  hide() {
    this.isVisible = false
    this.particleSystem.clear()
  }

  /**
   * 启动庆祝效果
   */
  startCelebrationEffects() {
    const centerX = this.canvasSize.screenWidth / 2
    const centerY = this.canvasSize.screenHeight / 2
    
    // 创建烟花效果
    setTimeout(() => {
      this.particleSystem.createFirework(centerX - 100, centerY - 100, 25)
    }, 200)
    
    setTimeout(() => {
      this.particleSystem.createFirework(centerX + 100, centerY - 80, 25)
    }, 600)
    
    setTimeout(() => {
      this.particleSystem.createFirework(centerX, centerY - 120, 30)
    }, 1000)
    
    // 创建彩带效果
    setTimeout(() => {
      this.particleSystem.createConfetti(this.canvasSize.screenWidth, 30)
    }, 300)
    
    // 创建星星效果
    setTimeout(() => {
      this.particleSystem.createStars(this.canvasSize.screenWidth, this.canvasSize.screenHeight, 20)
    }, 800)
    
    // 创建光环效果
    setTimeout(() => {
      this.particleSystem.createRipple(centerX, centerY)
    }, 100)
  }

  /**
   * 更新动画状态
   * @param {number} deltaTime - 时间差
   */
  update(deltaTime) {
    if (!this.isVisible) return
    
    const elapsed = Date.now() - this.animationStartTime
    const progress = Math.min(elapsed / this.animationDuration, 1)
    
    // 更新背景透明度
    this.backgroundAlpha = this.easeOutCubic(Math.min(progress * 2, 1))
    
    // 更新标题动画（0.2秒后开始）
    if (elapsed > 200) {
      const titleProgress = Math.min((elapsed - 200) / 800, 1)
      this.titleScale = this.easeOutBounce(titleProgress)
      this.titleAlpha = this.easeOutCubic(titleProgress)
    }
    
    // 更新统计信息动画（1秒后开始）
    if (elapsed > 1000) {
      const statsProgress = Math.min((elapsed - 1000) / 1000, 1)
      this.statsAlpha = this.easeOutCubic(statsProgress)
      
      // 数字递增动画
      this.animatedStats.time = Math.floor(this.targetStats.time * this.easeOutCubic(statsProgress))
      this.animatedStats.score = Math.floor(this.targetStats.score * this.easeOutCubic(statsProgress))
      this.animatedStats.moves = Math.floor(this.targetStats.moves * this.easeOutCubic(statsProgress))
    }
    
    // 更新按钮动画（2秒后开始）
    if (elapsed > 2000) {
      const buttonProgress = Math.min((elapsed - 2000) / 500, 1)
      this.buttonAlpha = this.easeOutCubic(buttonProgress)
    }
    
    // 按钮脉冲效果
    this.buttonPulse = Math.sin(Date.now() * 0.003) * 0.1 + 1
    
    // 更新粒子系统
    this.particleSystem.update(deltaTime)
  }

  /**
   * 渲染胜利页面
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    if (!this.isVisible) return
    
    ctx.save()
    
    // 渲染背景
    this.renderBackground(ctx)
    
    // 渲染粒子效果
    this.particleSystem.render(ctx)
    
    // 渲染标题
    this.renderTitle(ctx)
    
    // 渲染统计信息
    this.renderStats(ctx)
    
    // 渲染按钮
    this.renderButton(ctx)
    
    // 渲染跳过提示
    this.renderSkipHint(ctx)
    
    ctx.restore()
  }

  /**
   * 渲染背景
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderBackground(ctx) {
    ctx.save()
    ctx.globalAlpha = this.backgroundAlpha
    
    // 渐变背景
    const gradient = ctx.createLinearGradient(0, 0, 0, this.canvasSize.screenHeight)
    gradient.addColorStop(0, this.colors.background.start)
    gradient.addColorStop(0.5, this.colors.background.middle)
    gradient.addColorStop(1, this.colors.background.end)
    
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, this.canvasSize.screenWidth, this.canvasSize.screenHeight)
    
    ctx.restore()
  }

  /**
   * 渲染标题
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderTitle(ctx) {
    if (this.titleAlpha <= 0) return
    
    ctx.save()
    ctx.globalAlpha = this.titleAlpha
    
    const centerX = this.canvasSize.screenWidth / 2
    const centerY = this.canvasSize.screenHeight / 2 - 100
    
    // 应用缩放
    ctx.translate(centerX, centerY)
    ctx.scale(this.titleScale, this.titleScale)
    ctx.translate(-centerX, -centerY)
    
    // 发光效果
    ctx.shadowColor = this.colors.title.glow
    ctx.shadowBlur = 20
    ctx.shadowOffsetX = 0
    ctx.shadowOffsetY = 0
    
    // 渐变文字
    const titleGradient = ctx.createLinearGradient(centerX - 150, centerY - 30, centerX + 150, centerY + 30)
    titleGradient.addColorStop(0, this.colors.title.primary)
    titleGradient.addColorStop(0.5, this.colors.title.secondary)
    titleGradient.addColorStop(1, this.colors.title.primary)
    
    ctx.fillStyle = titleGradient
    ctx.strokeStyle = '#8B4513'
    ctx.lineWidth = 4
    ctx.font = 'bold 48px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    
    ctx.strokeText('🎉 关卡完成！', centerX, centerY)
    ctx.fillText('🎉 关卡完成！', centerX, centerY)
    
    ctx.restore()
  }

  /**
   * 渲染统计信息
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderStats(ctx) {
    if (this.statsAlpha <= 0) return
    
    ctx.save()
    ctx.globalAlpha = this.statsAlpha
    
    const centerX = this.canvasSize.screenWidth / 2
    const centerY = this.canvasSize.screenHeight / 2
    
    // 统计卡片
    const cardWidth = 280
    const cardHeight = 120
    const cardX = centerX - cardWidth / 2
    const cardY = centerY - cardHeight / 2
    
    // 卡片背景
    ctx.fillStyle = this.colors.stats.background
    ctx.strokeStyle = this.colors.stats.border
    ctx.lineWidth = 3
    this.drawRoundedRect(ctx, cardX, cardY, cardWidth, cardHeight, 15)
    ctx.fill()
    ctx.stroke()
    
    // 统计信息
    ctx.fillStyle = this.colors.stats.text
    ctx.font = 'bold 18px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    
    const lineHeight = 25
    const startY = centerY - 30
    
    ctx.fillText(`⏱️ 用时: ${this.animatedStats.time}秒`, centerX, startY)
    ctx.fillText(`💎 总分: ${this.animatedStats.score}`, centerX, startY + lineHeight)
    ctx.fillText(`🎯 移动次数: ${this.animatedStats.moves}`, centerX, startY + lineHeight * 2)
    
    ctx.restore()
  }

  /**
   * 渲染按钮
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderButton(ctx) {
    if (this.buttonAlpha <= 0) return
    
    ctx.save()
    ctx.globalAlpha = this.buttonAlpha
    
    const centerX = this.canvasSize.screenWidth / 2
    const centerY = this.canvasSize.screenHeight / 2 + 120
    
    const buttonWidth = 200
    const buttonHeight = 50
    const buttonX = centerX - buttonWidth / 2
    const buttonY = centerY - buttonHeight / 2
    
    // 应用脉冲效果
    ctx.translate(centerX, centerY)
    ctx.scale(this.buttonPulse, this.buttonPulse)
    ctx.translate(-centerX, -centerY)
    
    // 按钮背景
    const buttonColor = this.buttonHover ? this.colors.button.hover : this.colors.button.background
    ctx.fillStyle = buttonColor
    ctx.strokeStyle = '#2e7d2e'
    ctx.lineWidth = 2
    this.drawRoundedRect(ctx, buttonX, buttonY, buttonWidth, buttonHeight, 25)
    ctx.fill()
    ctx.stroke()
    
    // 按钮文字
    ctx.fillStyle = this.colors.button.text
    ctx.font = 'bold 18px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText('🚀 继续下一关', centerX, centerY)
    
    ctx.restore()
  }

  /**
   * 渲染跳过提示
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderSkipHint(ctx) {
    if (!this.canSkip) return
    
    ctx.save()
    ctx.globalAlpha = 0.6
    ctx.fillStyle = '#666666'
    ctx.font = '14px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'bottom'
    ctx.fillText('点击任意位置跳过动画', this.canvasSize.screenWidth / 2, this.canvasSize.screenHeight - 20)
    ctx.restore()
  }

  /**
   * 绘制圆角矩形
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} radius - 圆角半径
   */
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
  }

  // 缓动函数
  easeOutCubic(t) {
    return 1 - Math.pow(1 - t, 3)
  }

  easeOutBounce(t) {
    const n1 = 7.5625
    const d1 = 2.75
    if (t < 1 / d1) {
      return n1 * t * t
    } else if (t < 2 / d1) {
      return n1 * (t -= 1.5 / d1) * t + 0.75
    } else if (t < 2.5 / d1) {
      return n1 * (t -= 2.25 / d1) * t + 0.9375
    } else {
      return n1 * (t -= 2.625 / d1) * t + 0.984375
    }
  }

  /**
   * 检查点击是否在按钮上
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {boolean} 是否点击在按钮上
   */
  isButtonClicked(x, y) {
    if (this.buttonAlpha <= 0) return false
    
    const centerX = this.canvasSize.screenWidth / 2
    const centerY = this.canvasSize.screenHeight / 2 + 120
    const buttonWidth = 200
    const buttonHeight = 50
    
    return x >= centerX - buttonWidth / 2 && 
           x <= centerX + buttonWidth / 2 && 
           y >= centerY - buttonHeight / 2 && 
           y <= centerY + buttonHeight / 2
  }

  /**
   * 设置按钮悬停状态
   * @param {boolean} hover - 是否悬停
   */
  setButtonHover(hover) {
    this.buttonHover = hover
  }

  /**
   * 跳过动画
   */
  skipAnimation() {
    if (!this.canSkip) return
    
    // 立即完成所有动画
    this.titleScale = 1
    this.titleAlpha = 1
    this.statsAlpha = 1
    this.buttonAlpha = 1
    this.backgroundAlpha = 1
    
    // 完成统计数字动画
    this.animatedStats = { ...this.targetStats }
    
    // 清除粒子效果
    this.particleSystem.clear()
  }
}

// CommonJS导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WinScreen
}
